#!/bin/bash

# 開發環境啟動腳本
echo "🚀 啟動 HappyOrder 藥房系統開發環境..."

# 檢查 Docker 是否運行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未運行，請先啟動 Docker"
    exit 1
fi

# 停止現有的容器
echo "🛑 停止現有容器..."
docker-compose down

# 啟動資料庫
echo "🗄️  啟動 PostgreSQL 資料庫..."
docker-compose up -d db

# 等待資料庫準備就緒
echo "⏳ 等待資料庫準備就緒..."
sleep 10

# 檢查資料庫連接
echo "🔍 檢查資料庫連接..."
docker-compose exec db pg_isready -U postgres -d pharmacy_db

if [ $? -eq 0 ]; then
    echo "✅ 資料庫已準備就緒"
else
    echo "❌ 資料庫連接失敗"
    exit 1
fi

# 設置環境變數
export DATABASE_URL="postgresql://postgres:password@localhost:5432/pharmacy_db"
export JWT_SECRET="development-jwt-secret-key"
export PORT=8080

echo "🔧 環境變數已設置："
echo "   DATABASE_URL: $DATABASE_URL"
echo "   JWT_SECRET: $JWT_SECRET"
echo "   PORT: $PORT"

# 編譯並運行應用程式
echo "🔨 編譯並運行應用程式..."
cargo run --bin pharmacy-system

echo "🎉 開發環境啟動完成！"
echo "📱 應用程式運行在: http://localhost:8080"
echo "🗄️  資料庫運行在: localhost:5432" 