#!/bin/bash

echo "🚀 快速測試藥品採購系統"
echo "======================"

BASE_URL="http://localhost:8080"

# 檢查伺服器
echo "檢查伺服器狀態..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo "❌ 伺服器未運行，請先執行: ./dev.sh"
    exit 1
fi
echo "✅ 伺服器正常運行"

# 顯示可用端點
echo -e "\n📋 可用的 API 端點:"
curl -s "$BASE_URL/" | jq -r '.endpoints | to_entries[] | "  \(.key): \(.value)"'

echo -e "\n🔗 重要連結:"
echo "  🌐 Web 介面: $BASE_URL/"
echo "  📡 API 資訊: $BASE_URL/api"
echo "  ❤️  健康檢查: $BASE_URL/health"
echo "  🔍 詳細健康檢查: $BASE_URL/health/detailed"

echo -e "\n📝 API 使用範例:"
echo "1. 註冊使用者:"
echo "   curl -X POST $BASE_URL/api/auth/register \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"username\":\"myuser\",\"email\":\"<EMAIL>\",\"password\":\"mypass\",\"pharmacy_name\":\"我的藥局\",\"phone\":\"**********\"}'"

echo -e "\n2. 登入取得 token:"
echo "   curl -X POST $BASE_URL/api/auth/login \\"
echo "     -H 'Content-Type: application/json' \\"
echo "     -d '{\"username\":\"myuser\",\"password\":\"mypass\"}'"

echo -e "\n3. 使用 token 存取 API:"
echo "   TOKEN=\"your_token_here\""
echo "   curl -H \"Authorization: Bearer \$TOKEN\" $BASE_URL/api/products"

echo -e "\n💡 提示: 執行 ./test_api.sh 進行完整的 API 測試"