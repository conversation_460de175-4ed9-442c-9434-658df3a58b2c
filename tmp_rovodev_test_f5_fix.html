<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>F5 重新整理測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔄 F5 重新整理修復測試</h1>
    
    <div class="test-section">
        <h2>📋 測試說明</h2>
        <p>這個頁面用來測試 F5 重新整理後是否還會被登出的問題。</p>
        <ol>
            <li>先在主應用程式中登入</li>
            <li>回到這個測試頁面</li>
            <li>檢查 localStorage 中的認證資料</li>
            <li>模擬重新整理流程</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔍 當前認證狀態</h2>
        <div id="auth-status"></div>
        <button onclick="checkAuthStatus()">🔄 檢查認證狀態</button>
        <button onclick="clearAuthData()">🗑️ 清除認證資料</button>
    </div>

    <div class="test-section">
        <h2>🧪 模擬測試</h2>
        <button onclick="simulateF5Refresh()">🔄 模擬 F5 重新整理</button>
        <button onclick="testTokenValidation()">🔐 測試 Token 驗證</button>
        <button onclick="testOfflineMode()">📱 測試離線模式</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 localStorage 內容</h2>
        <button onclick="showLocalStorage()">👁️ 顯示 localStorage</button>
        <div id="localStorage-content"></div>
    </div>

    <script>
        const API_BASE = "http://localhost:8080";
        
        function checkAuthStatus() {
            const authToken = localStorage.getItem("authToken");
            const tokenExpiry = localStorage.getItem("tokenExpiry");
            const user = localStorage.getItem("user");
            
            let status = "";
            
            if (authToken) {
                const now = Date.now();
                const expiry = parseInt(tokenExpiry);
                const isExpired = now >= expiry;
                const timeLeft = Math.max(0, expiry - now);
                const minutesLeft = Math.floor(timeLeft / (1000 * 60));
                
                status += `<div class="status ${isExpired ? 'error' : 'success'}">`;
                status += `<strong>Token 狀態:</strong> ${isExpired ? '已過期' : '有效'}<br>`;
                status += `<strong>剩餘時間:</strong> ${minutesLeft} 分鐘<br>`;
                status += `<strong>Token 長度:</strong> ${authToken.length} 字符`;
                status += `</div>`;
                
                if (user) {
                    try {
                        const userData = JSON.parse(user);
                        status += `<div class="status info">`;
                        status += `<strong>用戶資料:</strong> ${userData.username || 'N/A'}<br>`;
                        status += `<strong>藥局名稱:</strong> ${userData.pharmacy_name || 'N/A'}<br>`;
                        status += `<strong>角色:</strong> ${userData.permissions?.role_name || 'N/A'}`;
                        status += `</div>`;
                    } catch (e) {
                        status += `<div class="status error">用戶資料解析失敗: ${e.message}</div>`;
                    }
                } else {
                    status += `<div class="status warning">沒有用戶資料快取</div>`;
                }
            } else {
                status = `<div class="status error">沒有找到認證 Token</div>`;
            }
            
            document.getElementById("auth-status").innerHTML = status;
        }
        
        function clearAuthData() {
            localStorage.removeItem("authToken");
            localStorage.removeItem("tokenExpiry");
            localStorage.removeItem("user");
            document.getElementById("auth-status").innerHTML = 
                `<div class="status info">認證資料已清除</div>`;
        }
        
        async function simulateF5Refresh() {
            const results = document.getElementById("test-results");
            results.innerHTML = `<div class="status info">正在模擬 F5 重新整理流程...</div>`;
            
            try {
                // 模擬應用程式初始化流程
                const authToken = localStorage.getItem("authToken");
                const tokenExpiry = localStorage.getItem("tokenExpiry");
                const savedUser = localStorage.getItem("user");
                
                let testResults = "";
                
                // 步驟 1: 檢查 Token
                if (!authToken) {
                    testResults += `<div class="status error">❌ 步驟 1: 沒有 Token，應該顯示登入頁面</div>`;
                    results.innerHTML = testResults;
                    return;
                }
                
                // 步驟 2: 檢查 Token 是否過期
                const now = Date.now();
                const expiry = parseInt(tokenExpiry);
                const isExpired = now >= expiry;
                
                if (isExpired) {
                    testResults += `<div class="status warning">⚠️ 步驟 2: Token 已過期，需要刷新</div>`;
                } else {
                    testResults += `<div class="status success">✅ 步驟 2: Token 有效</div>`;
                }
                
                // 步驟 3: 檢查用戶資料快取
                if (savedUser) {
                    try {
                        const userData = JSON.parse(savedUser);
                        testResults += `<div class="status success">✅ 步驟 3: 找到用戶資料快取 (${userData.username})</div>`;
                    } catch (e) {
                        testResults += `<div class="status error">❌ 步驟 3: 用戶資料快取損壞</div>`;
                    }
                } else {
                    testResults += `<div class="status warning">⚠️ 步驟 3: 沒有用戶資料快取</div>`;
                }
                
                // 步驟 4: 模擬 API 驗證
                testResults += `<div class="status info">🔄 步驟 4: 模擬 API 驗證...</div>`;
                
                try {
                    const response = await fetch(`${API_BASE}/api/auth/me`, {
                        headers: {
                            "Authorization": `Bearer ${authToken}`,
                            "Content-Type": "application/json"
                        }
                    });
                    
                    if (response.ok) {
                        testResults += `<div class="status success">✅ 步驟 4: API 驗證成功，應該保持登入狀態</div>`;
                        testResults += `<div class="status success">🎉 結論: F5 重新整理後應該保持登入狀態！</div>`;
                    } else if (response.status === 401) {
                        testResults += `<div class="status warning">⚠️ 步驟 4: Token 無效，需要刷新或重新登入</div>`;
                    } else {
                        testResults += `<div class="status error">❌ 步驟 4: API 驗證失敗 (${response.status})</div>`;
                    }
                } catch (error) {
                    if (savedUser) {
                        testResults += `<div class="status warning">⚠️ 步驟 4: 網路錯誤，但有快取資料，應該進入離線模式</div>`;
                        testResults += `<div class="status success">🎉 結論: 即使網路有問題，也應該保持登入狀態（離線模式）！</div>`;
                    } else {
                        testResults += `<div class="status error">❌ 步驟 4: 網路錯誤且沒有快取資料</div>`;
                    }
                }
                
                results.innerHTML = testResults;
                
            } catch (error) {
                results.innerHTML = `<div class="status error">測試失敗: ${error.message}</div>`;
            }
        }
        
        async function testTokenValidation() {
            const results = document.getElementById("test-results");
            const authToken = localStorage.getItem("authToken");
            
            if (!authToken) {
                results.innerHTML = `<div class="status error">沒有 Token 可以測試</div>`;
                return;
            }
            
            results.innerHTML = `<div class="status info">正在測試 Token 驗證...</div>`;
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/me`, {
                    headers: {
                        "Authorization": `Bearer ${authToken}`,
                        "Content-Type": "application/json"
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    results.innerHTML = `
                        <div class="status success">✅ Token 驗證成功</div>
                        <div class="code">${JSON.stringify(data, null, 2)}</div>
                    `;
                } else {
                    results.innerHTML = `
                        <div class="status error">❌ Token 驗證失敗 (${response.status})</div>
                        <div class="code">${JSON.stringify(data, null, 2)}</div>
                    `;
                }
            } catch (error) {
                results.innerHTML = `<div class="status error">網路錯誤: ${error.message}</div>`;
            }
        }
        
        function testOfflineMode() {
            const results = document.getElementById("test-results");
            const savedUser = localStorage.getItem("user");
            
            if (savedUser) {
                try {
                    const userData = JSON.parse(savedUser);
                    results.innerHTML = `
                        <div class="status success">✅ 離線模式可用</div>
                        <div class="status info">快取的用戶資料:</div>
                        <div class="code">${JSON.stringify(userData, null, 2)}</div>
                    `;
                } catch (e) {
                    results.innerHTML = `<div class="status error">❌ 用戶資料快取損壞</div>`;
                }
            } else {
                results.innerHTML = `<div class="status error">❌ 沒有用戶資料快取，無法使用離線模式</div>`;
            }
        }
        
        function showLocalStorage() {
            const content = document.getElementById("localStorage-content");
            let html = "<div class='code'>";
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                html += `${key}: ${value}\n\n`;
            }
            
            html += "</div>";
            content.innerHTML = html;
        }
        
        // 頁面載入時自動檢查狀態
        window.onload = function() {
            checkAuthStatus();
        };
    </script>
</body>
</html>