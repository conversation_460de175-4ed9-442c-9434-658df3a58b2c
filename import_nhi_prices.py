#!/usr/bin/env python3
import pandas as pd
import psycopg2
import sys
from decimal import Decimal
import os
from datetime import datetime

def get_database_config():
    """從環境變數或預設值獲取資料庫設定"""
    # 優先使用 DATABASE_URL
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        return database_url
    
    # 否則使用個別設定
    return {
        'host': os.getenv('DATABASE_HOST', 'localhost'),
        'port': int(os.getenv('DATABASE_PORT', '5432')),
        'database': os.getenv('DATABASE_NAME', 'happyorder'),
        'user': os.getenv('DATABASE_USER', 'happyorder'),
        'password': os.getenv('DATABASE_PASSWORD', 'password')
    }

def import_nhi_prices():
    print("=== 匯入 4.xlsx 到 nhi_prices 資料表 ===")
    
    # 讀取 Excel 檔案
    print("正在讀取 4.xlsx...")
    df = pd.read_excel('4.xlsx', header=None)
    print(f"Excel 檔案總行數: {len(df)}")
    
    # 提取 B 欄 (nhi_code) 和 C 欄 (nhi_price)
    nhi_data = df.iloc[:, [1, 2]].copy()  # 取第1欄和第2欄 (索引1和2)
    nhi_data.columns = ['nhi_code', 'nhi_price']
    
    # 過濾掉空值和無效資料
    original_count = len(nhi_data)
    nhi_data = nhi_data.dropna()
    nhi_data = nhi_data[nhi_data['nhi_code'] != '']
    nhi_data = nhi_data[nhi_data['nhi_price'] != 0]  # 過濾掉價格為0的記錄
    
    # 去重 - 相同 nhi_code 只保留一筆
    nhi_data = nhi_data.drop_duplicates(subset=['nhi_code'], keep='first')
    
    filtered_count = len(nhi_data)
    print(f"過濾後有效資料: {filtered_count} 筆 (原始: {original_count} 筆)")
    print(f"已排除: {original_count - filtered_count} 筆無效或重複資料")
    
    if filtered_count == 0:
        print("沒有有效資料可匯入！")
        return
    
    # 顯示前幾筆資料作為預覽
    print("\n前5筆將匯入的資料:")
    print(nhi_data.head())
    
    # 連接資料庫
    db_config = get_database_config()
    print(f"\n正在連接資料庫...")
    
    try:
        if isinstance(db_config, str):
            # 使用 DATABASE_URL 連接
            conn = psycopg2.connect(db_config)
            print("使用 DATABASE_URL 連接")
        else:
            # 使用個別參數連接
            conn = psycopg2.connect(**db_config)
            print(f"連接到: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        cursor = conn.cursor()
        
        # 檢查資料表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_name = 'nhi_prices'
            )
        """)
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            print("錯誤: nhi_prices 資料表不存在！請先執行資料庫遷移。")
            return
        
        print("資料庫連接成功，開始匯入資料...")
        
        # 統計原有資料
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        existing_count = cursor.fetchone()[0]
        print(f"資料表現有記錄: {existing_count} 筆")
        
        # 準備插入資料的SQL (使用 UPSERT，基於 nhi_code 和 effective_date 的唯一約束)
        insert_sql = """
        INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price, effective_date, created_at, updated_at)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON CONFLICT (nhi_code, effective_date) 
        DO UPDATE SET 
            nhi_price = EXCLUDED.nhi_price,
            selling_price = EXCLUDED.selling_price,
            updated_at = EXCLUDED.updated_at
        """
        
        # 批量插入資料
        success_count = 0
        error_count = 0
        current_time = datetime.now()
        current_date = current_time.date()  # 使用當前日期作為 effective_date
        
        for index, row in nhi_data.iterrows():
            try:
                nhi_code = str(row['nhi_code']).strip()
                nhi_price = Decimal(str(row['nhi_price']))
                
                # 執行插入/更新
                cursor.execute(insert_sql, (
                    nhi_code,
                    nhi_price,
                    nhi_price,  # selling_price 設為與 nhi_price 相同
                    current_date,  # effective_date
                    current_time,
                    current_time
                ))
                success_count += 1
                
                if success_count % 1000 == 0:
                    print(f"已處理 {success_count} 筆...")
                    
            except Exception as e:
                error_count += 1
                print(f"第 {index+1} 行匯入失敗: {e}")
                if error_count > 10:  # 如果錯誤太多就停止
                    print("錯誤過多，停止匯入")
                    break
        
        # 提交異動
        conn.commit()
        
        # 統計最終結果
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        final_count = cursor.fetchone()[0]
        
        print(f"\n=== 匯入完成 ===")
        print(f"成功匯入: {success_count} 筆")
        print(f"匯入失敗: {error_count} 筆")
        print(f"資料表記錄數: {existing_count} → {final_count}")
        print(f"新增記錄: {final_count - existing_count} 筆")
        
        # 顯示一些統計資訊
        cursor.execute("SELECT MIN(nhi_price), MAX(nhi_price), AVG(nhi_price) FROM nhi_prices")
        min_price, max_price, avg_price = cursor.fetchone()
        print(f"價格範圍: ${min_price} ~ ${max_price} (平均: ${avg_price:.2f})")
        
        cursor.close()
        conn.close()
        
        print("資料庫連接已關閉")
        
    except Exception as e:
        print(f"資料庫操作失敗: {e}")
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        sys.exit(1)

if __name__ == "__main__":
    try:
        import_nhi_prices()
    except Exception as e:
        print(f"執行失敗: {e}")
        sys.exit(1)