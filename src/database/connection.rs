use sqlx::{
    postgres::{PgPool, PgPoolOptions, PgConnectOptions},
};
use std::{sync::Arc, time::Duration, str::FromStr};
use tracing::{info, debug};

use super::error::DatabaseError;

pub type DbPool = Arc<PgPool>;

/// 資料庫連線管理器
#[derive(Debug, Clone)]
pub struct ConnectionManager {
    pool: DbPool,
    #[allow(dead_code)]
    config: ConnectionConfig,
}

/// 連線配置
#[derive(Debug, Clone)]
pub struct ConnectionConfig {
    pub database_url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
    #[allow(dead_code)]
    pub enable_wal_mode: bool,
    #[allow(dead_code)]
    pub enable_foreign_keys: bool,
}

impl Default for ConnectionConfig {
    fn default() -> Self {
        Self {
            database_url: std::env::var("DATABASE_URL").unwrap_or_else(|_| "postgresql://localhost:5432/happyorder".to_string()),
            max_connections: 10,
            min_connections: 1,
            connection_timeout: Duration::from_secs(30),
            idle_timeout: Duration::from_secs(600),
            max_lifetime: Duration::from_secs(3600),
            enable_wal_mode: false,
            enable_foreign_keys: true,
        }
    }
}

impl ConnectionManager {
    /// 建立新的連線管理器
    pub async fn new(config: ConnectionConfig) -> Result<Self, DatabaseError> {
        info!("Creating database connection manager");
        
        let connect_options = PgConnectOptions::from_str(&config.database_url)
            .map_err(|e| DatabaseError::Configuration(format!("Invalid database URL: {}", e)))?;

        let pool = PgPoolOptions::new()
            .max_connections(config.max_connections)
            .min_connections(config.min_connections)
            .acquire_timeout(config.connection_timeout)
            .idle_timeout(Some(config.idle_timeout))
            .max_lifetime(Some(config.max_lifetime))
            .connect_with(connect_options)
            .await
            .map_err(DatabaseError::Connection)?;

        let manager = Self {
            pool: Arc::new(pool),
            config,
        };

        // PostgreSQL 不需要額外配置

        info!("Database connection manager created successfully");
        Ok(manager)
    }

    /// 取得連線池
    pub fn pool(&self) -> &DbPool {
        &self.pool
    }


    /// 健康檢查
    pub async fn health_check(&self) -> Result<(), DatabaseError> {
        debug!("Performing connection health check...");
        
        sqlx::query("SELECT 1")
            .fetch_one(&*self.pool)
            .await
            .map_err(|e| DatabaseError::HealthCheck(format!("Health check failed: {}", e)))?;

        debug!("Connection health check passed");
        Ok(())
    }

    /// 取得連線池統計
    pub fn stats(&self) -> ConnectionStats {
        ConnectionStats {
            size: self.pool.size(),
            idle: self.pool.num_idle() as u32,
            max_connections: self.config.max_connections,
        }
    }

    /// 關閉連線池
    pub async fn close(&self) {
        info!("Closing database connection pool...");
        self.pool.close().await;
        info!("Database connection pool closed");
    }
}

/// 連線統計資訊
#[derive(Debug)]
pub struct ConnectionStats {
    pub size: u32,
    pub idle: u32,
    pub max_connections: u32,
}

impl std::fmt::Display for ConnectionStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "Connection Stats - Size: {}, Idle: {}, Max: {}",
            self.size, self.idle, self.max_connections
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_connection_manager_creation() {
        let config = ConnectionConfig {
            database_url: "postgresql://postgres:password@localhost:5432/test_db".to_string(),
            ..Default::default()
        };

        // Note: This test requires a running PostgreSQL instance
        let _manager = ConnectionManager::new(config).await;
        // assert!(manager.is_ok());
    }

    #[tokio::test]
    async fn test_health_check() {
        let config = ConnectionConfig {
            database_url: "postgresql://postgres:password@localhost:5432/test_db".to_string(),
            ..Default::default()
        };

        // Note: This test requires a running PostgreSQL instance
        // let manager = ConnectionManager::new(config).await.unwrap();
        // let result = manager.health_check().await;
        // assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_connection_stats() {
        let config = ConnectionConfig {
            database_url: "postgresql://postgres:password@localhost:5432/test_db".to_string(),
            max_connections: 5,
            ..Default::default()
        };

        // Note: This test requires a running PostgreSQL instance
        // let manager = ConnectionManager::new(config).await.unwrap();
        // let stats = manager.stats();
        // assert_eq!(stats.max_connections, 5);
    }
}