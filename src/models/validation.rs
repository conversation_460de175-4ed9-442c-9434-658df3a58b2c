use regex::Regex;
use rust_decimal::Decimal;
use thiserror::Error;

#[derive(Debug, Erro<PERSON>)]
pub enum ValidationError {
    #[error("Field '{field}' is required")]
    Required { field: String },
    
    #[error("Field '{field}' must be between {min} and {max} characters")]
    Length { field: String, min: usize, max: usize },
    
    #[error("Field '{field}' has invalid format")]
    Format { field: String },
    
    #[error("Field '{field}' must be positive")]
    Positive { field: String },
    
    #[error("Field '{field}' must be non-negative")]
    NonNegative { field: String },
    
    #[error("Field '{field}' value '{value}' is not allowed")]
    InvalidValue { field: String, value: String },
    
    #[error("Email format is invalid")]
    InvalidEmail,
    
    #[error("NHI code format is invalid")]
    InvalidNhiCode,
    
    #[error("Phone number format is invalid")]
    InvalidPhone,
}

pub type ValidationResult<T> = Result<T, ValidationError>;

#[allow(dead_code)]
pub trait Validate {
    fn validate(&self) -> Result<(), Vec<ValidationError>>;
}

// Common validation functions
pub fn validate_required_string(value: &str, field_name: &str) -> ValidationResult<()> {
    if value.trim().is_empty() {
        Err(ValidationError::Required {
            field: field_name.to_string(),
        })
    } else {
        Ok(())
    }
}

pub fn validate_string_length(
    value: &str,
    field_name: &str,
    min: usize,
    max: usize,
) -> ValidationResult<()> {
    let len = value.trim().len();
    if len < min || len > max {
        Err(ValidationError::Length {
            field: field_name.to_string(),
            min,
            max,
        })
    } else {
        Ok(())
    }
}

pub fn validate_email(email: &str) -> ValidationResult<()> {
    let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap();
    if email_regex.is_match(email) {
        Ok(())
    } else {
        Err(ValidationError::InvalidEmail)
    }
}

pub fn validate_nhi_code(nhi_code: &str) -> ValidationResult<()> {
    // NHI code format: alphanumeric, 4-12 characters
    let nhi_regex = Regex::new(r"^[A-Z0-9]{4,12}$").unwrap();
    if nhi_regex.is_match(nhi_code) {
        Ok(())
    } else {
        Err(ValidationError::InvalidNhiCode)
    }
}

pub fn validate_phone(phone: &str) -> ValidationResult<()> {
    // Taiwan phone number format: 09xxxxxxxx or +886-9xxxxxxxx
    let phone_regex = Regex::new(r"^(\+886-?)?0?9\d{8}$").unwrap();
    if phone_regex.is_match(phone) {
        Ok(())
    } else {
        Err(ValidationError::InvalidPhone)
    }
}

pub fn validate_positive_decimal(value: &Decimal, field_name: &str) -> ValidationResult<()> {
    if *value <= Decimal::ZERO {
        Err(ValidationError::Positive {
            field: field_name.to_string(),
        })
    } else {
        Ok(())
    }
}

pub fn validate_non_negative_i32(value: i32, field_name: &str) -> ValidationResult<()> {
    if value < 0 {
        Err(ValidationError::NonNegative {
            field: field_name.to_string(),
        })
    } else {
        Ok(())
    }
}

pub fn validate_non_negative_i64(value: i64, field_name: &str) -> ValidationResult<()> {
    if value < 0 {
        Err(ValidationError::NonNegative {
            field: field_name.to_string(),
        })
    } else {
        Ok(())
    }
}

pub fn validate_username(username: &str) -> ValidationResult<()> {
    // Username: 3-50 characters, alphanumeric and underscore only
    let username_regex = Regex::new(r"^[a-zA-Z0-9_]{3,50}$").unwrap();
    if username_regex.is_match(username) {
        Ok(())
    } else {
        Err(ValidationError::Format {
            field: "username".to_string(),
        })
    }
}

pub fn validate_password_strength(password: &str) -> ValidationResult<()> {
    // Password: at least 8 characters, must contain letter and number
    if password.len() < 8 {
        return Err(ValidationError::Length {
            field: "password".to_string(),
            min: 8,
            max: 128,
        });
    }
    
    let has_letter = password.chars().any(|c| c.is_alphabetic());
    let has_number = password.chars().any(|c| c.is_numeric());
    
    if !has_letter || !has_number {
        return Err(ValidationError::Format {
            field: "password".to_string(),
        });
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[test]
    fn test_validate_required_string() {
        assert!(validate_required_string("test", "field").is_ok());
        assert!(validate_required_string("", "field").is_err());
        assert!(validate_required_string("   ", "field").is_err());
    }

    #[test]
    fn test_validate_string_length() {
        assert!(validate_string_length("test", "field", 1, 10).is_ok());
        assert!(validate_string_length("", "field", 1, 10).is_err());
        assert!(validate_string_length("very long string", "field", 1, 10).is_err());
    }

    #[test]
    fn test_validate_email() {
        assert!(validate_email("<EMAIL>").is_ok());
        assert!(validate_email("<EMAIL>").is_ok());
        assert!(validate_email("invalid-email").is_err());
        assert!(validate_email("@domain.com").is_err());
    }

    #[test]
    fn test_validate_nhi_code() {
        assert!(validate_nhi_code("A123456789").is_ok());
        assert!(validate_nhi_code("BC1234567890").is_ok());
        assert!(validate_nhi_code("invalid").is_err());
        assert!(validate_nhi_code("123").is_err());
    }

    #[test]
    fn test_validate_phone() {
        assert!(validate_phone("0912345678").is_ok());
        assert!(validate_phone("+886-912345678").is_ok());
        assert!(validate_phone("912345678").is_ok());
        assert!(validate_phone("invalid").is_err());
        assert!(validate_phone("0812345678").is_err()); // Not mobile
    }

    #[test]
    fn test_validate_positive_decimal() {
        assert!(validate_positive_decimal(&dec!(10.50), "price").is_ok());
        assert!(validate_positive_decimal(&dec!(0), "price").is_err());
        assert!(validate_positive_decimal(&dec!(-5.0), "price").is_err());
    }

    #[test]
    fn test_validate_username() {
        assert!(validate_username("user123").is_ok());
        assert!(validate_username("test_user").is_ok());
        assert!(validate_username("ab").is_err()); // Too short
        assert!(validate_username("user-name").is_err()); // Invalid character
    }

    #[test]
    fn test_validate_password_strength() {
        assert!(validate_password_strength("password123").is_ok());
        assert!(validate_password_strength("Test1234").is_ok());
        assert!(validate_password_strength("short").is_err()); // Too short
        assert!(validate_password_strength("onlyletters").is_err()); // No numbers
        assert!(validate_password_strength("12345678").is_err()); // No letters
    }
}