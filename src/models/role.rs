use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Role {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Permission {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub resource: String,
    pub action: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct RolePermission {
    pub id: i64,
    pub role_id: i64,
    pub permission_id: i64,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RoleWithPermissions {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub permissions: Vec<Permission>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// 權限檢查結構
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[allow(dead_code)]
pub struct UserPermissions {
    pub user_id: i64,
    pub role_name: String,
    pub permissions: Vec<String>,
}

impl UserPermissions {
    pub fn has_permission(&self, permission: &str) -> bool {
        self.permissions.contains(&permission.to_string())
    }

    pub fn has_resource_action(&self, resource: &str, action: &str) -> bool {
        let permission = format!("{}.{}", resource, action);
        self.has_permission(&permission)
    }

    pub fn is_super_admin(&self) -> bool {
        self.role_name == "super_admin"
    }

    pub fn is_admin(&self) -> bool {
        self.role_name == "admin" || self.is_super_admin()
    }

    pub fn can_manage_users(&self) -> bool {
        self.has_resource_action("users", "read_all") || self.is_super_admin()
    }

    pub fn can_manage_products(&self) -> bool {
        self.has_resource_action("products", "create") || 
        self.has_resource_action("products", "update") || 
        self.has_resource_action("products", "delete")
    }

    pub fn can_view_all_orders(&self) -> bool {
        self.has_resource_action("orders", "read_all") || self.is_admin()
    }
}

// 角色枚舉
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum UserRole {
    SuperAdmin,
    Admin, 
    Pharmacy,
    Viewer,
}

impl UserRole {
    pub fn as_str(&self) -> &'static str {
        match self {
            UserRole::SuperAdmin => "super_admin",
            UserRole::Admin => "admin",
            UserRole::Pharmacy => "pharmacy", 
            UserRole::Viewer => "viewer",
        }
    }

    pub fn from_str(s: &str) -> Option<UserRole> {
        match s {
            "super_admin" => Some(UserRole::SuperAdmin),
            "admin" => Some(UserRole::Admin),
            "pharmacy" => Some(UserRole::Pharmacy),
            "viewer" => Some(UserRole::Viewer),
            _ => None,
        }
    }

    pub fn description(&self) -> &'static str {
        match self {
            UserRole::SuperAdmin => "超級管理員 - 擁有所有權限",
            UserRole::Admin => "管理員 - 可以管理產品和訂單",
            UserRole::Pharmacy => "藥局用戶 - 可以下訂單和查看自己的資料",
            UserRole::Viewer => "檢視者 - 只能查看資料",
        }
    }
}

// 創建角色請求
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRoleRequest {
    pub name: String,
    pub description: Option<String>,
    pub permission_ids: Vec<i64>,
}

// 更新角色請求
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateRoleRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub permission_ids: Option<Vec<i64>>,
}