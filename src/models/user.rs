use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use bcrypt::{hash, verify, DEFAULT_COST};
use validator::{Validate as ValidatorValidate, ValidationError as ValidatorError};
use regex::Regex;
use lazy_static::lazy_static;
use crate::models::validation::{
    Validate, ValidationError,
    validate_required_string, validate_string_length, validate_email,
    validate_username, validate_password_strength, validate_phone
};
use crate::models::date_format;

// 正則表達式常量
lazy_static! {
    pub static ref USERNAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_]{3,50}$").unwrap();
    pub static ref PHONE_REGEX: Regex = Regex::new(r"^[\+]?[0-9\-\(\)\s]{8,20}$").unwrap();
    pub static ref NHI_CODE_REGEX: Regex = Regex::new(r"^[A-Z][0-9]{9}$").unwrap();
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::FromRow)]
pub struct User {
    pub id: i64,
    pub username: String,
    pub email: String,
    pub password_hash: String,
    pub pharmacy_name: String,
    pub phone: Option<String>,
    pub line_user_id: Option<String>,
    pub notification_email: bool,
    pub notification_line: bool,
    pub role_id: i64,
    pub contact_person: Option<String>,
    pub mobile: Option<String>,
    pub institution_code: Option<String>,
    pub address: Option<String>,
    #[serde(with = "date_format::date_only_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "date_format::date_only_format")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserWithRole {
    pub id: i64,
    pub username: String,
    pub email: String,
    pub pharmacy_name: String,
    pub phone: Option<String>,
    pub line_user_id: Option<String>,
    pub notification_email: bool,
    pub notification_line: bool,
    pub role_id: i64,
    pub role_name: String,
    pub role_description: Option<String>,
    pub contact_person: Option<String>,
    pub mobile: Option<String>,
    pub institution_code: Option<String>,
    pub address: Option<String>,
    #[serde(with = "date_format::date_only_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "date_format::date_only_format")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, ValidatorValidate)]
pub struct LoginRequest {
    #[validate(length(min = 3, max = 50, message = "用戶名長度必須在 3-50 字符之間"))]
    #[validate(regex(path = "USERNAME_REGEX", message = "用戶名只能包含字母、數字和下劃線"))]
    pub username: String,
    
    #[validate(length(min = 6, message = "密碼長度至少 6 個字符"))]
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize, ValidatorValidate)]
pub struct RegisterRequest {
    #[validate(length(min = 3, max = 50, message = "用戶名長度必須在 3-50 字符之間"))]
    #[validate(regex(path = "USERNAME_REGEX", message = "用戶名只能包含字母、數字和下劃線"))]
    pub username: String,
    
    #[validate(email(message = "請輸入有效的電子郵箱地址"))]
    #[validate(length(max = 255, message = "電子郵箱長度不能超過 255 字符"))]
    pub email: String,
    
    #[validate(length(min = 8, message = "密碼長度至少 8 個字符"))]
    #[validate(custom = "validate_password_complexity")]
    pub password: String,
    
    #[validate(length(min = 2, max = 200, message = "機構單位名稱長度必須在 2-200 字符之間"))]
    pub pharmacy_name: String,
    
    #[validate(length(min = 1, max = 100, message = "聯絡人姓名長度必須在 1-100 字符之間"))]
    pub contact_person: String,
    
    #[validate(regex(path = "PHONE_REGEX", message = "請輸入有效的聯絡電話"))]
    pub phone: String,
    
    #[validate(regex(path = "PHONE_REGEX", message = "請輸入有效的手機號碼"))]
    pub mobile: String,
    
    #[validate(length(min = 1, max = 50, message = "機構代號長度必須在 1-50 字符之間"))]
    pub institution_code: String,
    
    #[validate(length(min = 5, max = 500, message = "聯絡地址長度必須在 5-500 字符之間"))]
    pub address: String,
    
    #[validate(length(max = 100, message = "LINE ID 長度不能超過 100 字符"))]
    pub line_user_id: Option<String>,
    
    // 角色 ID，如果未提供則使用預設角色 (pharmacy)
    pub role_id: Option<i64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub user: UserInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: i64,
    pub username: String,
    pub email: String,
    pub pharmacy_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,
    pub user_id: i64,
    pub exp: usize,
    pub iat: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NotificationPreferences {
    pub email: bool,
    pub line: bool,
}

#[derive(Debug, Clone, Copy)]
#[allow(dead_code)]
pub enum NotificationChannel {
    Email,
    Line,
}

#[allow(dead_code)]
impl User {
    pub fn new(
        username: String,
        email: String,
        password_hash: String,
        pharmacy_name: String,
        phone: Option<String>,
        line_user_id: Option<String>,
        contact_person: Option<String>,
        mobile: Option<String>,
        institution_code: Option<String>,
        address: Option<String>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: 0, // Will be set by database
            username,
            email,
            password_hash,
            pharmacy_name,
            phone,
            line_user_id,
            notification_email: true,
            notification_line: false,
            role_id: 3, // 預設為 pharmacy 角色
            contact_person,
            mobile,
            institution_code,
            address,
            created_at: now,
            updated_at: now,
        }
    }

    /// 建立新使用者，自動雜湊密碼
    pub fn create_with_password(
        username: String,
        email: String,
        password: &str,
        pharmacy_name: String,
        phone: Option<String>,
        line_user_id: Option<String>,
        contact_person: Option<String>,
        mobile: Option<String>,
        institution_code: Option<String>,
        address: Option<String>,
    ) -> Result<Self, bcrypt::BcryptError> {
        let password_hash = Self::hash_password(password)?;
        Ok(Self::new(username, email, password_hash, pharmacy_name, phone, line_user_id, contact_person, mobile, institution_code, address))
    }

    /// 雜湊密碼
    pub fn hash_password(password: &str) -> Result<String, bcrypt::BcryptError> {
        hash(password, DEFAULT_COST)
    }

    /// 驗證密碼
    pub fn verify_password(&self, password: &str) -> Result<bool, bcrypt::BcryptError> {
        verify(password, &self.password_hash)
    }

    /// 更新密碼
    pub fn update_password(&mut self, new_password: &str) -> Result<(), bcrypt::BcryptError> {
        self.password_hash = Self::hash_password(new_password)?;
        self.updated_at = Utc::now();
        Ok(())
    }

    pub fn notification_preferences(&self) -> NotificationPreferences {
        NotificationPreferences {
            email: self.notification_email,
            line: self.notification_line,
        }
    }

    pub fn update_notification_preferences(&mut self, preferences: NotificationPreferences) {
        self.notification_email = preferences.email;
        self.notification_line = preferences.line;
        self.updated_at = Utc::now();
    }

    /// 更新使用者資訊
    pub fn update_info(&mut self, pharmacy_name: Option<String>, phone: Option<String>, line_user_id: Option<String>) {
        if let Some(name) = pharmacy_name {
            self.pharmacy_name = name;
        }
        if let Some(p) = phone {
            self.phone = Some(p);
        }
        if let Some(line_id) = line_user_id {
            self.line_user_id = Some(line_id);
        }
        self.updated_at = Utc::now();
    }

    /// 檢查使用者是否啟用了特定通知管道
    pub fn has_notification_enabled(&self, channel: NotificationChannel) -> bool {
        match channel {
            NotificationChannel::Email => self.notification_email,
            NotificationChannel::Line => self.notification_line && self.line_user_id.is_some(),
        }
    }
}

impl Validate for User {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if let Err(e) = validate_username(&self.username) {
            errors.push(e);
        }

        if let Err(e) = validate_email(&self.email) {
            errors.push(e);
        }

        if let Err(e) = validate_required_string(&self.pharmacy_name, "pharmacy_name") {
            errors.push(e);
        }

        if let Err(e) = validate_string_length(&self.pharmacy_name, "pharmacy_name", 1, 200) {
            errors.push(e);
        }

        if let Some(ref phone) = self.phone {
            if !phone.is_empty() {
                if let Err(e) = validate_phone(phone) {
                    errors.push(e);
                }
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

impl Validate for RegisterRequest {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if let Err(e) = validate_username(&self.username) {
            errors.push(e);
        }

        if let Err(e) = validate_email(&self.email) {
            errors.push(e);
        }

        if let Err(e) = validate_password_strength(&self.password) {
            errors.push(e);
        }

        if let Err(e) = validate_required_string(&self.pharmacy_name, "pharmacy_name") {
            errors.push(e);
        }

        if let Err(e) = validate_string_length(&self.pharmacy_name, "pharmacy_name", 1, 200) {
            errors.push(e);
        }

        if !self.phone.is_empty() {
            if let Err(e) = validate_phone(&self.phone) {
                errors.push(e);
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

impl Validate for LoginRequest {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if let Err(e) = validate_required_string(&self.username, "username") {
            errors.push(e);
        }

        if let Err(e) = validate_required_string(&self.password, "password") {
            errors.push(e);
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 密碼複雜度驗證函數
pub fn validate_password_complexity(password: &str) -> Result<(), ValidatorError> {
    // 檢查是否包含至少一個數字
    if !password.chars().any(|c| c.is_ascii_digit()) {
        return Err(ValidatorError::new("密碼必須包含至少一個數字"));
    }
    
    // 檢查是否包含至少一個字母
    if !password.chars().any(|c| c.is_ascii_alphabetic()) {
        return Err(ValidatorError::new("密碼必須包含至少一個字母"));
    }
    
    // 檢查是否包含至少一個特殊字符
    let special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
    if !password.chars().any(|c| special_chars.contains(c)) {
        return Err(ValidatorError::new("密碼必須包含至少一個特殊字符 (!@#$%^&*()_+-=[]{}|;:,.<>?)"));
    }
    
    // 檢查是否有常見的弱密碼模式
    let weak_patterns = [
        "123456", "password", "admin", "user", "test", "qwerty", 
        "abc123", "password123", "admin123", "12345678"
    ];
    
    let lower_password = password.to_lowercase();
    for pattern in &weak_patterns {
        if lower_password.contains(pattern) {
            return Err(ValidatorError::new("密碼不能包含常見的弱密碼模式"));
        }
    }
    
    Ok(())
}

/// 安全性增強：檢查密碼是否被洩露 (簡化版本)
#[allow(dead_code)]
pub fn check_password_against_common_list(password: &str) -> bool {
    // 這裡可以集成 HaveIBeenPwned API 或本地常見密碼列表
    // 為了演示，我們使用一個簡單的黑名單
    let common_passwords = [
        "password", "123456", "password123", "admin", "qwerty",
        "12345678", "123456789", "password1", "abc123", "Password1"
    ];
    
    !common_passwords.iter().any(|&p| p.eq_ignore_ascii_case(password))
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    #[test]
    fn test_user_new() {
        let user = User::new(
            "testuser".to_string(),
            "<EMAIL>".to_string(),
            "hashed_password".to_string(),
            "測試藥局".to_string(),
            Some("0912345678".to_string()),
            None,
        );

        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, "<EMAIL>");
        assert_eq!(user.pharmacy_name, "測試藥局");
        assert_eq!(user.phone, Some("0912345678".to_string()));
        assert_eq!(user.line_user_id, None);
        assert!(user.notification_email);
        assert!(!user.notification_line);
    }

    #[test]
    fn test_user_create_with_password() {
        let password = "test_password_123";
        let user = User::create_with_password(
            "testuser".to_string(),
            "<EMAIL>".to_string(),
            password,
            "測試藥局".to_string(),
            Some("0912345678".to_string()),
            None,
        ).expect("Failed to create user with password");

        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, "<EMAIL>");
        assert_eq!(user.pharmacy_name, "測試藥局");
        assert_ne!(user.password_hash, password); // 密碼應該被雜湊
        assert!(user.password_hash.starts_with("$2b$")); // bcrypt 雜湊格式
    }

    #[test]
    fn test_password_hashing() {
        let password = "test_password_123";
        let hash1 = User::hash_password(password).expect("Failed to hash password");
        let hash2 = User::hash_password(password).expect("Failed to hash password");

        // 每次雜湊應該產生不同的結果（因為 salt）
        assert_ne!(hash1, hash2);
        
        // 但都應該是有效的 bcrypt 雜湊
        assert!(hash1.starts_with("$2b$"));
        assert!(hash2.starts_with("$2b$"));
    }

    #[test]
    fn test_password_verification() {
        let password = "test_password_123";
        let user = User::create_with_password(
            "testuser".to_string(),
            "<EMAIL>".to_string(),
            password,
            "測試藥局".to_string(),
            None,
            None,
        ).expect("Failed to create user");

        // 正確密碼應該驗證成功
        assert!(user.verify_password(password).expect("Password verification failed"));
        
        // 錯誤密碼應該驗證失敗
        assert!(!user.verify_password("wrong_password").expect("Password verification failed"));
    }

    #[test]
    fn test_password_update() {
        let original_password = "original_password_123";
        let new_password = "new_password_456";
        
        let mut user = User::create_with_password(
            "testuser".to_string(),
            "<EMAIL>".to_string(),
            original_password,
            "測試藥局".to_string(),
            None,
            None,
        ).expect("Failed to create user");

        let original_hash = user.password_hash.clone();

        // 更新密碼
        user.update_password(new_password).expect("Failed to update password");

        // 密碼雜湊應該改變
        assert_ne!(user.password_hash, original_hash);
        
        // 舊密碼應該無法驗證
        assert!(!user.verify_password(original_password).expect("Password verification failed"));
        
        // 新密碼應該可以驗證
        assert!(user.verify_password(new_password).expect("Password verification failed"));
    }

    #[test]
    fn test_user_validation_success() {
        let user = User {
            id: 1,
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hashed_password".to_string(),
            pharmacy_name: "測試藥局".to_string(),
            phone: Some("0912345678".to_string()),
            line_user_id: None,
            notification_email: true,
            notification_line: false,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        assert!(user.validate().is_ok());
    }

    #[test]
    fn test_user_validation_failure() {
        let user = User {
            id: 1,
            username: "".to_string(), // 空使用者名稱
            email: "invalid-email".to_string(), // 無效郵箱
            password_hash: "hashed_password".to_string(),
            pharmacy_name: "".to_string(), // 空藥局名稱
            phone: Some("invalid-phone".to_string()), // 無效電話
            line_user_id: None,
            notification_email: true,
            notification_line: false,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let errors = user.validate().unwrap_err();
        assert!(errors.len() >= 4); // 至少4個錯誤
    }

    #[test]
    fn test_register_request_validation_success() {
        let request = RegisterRequest {
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password: "password123".to_string(),
            pharmacy_name: "測試藥局".to_string(),
            phone: Some("0912345678".to_string()),
            line_user_id: None,
        };

        assert!(request.validate().is_ok());
    }

    #[test]
    fn test_register_request_validation_failure() {
        let request = RegisterRequest {
            username: "".to_string(),
            email: "invalid-email".to_string(),
            password: "123".to_string(), // 太短
            pharmacy_name: "".to_string(),
            phone: Some("invalid-phone".to_string()),
            line_user_id: None,
        };

        let errors = request.validate().unwrap_err();
        assert!(errors.len() >= 4);
    }

    #[test]
    fn test_login_request_validation() {
        let request = LoginRequest {
            username: "testuser".to_string(),
            password: "password123".to_string(),
        };

        assert!(request.validate().is_ok());

        let invalid_request = LoginRequest {
            username: "".to_string(),
            password: "".to_string(),
        };

        let errors = invalid_request.validate().unwrap_err();
        assert_eq!(errors.len(), 2);
    }

    #[test]
    fn test_notification_preferences() {
        let mut user = User::new(
            "testuser".to_string(),
            "<EMAIL>".to_string(),
            "hashed_password".to_string(),
            "測試藥局".to_string(),
            None,
            None,
        );

        let preferences = user.notification_preferences();
        assert!(preferences.email);
        assert!(!preferences.line);

        // 更新偏好設定
        user.update_notification_preferences(NotificationPreferences {
            email: false,
            line: true,
        });

        let new_preferences = user.notification_preferences();
        assert!(!new_preferences.email);
        assert!(new_preferences.line);
    }

    #[test]
    fn test_update_info() {
        let mut user = User::new(
            "testuser".to_string(),
            "<EMAIL>".to_string(),
            "hashed_password".to_string(),
            "原始藥局".to_string(),
            None,
            None,
        );

        let original_updated_at = user.updated_at;

        // 等待一毫秒確保時間戳不同
        std::thread::sleep(std::time::Duration::from_millis(1));

        user.update_info(
            Some("新藥局名稱".to_string()),
            Some("0987654321".to_string()),
            Some("line_user_123".to_string()),
        );

        assert_eq!(user.pharmacy_name, "新藥局名稱");
        assert_eq!(user.phone, Some("0987654321".to_string()));
        assert_eq!(user.line_user_id, Some("line_user_123".to_string()));
        assert!(user.updated_at > original_updated_at);
    }

    #[test]
    fn test_has_notification_enabled() {
        let mut user = User::new(
            "testuser".to_string(),
            "<EMAIL>".to_string(),
            "hashed_password".to_string(),
            "測試藥局".to_string(),
            None,
            Some("line_user_123".to_string()),
        );

        // 預設 email 啟用，line 停用
        assert!(user.has_notification_enabled(NotificationChannel::Email));
        assert!(!user.has_notification_enabled(NotificationChannel::Line));

        // 啟用 line 通知
        user.notification_line = true;
        assert!(user.has_notification_enabled(NotificationChannel::Line));

        // 移除 line_user_id，即使啟用也應該回傳 false
        user.line_user_id = None;
        assert!(!user.has_notification_enabled(NotificationChannel::Line));
    }

    #[test]
    fn test_password_edge_cases() {
        // 測試空密碼
        let result = User::hash_password("");
        assert!(result.is_ok());

        // 測試很長的密碼
        let long_password = "a".repeat(1000);
        let result = User::hash_password(&long_password);
        assert!(result.is_ok());

        // 測試包含特殊字符的密碼
        let special_password = "密碼123!@#$%^&*()_+-=[]{}|;':\",./<>?";
        let result = User::hash_password(special_password);
        assert!(result.is_ok());

        let user = User::create_with_password(
            "testuser".to_string(),
            "<EMAIL>".to_string(),
            special_password,
            "測試藥局".to_string(),
            None,
            None,
        ).expect("Failed to create user");

        assert!(user.verify_password(special_password).expect("Password verification failed"));
    }
}