use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::models::validation::{
    Validate, ValidationError, ValidationResult,
    validate_positive_decimal, validate_non_negative_i32
};
use crate::models::date_format;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum OrderStatus {
    待處理,
    揀貨中,
    已出貨,
}

impl From<String> for OrderStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "待處理" => OrderStatus::待處理,
            "揀貨中" => OrderStatus::揀貨中, 
            "已出貨" => OrderStatus::已出貨,
            // 兼容舊的英文狀態
            "Pending" => OrderStatus::待處理,
            "Processing" => OrderStatus::揀貨中,
            "Shipped" => OrderStatus::已出貨,
            _ => OrderStatus::待處理, // 預設值
        }
    }
}

impl ToString for OrderStatus {
    fn to_string(&self) -> String {
        match self {
            OrderStatus::待處理 => "待處理".to_string(),
            OrderStatus::揀貨中 => "揀貨中".to_string(),
            OrderStatus::已出貨 => "已出貨".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct Order {
    pub id: i64,
    pub order_number: String,
    pub user_id: i64,
    pub status: OrderStatus,
    pub total_amount: Decimal,
    pub notes: Option<String>,
    #[serde(with = "date_format::date_only_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "date_format::date_only_format")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct OrderItem {
    pub id: i64,
    pub order_id: i64,
    pub product_id: i64,
    pub quantity: i32,
    pub unit_price: Decimal,
    pub subtotal: Decimal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderDetails {
    pub order: Order,
    pub items: Vec<OrderItemDetails>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderItemDetails {
    pub item: OrderItem,
    pub product_name: String,
    pub product_nhi_code: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateOrderRequest {
    pub items: Vec<CreateOrderItem>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateOrderItem {
    pub product_id: i64,
    pub quantity: i32,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize, Default)]
pub struct OrderFilter {
    pub status: Option<OrderStatus>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

#[allow(dead_code)]
impl OrderStatus {
    pub fn can_transition_to(&self, new_status: &OrderStatus) -> bool {
        use OrderStatus::*;
        match (self, new_status) {
            (待處理, 揀貨中) => true,
            (待處理, 已出貨) => true,
            (揀貨中, 已出貨) => true,
            _ => false,
        }
    }

    pub fn is_final(&self) -> bool {
        matches!(self, OrderStatus::已出貨)
    }
}

#[allow(dead_code)]
impl Order {
    pub fn new(user_id: i64, items: Vec<OrderItem>) -> ValidationResult<Self> {
        if items.is_empty() {
            return Err(ValidationError::Required {
                field: "items".to_string(),
            });
        }

        let total_amount = items.iter().map(|item| item.subtotal).sum();
        validate_positive_decimal(&total_amount, "total_amount")?;

        let now = Utc::now();
        Ok(Self {
            id: 0, // Will be set by database
            order_number: Self::generate_order_number(),
            user_id,
            status: OrderStatus::待處理,
            total_amount,
            notes: None,
            created_at: now,
            updated_at: now,
        })
    }

    pub fn generate_order_number() -> String {
        let now = Utc::now();
        let uuid_short = Uuid::new_v4().to_string().split('-').next().unwrap().to_uppercase();
        format!("ORD-{}-{}", now.format("%Y%m%d"), uuid_short)
    }

    pub fn update_status(&mut self, new_status: OrderStatus) -> ValidationResult<()> {
        if !self.status.can_transition_to(&new_status) {
            return Err(ValidationError::InvalidValue {
                field: "status".to_string(),
                value: format!("{:?}", new_status),
            });
        }

        self.status = new_status;
        self.updated_at = Utc::now();
        Ok(())
    }

    pub fn recalculate_total(&mut self, items: &[OrderItem]) {
        self.total_amount = items.iter().map(|item| item.subtotal).sum();
        self.updated_at = Utc::now();
    }

    pub fn is_modifiable(&self) -> bool {
        matches!(self.status, OrderStatus::待處理)
    }

    pub fn is_cancellable(&self) -> bool {
        !self.status.is_final()
    }
}

#[allow(dead_code)]
impl OrderItem {
    pub fn new(product_id: i64, quantity: i32, unit_price: Decimal) -> ValidationResult<Self> {
        validate_non_negative_i32(quantity, "quantity")?;
        if quantity == 0 {
            return Err(ValidationError::Required {
                field: "quantity".to_string(),
            });
        }

        validate_positive_decimal(&unit_price, "unit_price")?;

        let subtotal = (unit_price * Decimal::from(quantity)).round_dp(0);

        Ok(Self {
            id: 0, // Will be set by database
            order_id: 0, // Will be set when added to order
            product_id,
            quantity,
            unit_price,
            subtotal,
        })
    }

    pub fn update_quantity(&mut self, new_quantity: i32) -> ValidationResult<()> {
        validate_non_negative_i32(new_quantity, "quantity")?;
        if new_quantity == 0 {
            return Err(ValidationError::Required {
                field: "quantity".to_string(),
            });
        }

        self.quantity = new_quantity;
        self.subtotal = (self.unit_price * Decimal::from(new_quantity)).round_dp(0);
        Ok(())
    }

    pub fn update_price(&mut self, new_price: Decimal) -> ValidationResult<()> {
        validate_positive_decimal(&new_price, "unit_price")?;
        self.unit_price = new_price;
        self.subtotal = (new_price * Decimal::from(self.quantity)).round_dp(0);
        Ok(())
    }
}

impl Validate for Order {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if self.user_id <= 0 {
            errors.push(ValidationError::Required {
                field: "user_id".to_string(),
            });
        }

        if let Err(e) = validate_positive_decimal(&self.total_amount, "total_amount") {
            errors.push(e);
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

impl Validate for OrderItem {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if self.product_id <= 0 {
            errors.push(ValidationError::Required {
                field: "product_id".to_string(),
            });
        }

        if let Err(e) = validate_non_negative_i32(self.quantity, "quantity") {
            errors.push(e);
        }

        if self.quantity == 0 {
            errors.push(ValidationError::Required {
                field: "quantity".to_string(),
            });
        }

        if let Err(e) = validate_positive_decimal(&self.unit_price, "unit_price") {
            errors.push(e);
        }

        if let Err(e) = validate_positive_decimal(&self.subtotal, "subtotal") {
            errors.push(e);
        }

        // Verify subtotal calculation
        let expected_subtotal = (self.unit_price * Decimal::from(self.quantity)).round_dp(0);
        if (self.subtotal - expected_subtotal).abs() > Decimal::from_f64_retain(0.01).unwrap() {
            errors.push(ValidationError::InvalidValue {
                field: "subtotal".to_string(),
                value: self.subtotal.to_string(),
            });
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

impl Validate for CreateOrderRequest {
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if self.items.is_empty() {
            errors.push(ValidationError::Required {
                field: "items".to_string(),
            });
        }

        for (index, item) in self.items.iter().enumerate() {
            if item.product_id <= 0 {
                errors.push(ValidationError::Required {
                    field: format!("items[{}].product_id", index),
                });
            }

            if let Err(e) = validate_non_negative_i32(item.quantity, &format!("items[{}].quantity", index)) {
                errors.push(e);
            }

            if item.quantity == 0 {
                errors.push(ValidationError::Required {
                    field: format!("items[{}].quantity", index),
                });
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[test]
    fn test_order_status_transitions() {
        // 測試有效的狀態轉換
        assert!(OrderStatus::Pending.can_transition_to(&OrderStatus::Confirmed));
        assert!(OrderStatus::Pending.can_transition_to(&OrderStatus::Cancelled));
        assert!(OrderStatus::Confirmed.can_transition_to(&OrderStatus::Processing));
        assert!(OrderStatus::Processing.can_transition_to(&OrderStatus::Shipped));
        assert!(OrderStatus::Shipped.can_transition_to(&OrderStatus::Delivered));

        // 測試無效的狀態轉換
        assert!(!OrderStatus::Delivered.can_transition_to(&OrderStatus::Processing));
        assert!(!OrderStatus::Cancelled.can_transition_to(&OrderStatus::Confirmed));
        assert!(!OrderStatus::Shipped.can_transition_to(&OrderStatus::Processing));
    }

    #[test]
    fn test_order_status_final() {
        assert!(OrderStatus::Delivered.is_final());
        assert!(OrderStatus::Cancelled.is_final());
        assert!(!OrderStatus::Pending.is_final());
        assert!(!OrderStatus::Confirmed.is_final());
        assert!(!OrderStatus::Processing.is_final());
        assert!(!OrderStatus::Shipped.is_final());
    }

    #[test]
    fn test_order_generate_order_number() {
        let order_number = Order::generate_order_number();
        assert!(order_number.starts_with("ORD-"));
        assert!(order_number.len() > 10);
    }

    #[test]
    fn test_order_new() {
        let items = vec![
            OrderItem::new(1, 2, dec!(50.0)).unwrap(),
            OrderItem::new(2, 1, dec!(30.0)).unwrap(),
        ];

        let order = Order::new(1, items).unwrap();
        assert_eq!(order.user_id, 1);
        assert_eq!(order.status, OrderStatus::Pending);
        assert_eq!(order.total_amount, dec!(130)); // 2*50 + 1*30
        assert!(order.order_number.starts_with("ORD-"));
    }

    #[test]
    fn test_order_new_empty_items() {
        let items = vec![];
        let result = Order::new(1, items);
        assert!(result.is_err());
    }

    #[test]
    fn test_order_update_status() {
        let items = vec![OrderItem::new(1, 2, dec!(50.0)).unwrap()];
        let mut order = Order::new(1, items).unwrap();

        // 測試有效狀態更新
        assert!(order.update_status(OrderStatus::Confirmed).is_ok());
        assert_eq!(order.status, OrderStatus::Confirmed);

        // 測試無效狀態更新
        assert!(order.update_status(OrderStatus::Delivered).is_err());
        assert_eq!(order.status, OrderStatus::Confirmed); // 狀態未改變
    }

    #[test]
    fn test_order_modifiability() {
        let items = vec![OrderItem::new(1, 2, dec!(50.0)).unwrap()];
        let order = Order::new(1, items).unwrap();
        assert!(order.is_modifiable()); // Pending 狀態可修改

        let mut order = order;
        order.update_status(OrderStatus::Confirmed).unwrap();
        assert!(!order.is_modifiable()); // Confirmed 狀態不可修改
    }

    #[test]
    fn test_order_cancellability() {
        let items = vec![OrderItem::new(1, 2, dec!(50.0)).unwrap()];
        let mut order = Order::new(1, items).unwrap();
        assert!(order.is_cancellable()); // Pending 狀態可取消

        order.update_status(OrderStatus::Confirmed).unwrap();
        assert!(order.is_cancellable()); // Confirmed 狀態可取消

        order.update_status(OrderStatus::Processing).unwrap();
        assert!(order.is_cancellable()); // Processing 狀態可取消

        order.update_status(OrderStatus::Shipped).unwrap();
        assert!(!order.is_cancellable()); // Shipped 狀態不可取消

        order.update_status(OrderStatus::Delivered).unwrap();
        assert!(!order.is_cancellable()); // Delivered 狀態不可取消
    }

    #[test]
    fn test_order_item_new() {
        let item = OrderItem::new(1, 2, dec!(50.0)).unwrap();
        assert_eq!(item.product_id, 1);
        assert_eq!(item.quantity, 2);
        assert_eq!(item.unit_price, dec!(50.0));
        assert_eq!(item.subtotal, dec!(100)); // 2 * 50
    }

    #[test]
    fn test_order_item_new_invalid() {
        // 測試零數量
        assert!(OrderItem::new(1, 0, dec!(50.0)).is_err());
        
        // 測試負數量
        assert!(OrderItem::new(1, -1, dec!(50.0)).is_err());
        
        // 測試負價格
        assert!(OrderItem::new(1, 2, dec!(-50.0)).is_err());
    }

    #[test]
    fn test_order_item_update_quantity() {
        let mut item = OrderItem::new(1, 2, dec!(50.0)).unwrap();
        
        // 測試有效數量更新
        assert!(item.update_quantity(3).is_ok());
        assert_eq!(item.quantity, 3);
        assert_eq!(item.subtotal, dec!(150)); // 3 * 50

        // 測試無效數量更新
        assert!(item.update_quantity(0).is_err());
        assert!(item.update_quantity(-1).is_err());
    }

    #[test]
    fn test_order_item_update_price() {
        let mut item = OrderItem::new(1, 2, dec!(50.0)).unwrap();
        
        // 測試有效價格更新
        assert!(item.update_price(dec!(60.0)).is_ok());
        assert_eq!(item.unit_price, dec!(60.0));
        assert_eq!(item.subtotal, dec!(120)); // 2 * 60

        // 測試無效價格更新
        assert!(item.update_price(dec!(-10.0)).is_err());
    }

    #[test]
    fn test_order_validation() {
        let items = vec![OrderItem::new(1, 2, dec!(50.0)).unwrap()];
        let order = Order::new(1, items).unwrap();
        assert!(order.validate().is_ok());
    }

    #[test]
    fn test_order_item_validation() {
        let item = OrderItem::new(1, 2, dec!(50.0)).unwrap();
        assert!(item.validate().is_ok());
    }

    #[test]
    fn test_create_order_request_validation() {
        let request = CreateOrderRequest {
            items: vec![
                CreateOrderItem { product_id: 1, quantity: 2 },
                CreateOrderItem { product_id: 2, quantity: 1 },
            ],
        };
        assert!(request.validate().is_ok());
    }

    #[test]
    fn test_create_order_request_validation_failure() {
        // 測試空項目列表
        let request = CreateOrderRequest { items: vec![] };
        assert!(request.validate().is_err());

        // 測試無效項目
        let request = CreateOrderRequest {
            items: vec![
                CreateOrderItem { product_id: 0, quantity: 2 }, // 無效產品ID
                CreateOrderItem { product_id: 2, quantity: 0 }, // 零數量
                CreateOrderItem { product_id: 3, quantity: -1 }, // 負數量
            ],
        };
        let errors = request.validate().unwrap_err();
        assert!(errors.len() >= 3);
    }

    #[test]
    fn test_order_number_uniqueness() {
        // 測試訂單編號的唯一性
        let order_number1 = Order::generate_order_number();
        let order_number2 = Order::generate_order_number();
        
        // 訂單編號應該不同（因為包含UUID）
        assert_ne!(order_number1, order_number2);
        
        // 都應該符合格式 ORD-YYYYMMDD-XXXXXXXX
        assert!(order_number1.starts_with("ORD-"));
        assert!(order_number2.starts_with("ORD-"));
        assert!(order_number1.len() > 15);
        assert!(order_number2.len() > 15);
    }

    #[test]
    fn test_order_recalculate_total() {
        let items = vec![
            OrderItem::new(1, 2, dec!(50.0)).unwrap(),
            OrderItem::new(2, 1, dec!(30.0)).unwrap(),
        ];
        let mut order = Order::new(1, items.clone()).unwrap();
        
        // 初始總額應該是 130.0
        assert_eq!(order.total_amount, dec!(130));
        
        // 修改項目數量後重新計算
        let mut updated_items = items;
        updated_items[0].update_quantity(3).unwrap(); // 2*50 -> 3*50
        
        order.recalculate_total(&updated_items);
        assert_eq!(order.total_amount, dec!(180)); // 3*50 + 1*30
    }

    #[test]
    fn test_order_status_business_rules() {
        let items = vec![OrderItem::new(1, 2, dec!(50.0)).unwrap()];
        let mut order = Order::new(1, items).unwrap();
        
        // 測試完整的狀態轉換流程
        assert_eq!(order.status, OrderStatus::Pending);
        assert!(order.is_modifiable());
        assert!(order.is_cancellable());
        
        // Pending -> Confirmed
        assert!(order.update_status(OrderStatus::Confirmed).is_ok());
        assert!(!order.is_modifiable());
        assert!(order.is_cancellable());
        
        // Confirmed -> Processing
        assert!(order.update_status(OrderStatus::Processing).is_ok());
        assert!(!order.is_modifiable());
        assert!(order.is_cancellable());
        
        // Processing -> Shipped
        assert!(order.update_status(OrderStatus::Shipped).is_ok());
        assert!(!order.is_modifiable());
        assert!(!order.is_cancellable()); // Shipped 不可取消
        
        // Shipped -> Delivered
        assert!(order.update_status(OrderStatus::Delivered).is_ok());
        assert!(!order.is_modifiable());
        assert!(!order.is_cancellable());
        assert!(order.status.is_final());
    }

    #[test]
    fn test_order_item_subtotal_precision() {
        // 測試小數計算精度
        let item = OrderItem::new(1, 3, dec!(33.33)).unwrap();
        assert_eq!(item.subtotal, dec!(100)); // 3 * 33.33 rounded
        
        // 測試價格更新後的精度
        let mut item = OrderItem::new(1, 7, dec!(14.29)).unwrap();
        assert_eq!(item.subtotal, dec!(100)); // 7 * 14.29 rounded
        
        item.update_price(dec!(14.28)).unwrap();
        assert_eq!(item.subtotal, dec!(100)); // 7 * 14.28 rounded
    }

    #[test]
    fn test_order_with_large_quantities() {
        // 測試大數量訂單
        let item = OrderItem::new(1, 1000, dec!(1.50)).unwrap();
        assert_eq!(item.subtotal, dec!(1500.0));
        
        let items = vec![item];
        let order = Order::new(1, items).unwrap();
        assert_eq!(order.total_amount, dec!(1500));
    }

    #[test]
    fn test_order_status_from_string() {
        // 測試字串轉換為訂單狀態
        assert_eq!(OrderStatus::from("Pending".to_string()), OrderStatus::Pending);
        assert_eq!(OrderStatus::from("Confirmed".to_string()), OrderStatus::Confirmed);
        assert_eq!(OrderStatus::from("Processing".to_string()), OrderStatus::Processing);
        assert_eq!(OrderStatus::from("Shipped".to_string()), OrderStatus::Shipped);
        assert_eq!(OrderStatus::from("Delivered".to_string()), OrderStatus::Delivered);
        assert_eq!(OrderStatus::from("Cancelled".to_string()), OrderStatus::Cancelled);
        
        // 測試無效狀態回到預設值
        assert_eq!(OrderStatus::from("Invalid".to_string()), OrderStatus::Pending);
    }

    #[test]
    fn test_order_item_validation_edge_cases() {
        // 測試邊界值驗證
        let item = OrderItem {
            id: 1,
            order_id: 1,
            product_id: 1,
            quantity: 1,
            unit_price: dec!(0.01), // 最小正數價格
            subtotal: dec!(0.01),
        };
        assert!(item.validate().is_ok());
        
        // 測試小數計算錯誤的情況
        let invalid_item = OrderItem {
            id: 1,
            order_id: 1,
            product_id: 1,
            quantity: 2,
            unit_price: dec!(10.0),
            subtotal: dec!(19.0), // 錯誤的小計 (應該是 20.0)
        };
        assert!(invalid_item.validate().is_err());
    }
}