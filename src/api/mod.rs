pub mod auth;
pub mod products;
pub mod orders;
pub mod cart;
pub mod middleware;
pub mod backup;
pub mod notifications;
pub mod response;
pub mod rate_limit;
pub mod roles_simple;
pub mod promotions;
pub mod messages;

use axum::{
    middleware::{from_fn, from_fn_with_state},
    routing::get,
    Router,
};
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
    services::ServeDir,
};
use std::time::Duration;

use crate::api::{
    auth::{public_auth_routes, protected_auth_routes, AppState},
    products::{public_product_routes, protected_product_routes},
    orders::order_routes,
    cart::cart_routes,
    backup::backup_routes,
    notifications::notification_routes,
    roles_simple::role_routes,
    promotions::promotion_routes,
    messages::message_routes,
    middleware::{logging_middleware, error_handling_middleware},
};

/// 建立完整的 API 路由器
pub fn create_api_router(state: AppState) -> Router {
    // 建立 CORS 層
    let cors = CorsLayer::new()
        .allow_origin([
            "https://office.2guide.org".parse::<axum::http::HeaderValue>().unwrap(),
            "http://office.2guide.org".parse::<axum::http::HeaderValue>().unwrap(),
            "http://localhost:8080".parse::<axum::http::HeaderValue>().unwrap(),
            "http://127.0.0.1:8080".parse::<axum::http::HeaderValue>().unwrap(),
        ])
        .allow_methods([
            axum::http::Method::GET,
            axum::http::Method::POST,
            axum::http::Method::PUT,
            axum::http::Method::DELETE,
            axum::http::Method::OPTIONS,
        ])
        .allow_headers([
            axum::http::header::CONTENT_TYPE,
            axum::http::header::AUTHORIZATION,
        ])
        .allow_credentials(true)
        .max_age(Duration::from_secs(3600));

    // 建立追蹤層
    let trace_layer = TraceLayer::new_for_http()
        .make_span_with(|request: &axum::http::Request<_>| {
            tracing::info_span!(
                "http_request",
                method = %request.method(),
                uri = %request.uri(),
                version = ?request.version(),
            )
        })
        .on_request(|_request: &axum::http::Request<_>, _span: &tracing::Span| {
            tracing::info!("started processing request")
        })
        .on_response(|_response: &axum::http::Response<_>, latency: Duration, _span: &tracing::Span| {
            tracing::info!("finished processing request in {:?}", latency)
        });

    let protected_routes = Router::new()
        .nest("/api/auth", protected_auth_routes())
        .nest("/api/products-admin", protected_product_routes())
        .nest("/api/orders", order_routes())
        .merge(cart_routes()) 
        .nest("/api/backup", backup_routes())
        .nest("/api/notifications", notification_routes())
        .nest("/api/admin", role_routes())
        .nest("/api/promotions", promotion_routes())
        .nest("/api/messages", message_routes())
        .layer(from_fn_with_state(
            state.clone(),
            middleware::auth_middleware,
        ));

    Router::new()
        // 健康檢查端點
        .route("/health", get(health_check))
        .route("/health/detailed", get(detailed_health_check))
        .route("/api", get(api_info))
        
        // 公開 API 路由（不需要認證）
        .nest("/api/auth", public_auth_routes())
        .nest("/api/products", public_product_routes())
        
        // 受保護的 API 路由（需要認證）
        .merge(protected_routes)
        
        // 靜態檔案服務
        .nest_service("/", ServeDir::new("web"))
        
        // 中介軟體層（按順序應用）
        .layer(from_fn(error_handling_middleware))
        .layer(cors)
        .layer(trace_layer)
        .layer(from_fn_with_state(
            state.clone(),
            logging_middleware,
        ))
        
        // 應用狀態
        .with_state(state)
}

/// 健康檢查端點
async fn health_check() -> axum::Json<serde_json::Value> {
    use crate::logging::structured_logging;
    use std::time::Instant;
    
    let start_time = Instant::now();
    let mut overall_status = "healthy";
    let mut checks = serde_json::Map::new();
    
    // 檢查系統時間
    let system_time_check = Instant::now();
    checks.insert("system_time".to_string(), serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now(),
        "response_time_ms": system_time_check.elapsed().as_millis()
    }));
    
    // 檢查記憶體使用情況（簡單檢查）
    let memory_check_start = Instant::now();
    let memory_status = "healthy"; // 這裡可以添加實際的記憶體檢查邏輯
    checks.insert("memory".to_string(), serde_json::json!({
        "status": memory_status,
        "response_time_ms": memory_check_start.elapsed().as_millis()
    }));
    
    // 檢查檔案系統
    let fs_check_start = Instant::now();
    let fs_status = match std::fs::metadata(".") {
        Ok(_) => "healthy",
        Err(_) => {
            overall_status = "degraded";
            "unhealthy"
        }
    };
    checks.insert("filesystem".to_string(), serde_json::json!({
        "status": fs_status,
        "response_time_ms": fs_check_start.elapsed().as_millis()
    }));
    
    let total_response_time = start_time.elapsed();
    
    // 記錄健康檢查
    structured_logging::log_health_check(
        "system",
        overall_status,
        total_response_time.as_millis() as u64,
        Some(serde_json::json!({
            "checks_count": checks.len(),
            "all_healthy": overall_status == "healthy"
        }))
    );
    
    axum::Json(serde_json::json!({
        "status": overall_status,
        "timestamp": chrono::Utc::now(),
        "version": env!("CARGO_PKG_VERSION"),
        "response_time_ms": total_response_time.as_millis(),
        "checks": checks,
        "uptime": {
            "process_start": chrono::Utc::now(), // 這裡可以記錄實際的程序啟動時間
            "current_time": chrono::Utc::now()
        }
    }))
}

/// API 資訊端點
async fn api_info() -> axum::Json<serde_json::Value> {
    axum::Json(serde_json::json!({
        "name": "Pharmacy Procurement System API",
        "version": env!("CARGO_PKG_VERSION"),
        "description": "藥品中盤商採購系統 API",
        "endpoints": {
            "auth": "/api/auth",
            "products": "/api/products", 
            "orders": "/api/orders",
            "cart": "/api/cart",
            "backup": "/api/backup",
            "notifications": "/api/notifications",
            "admin": "/api/admin",
            "promotions": "/api/promotions",
            "messages": "/api/messages"
        },
        "health": "/health"
    }))
}
/// 詳細健康檢查端點(包含資料庫連線檢查)
async fn detailed_health_check(
    axum::extract::State(state): axum::extract::State<AppState>,
) -> axum::Json<serde_json::Value> {
    use crate::logging::structured_logging;
    use std::time::Instant;
    
    let start_time = Instant::now();
    let mut overall_status = "healthy";
    let mut checks = serde_json::Map::new();
    
    // 檢查資料庫連線
    let db_check_start = Instant::now();
    let db_status = match sqlx::query("SELECT 1").fetch_one(&*state.db).await {
        Ok(_) => "healthy",
        Err(e) => {
            tracing::error!("Database health check failed: {}", e);
            overall_status = "unhealthy";
            "unhealthy"
        }
    };
    checks.insert("database".to_string(), serde_json::json!({
        "status": db_status,
        "response_time_ms": db_check_start.elapsed().as_millis(),
        "type": "postgresql"
    }));
    
    // 檢查認證服務
    let auth_check_start = Instant::now();
    let auth_status = "healthy"; // 認證服務通常不需要外部依賴
    checks.insert("auth_service".to_string(), serde_json::json!({
        "status": auth_status,
        "response_time_ms": auth_check_start.elapsed().as_millis()
    }));
    
    // 檢查檔案系統（備份目錄）
    let backup_dir_check_start = Instant::now();
    let backup_dir = std::env::var("BACKUP_DIR").unwrap_or_else(|_| "./backups".to_string());
    let backup_dir_status = match tokio::fs::metadata(&backup_dir).await {
        Ok(metadata) if metadata.is_dir() => "healthy",
        Ok(_) => {
            overall_status = "degraded";
            "degraded"
        }
        Err(_) => {
            // 嘗試建立目錄
            match tokio::fs::create_dir_all(&backup_dir).await {
                Ok(_) => "healthy",
                Err(_) => {
                    overall_status = "degraded";
                    "unhealthy"
                }
            }
        }
    };
    checks.insert("backup_directory".to_string(), serde_json::json!({
        "status": backup_dir_status,
        "path": backup_dir,
        "response_time_ms": backup_dir_check_start.elapsed().as_millis()
    }));
    
    // 檢查 GCP 連線（如果配置了）
    let gcp_check_start = Instant::now();
    let gcp_status = if std::env::var("GCP_PROJECT_ID").is_ok() {
        // 這裡可以添加實際的 GCP 連線檢查
        "healthy"
    } else {
        "not_configured"
    };
    checks.insert("gcp_services".to_string(), serde_json::json!({
        "status": gcp_status,
        "response_time_ms": gcp_check_start.elapsed().as_millis()
    }));
    
    // 檢查外部服務（Email SMTP）
    let email_check_start = Instant::now();
    let email_status = if state.config.email.smtp_host.is_empty() {
        "not_configured"
    } else {
        // 這裡可以添加實際的 SMTP 連線檢查
        "unknown"
    };
    checks.insert("email_service".to_string(), serde_json::json!({
        "status": email_status,
        "smtp_host": state.config.email.smtp_host,
        "response_time_ms": email_check_start.elapsed().as_millis()
    }));
    
    // 檢查 Line Bot API
    let line_check_start = Instant::now();
    let line_status = if state.config.line.channel_access_token.is_empty() {
        "not_configured"
    } else {
        // 這裡可以添加實際的 Line API 連線檢查
        "unknown"
    };
    checks.insert("line_service".to_string(), serde_json::json!({
        "status": line_status,
        "response_time_ms": line_check_start.elapsed().as_millis()
    }));
    
    let total_response_time = start_time.elapsed();
    
    // 記錄詳細健康檢查
    structured_logging::log_health_check(
        "detailed_system",
        overall_status,
        total_response_time.as_millis() as u64,
        Some(serde_json::json!({
            "checks_count": checks.len(),
            "healthy_checks": checks.values().filter(|v| v["status"] == "healthy").count(),
            "unhealthy_checks": checks.values().filter(|v| v["status"] == "unhealthy").count(),
            "degraded_checks": checks.values().filter(|v| v["status"] == "degraded").count()
        }))
    );
    
    axum::Json(serde_json::json!({
        "status": overall_status,
        "timestamp": chrono::Utc::now(),
        "version": env!("CARGO_PKG_VERSION"),
        "response_time_ms": total_response_time.as_millis(),
        "checks": checks,
        "summary": {
            "total_checks": checks.len(),
            "healthy": checks.values().filter(|v| v["status"] == "healthy").count(),
            "unhealthy": checks.values().filter(|v| v["status"] == "unhealthy").count(),
            "degraded": checks.values().filter(|v| v["status"] == "degraded").count(),
            "not_configured": checks.values().filter(|v| v["status"] == "not_configured").count(),
            "unknown": checks.values().filter(|v| v["status"] == "unknown").count()
        },
        "uptime": {
            "process_start": chrono::Utc::now(), // 這裡可以記錄實際的程序啟動時間
            "current_time": chrono::Utc::now()
        }
    }))
}