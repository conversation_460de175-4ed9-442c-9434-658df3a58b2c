use axum::{
    extract::{Multipart, Query, Path, State},
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

use crate::{
    error::{AppError, AppResult},
    models::product::{ProductFilter, ImportResult},
    api::response::ApiResponse,
};

#[derive(Debug, Deserialize)]
pub struct ProductQueryParams {
    pub search: Option<String>,
    pub manufacturer: Option<String>,
    pub is_active: Option<bool>,
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

#[derive(Debug, Serialize)]
pub struct ProductListResponse {
    pub products: Vec<crate::models::product::Product>,
    pub total: usize,
    pub page: u32,
    pub limit: u32,
}

#[derive(Debug, Serialize)]
pub struct ImportResponse {
    pub success: bool,
    pub result: ImportResult,
}

// 公開的產品路由（不需要認證）
pub fn public_product_routes() -> Router<crate::api::auth::AppState> {
    Router::new()
        .route("/", get(list_products))
        .route("/nhi/:nhi_code", get(get_product_by_nhi_code))
        .route("/:id", get(get_product))
}

// 受保護的產品路由（需要認證）
pub fn protected_product_routes() -> Router<crate::api::auth::AppState> {
    Router::new()
        .route("/low-stock", get(get_low_stock_products))
        .route("/import/excel", post(import_excel))
        .route("/import/csv", post(import_csv))
        .route("/:id/stock", post(update_stock))
        .route("/:id/stock/status", get(get_stock_status))
        .route("/:id/stock/check", post(check_stock_availability))
        .route("/stock/bulk-update", post(bulk_update_stock))
}

// 為了向下相容，保留原來的函數
#[allow(dead_code)]
pub fn product_routes() -> Router<crate::api::auth::AppState> {
    protected_product_routes()
}

/// 取得產品清單
async fn list_products(
    Query(params): Query<ProductQueryParams>,
    State(state): State<crate::api::auth::AppState>,
) -> AppResult<Json<ApiResponse<Vec<crate::models::product::Product>>>> {
    let filter = if params.search.is_some() || params.manufacturer.is_some() || params.is_active.is_some() {
        Some(ProductFilter {
            search: params.search,
            manufacturer: params.manufacturer,
            is_active: params.is_active,
            page: params.page,
            limit: params.limit,
        })
    } else {
        None
    };

    let page = params.page.unwrap_or(1);
    let limit = params.limit.unwrap_or(50);

    let products = state.product_service.get_products(filter, Some(page), Some(limit)).await?;

    Ok(Json(ApiResponse::success(products)))
}

/// 根據 ID 取得產品
async fn get_product(
    Path(id): Path<i64>,
    State(state): State<crate::api::auth::AppState>,
) -> AppResult<Json<Value>> {
    match state.product_service.get_product_by_id(id).await? {
        Some(product) => Ok(Json(json!({ "product": product }))),
        None => Err(AppError::NotFound(format!("Product with id {} not found", id))),
    }
}

/// 根據健保代碼取得產品
async fn get_product_by_nhi_code(
    Path(nhi_code): Path<String>,
    State(state): State<crate::api::auth::AppState>,
) -> AppResult<Json<Value>> {
    match state.product_service.get_product_by_nhi_code(&nhi_code).await? {
        Some(product) => Ok(Json(json!({ "product": product }))),
        None => Err(AppError::NotFound(format!("Product with NHI code {} not found", nhi_code))),
    }
}

/// 匯入 Excel 檔案
async fn import_excel(
    State(state): State<crate::api::auth::AppState>,
    mut multipart: Multipart,
) -> AppResult<Json<ImportResponse>> {
    let mut file_data: Option<Vec<u8>> = None;

    while let Some(field) = multipart.next_field().await.map_err(|e| {
        AppError::FileProcessing(format!("Failed to read multipart field: {}", e))
    })? {
        let name = field.name().unwrap_or("");
        
        if name == "file" {
            let content_type = field.content_type().unwrap_or("");
            
            // 驗證檔案類型
            if !content_type.contains("spreadsheet") && !content_type.contains("excel") {
                return Err(AppError::FileProcessing(
                    "Invalid file type. Please upload an Excel file (.xlsx, .xls)".to_string()
                ));
            }

            let data = field.bytes().await.map_err(|e| {
                AppError::FileProcessing(format!("Failed to read file data: {}", e))
            })?;

            file_data = Some(data.to_vec());
            break;
        }
    }

    let file_data = file_data.ok_or_else(|| {
        AppError::FileProcessing("No file found in request".to_string())
    })?;

    tracing::info!("Processing Excel file upload, size: {} bytes", file_data.len());

    let result = state.product_service.import_from_excel(file_data).await?;

    Ok(Json(ImportResponse {
        success: result.error_count == 0,
        result,
    }))
}

/// 匯入 CSV 檔案
async fn import_csv(
    State(state): State<crate::api::auth::AppState>,
    mut multipart: Multipart,
) -> AppResult<Json<ImportResponse>> {
    let mut file_data: Option<Vec<u8>> = None;

    while let Some(field) = multipart.next_field().await.map_err(|e| {
        AppError::FileProcessing(format!("Failed to read multipart field: {}", e))
    })? {
        let name = field.name().unwrap_or("");
        
        if name == "file" {
            let content_type = field.content_type().unwrap_or("");
            
            // 驗證檔案類型
            if !content_type.contains("csv") && !content_type.contains("text") {
                return Err(AppError::FileProcessing(
                    "Invalid file type. Please upload a CSV file (.csv)".to_string()
                ));
            }

            let data = field.bytes().await.map_err(|e| {
                AppError::FileProcessing(format!("Failed to read file data: {}", e))
            })?;

            file_data = Some(data.to_vec());
            break;
        }
    }

    let file_data = file_data.ok_or_else(|| {
        AppError::FileProcessing("No file found in request".to_string())
    })?;

    tracing::info!("Processing CSV file upload, size: {} bytes", file_data.len());

    let result = state.product_service.import_from_csv(file_data).await?;

    Ok(Json(ImportResponse {
        success: result.error_count == 0,
        result,
    }))
}

/// 取得低庫存產品
async fn get_low_stock_products(
    State(state): State<crate::api::auth::AppState>,
) -> AppResult<Json<Value>> {
    let products = state.product_service.get_low_stock_products().await?;
    Ok(Json(json!({ "products": products })))
}

/// 更新產品庫存
#[derive(Debug, Deserialize)]
struct UpdateStockRequest {
    quantity: i32,
}

async fn update_stock(
    Path(id): Path<i64>,
    State(state): State<crate::api::auth::AppState>,
    Json(request): Json<UpdateStockRequest>,
) -> AppResult<Json<Value>> {
    let product = state.product_service.update_stock(id, request.quantity).await?;
    Ok(Json(json!({ "product": product })))
}

/// 取得產品庫存狀態
async fn get_stock_status(
    Path(id): Path<i64>,
    State(state): State<crate::api::auth::AppState>,
) -> AppResult<Json<Value>> {
    let status = state.product_service.get_stock_status(id).await?;
    Ok(Json(json!({ "stock_status": status })))
}

/// 檢查庫存可用性
#[derive(Debug, Deserialize)]
struct CheckStockRequest {
    required_quantity: i32,
}

async fn check_stock_availability(
    Path(id): Path<i64>,
    State(state): State<crate::api::auth::AppState>,
    Json(request): Json<CheckStockRequest>,
) -> AppResult<Json<Value>> {
    let is_available = state.product_service.check_stock_availability(id, request.required_quantity).await?;
    Ok(Json(json!({ 
        "product_id": id,
        "required_quantity": request.required_quantity,
        "is_available": is_available 
    })))
}

/// 批量更新庫存
#[derive(Debug, Deserialize)]
struct BulkUpdateStockRequest {
    updates: Vec<crate::services::product::StockUpdate>,
}

async fn bulk_update_stock(
    State(state): State<crate::api::auth::AppState>,
    Json(request): Json<BulkUpdateStockRequest>,
) -> AppResult<Json<Value>> {
    let updated_products = state.product_service.bulk_update_stock(request.updates).await?;
    Ok(Json(json!({ "updated_products": updated_products })))
}

