use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
    routing::get,
    Router,
};
use serde_json::json;

use crate::api::{
    auth::AppState,
    response::{ApiResponse},
};

/// 簡化的角色管理路由
pub fn role_routes() -> Router<AppState> {
    Router::new()
        .route("/roles", get(get_all_roles))
        .route("/permissions", get(get_all_permissions))
        .route("/user-permissions", get(get_user_permissions))
}

/// 獲取所有角色
async fn get_all_roles(
    State(_state): State<AppState>,
) -> Result<Json<ApiResponse<serde_json::Value>>, (StatusCode, String)> {
    // 暫時返回模擬數據
    let roles = json!([
        {
            "id": 1,
            "name": "super_admin",
            "description": "超級管理員 - 擁有所有權限",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": 2,
            "name": "admin",
            "description": "管理員 - 可以管理產品和訂單",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": 3,
            "name": "pharmacy",
            "description": "藥局用戶 - 可以下訂單和查看自己的資料",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": 4,
            "name": "viewer",
            "description": "檢視者 - 只能查看資料",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    ]);
    
    Ok(Json(ApiResponse::success(roles)))
}

/// 獲取所有權限
async fn get_all_permissions(
    State(_state): State<AppState>,
) -> Result<Json<ApiResponse<serde_json::Value>>, (StatusCode, String)> {
    // 暫時返回模擬數據
    let permissions = json!({
        "products": [
            {
                "id": 1,
                "name": "products.create",
                "description": "創建產品",
                "resource": "products",
                "action": "create"
            },
            {
                "id": 2,
                "name": "products.read",
                "description": "查看產品",
                "resource": "products",
                "action": "read"
            },
            {
                "id": 3,
                "name": "products.update",
                "description": "更新產品",
                "resource": "products",
                "action": "update"
            },
            {
                "id": 4,
                "name": "products.delete",
                "description": "刪除產品",
                "resource": "products",
                "action": "delete"
            }
        ],
        "orders": [
            {
                "id": 5,
                "name": "orders.create",
                "description": "創建訂單",
                "resource": "orders",
                "action": "create"
            },
            {
                "id": 6,
                "name": "orders.read",
                "description": "查看訂單",
                "resource": "orders",
                "action": "read"
            },
            {
                "id": 7,
                "name": "orders.update",
                "description": "更新訂單",
                "resource": "orders",
                "action": "update"
            },
            {
                "id": 8,
                "name": "orders.delete",
                "description": "刪除訂單",
                "resource": "orders",
                "action": "delete"
            }
        ],
        "users": [
            {
                "id": 9,
                "name": "users.create",
                "description": "創建用戶",
                "resource": "users",
                "action": "create"
            },
            {
                "id": 10,
                "name": "users.read",
                "description": "查看用戶",
                "resource": "users",
                "action": "read"
            },
            {
                "id": 11,
                "name": "users.update",
                "description": "更新用戶",
                "resource": "users",
                "action": "update"
            },
            {
                "id": 12,
                "name": "users.delete",
                "description": "刪除用戶",
                "resource": "users",
                "action": "delete"
            }
        ]
    });
    
    Ok(Json(ApiResponse::success(permissions)))
}

/// 獲取用戶權限
async fn get_user_permissions(
    State(_state): State<AppState>,
) -> Result<Json<ApiResponse<serde_json::Value>>, (StatusCode, String)> {
    // 暫時返回模擬的用戶權限數據
    let user_permissions = json!({
        "role": "admin",
        "permissions": [
            "view_products",
            "manage_products", 
            "view_orders",
            "manage_orders",
            "view_users",
            "manage_users",
            "view_reports",
            "manage_system"
        ],
        "resources": {
            "products": ["read", "write", "delete"],
            "orders": ["read", "write", "delete"],
            "users": ["read", "write"],
            "messages": ["read", "write", "delete"]
        }
    });

    Ok(Json(ApiResponse::success(user_permissions)))
}