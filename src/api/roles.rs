use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use crate::{
    api::{
        auth::AppState,
        middleware::{require_permission_middleware, require_resource_action_middleware},
        response::{ApiResponse, ApiResult},
    },
    models::{Role, Permission, RoleWithPermissions, CreateRoleRequest, UpdateRoleRequest},
    repositories::role::{RoleRepository, PostgresRoleRepository},
};

#[derive(Debug, Deserialize)]
pub struct RoleQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

#[derive(Debug, Deserialize)]
pub struct AssignPermissionsRequest {
    pub permission_ids: Vec<i64>,
}

/// 角色管理路由
pub fn role_routes() -> Router<AppState> {
    Router::new()
        .route("/roles", get(get_all_roles).post(create_role))
        .route("/roles/:id", get(get_role).put(update_role).delete(delete_role))
        .route("/roles/:id/permissions", get(get_role_permissions).post(assign_permissions))
        .route("/roles/permissions", get(get_all_permissions))
        .route("/roles/user-permissions", get(get_current_user_permissions))
        .route("/users/:user_id/permissions", get(get_user_permissions))
        // 權限檢查中間件
        // 權限檢查將在各個端點中單獨處理
}

/// 獲取所有角色
async fn get_all_roles(
    State(state): State<AppState>,
    Query(query): Query<RoleQuery>,
) -> ApiResult<Json<ApiResponse<Vec<Role>>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    match role_repo.find_all_roles().await {
        Ok(roles) => {
            let page = query.page.unwrap_or(1);
            let limit = query.limit.unwrap_or(50);
            let start = ((page - 1) * limit) as usize;
            let end = (start + limit as usize).min(roles.len());
            
            let paginated_roles = if start < roles.len() {
                roles[start..end].to_vec()
            } else {
                vec![]
            };
            
            Ok(Json(ApiResponse::success(paginated_roles)))
        }
        Err(e) => {
            tracing::error!("Failed to fetch roles: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to fetch roles".to_string()))
        }
    }
}

/// 獲取單個角色
async fn get_role(
    State(state): State<AppState>,
    Path(id): Path<i64>,
) -> ApiResult<Json<ApiResponse<RoleWithPermissions>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    match role_repo.find_role_with_permissions(id).await {
        Ok(Some(role)) => Ok(Json(ApiResponse::success(role))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
        Err(e) => {
            tracing::error!("Failed to fetch role: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to fetch role".to_string()))
        }
    }
}

/// 創建新角色
async fn create_role(
    State(state): State<AppState>,
    Json(request): Json<CreateRoleRequest>,
) -> ApiResult<Json<ApiResponse<Role>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    // 檢查角色名稱是否已存在
    match role_repo.find_role_by_name(&request.name).await {
        Ok(Some(_)) => {
            return Err((StatusCode::CONFLICT, "Role name already exists".to_string()));
        }
        Ok(None) => {}
        Err(e) => {
            tracing::error!("Failed to check role existence: {}", e);
            return Err((StatusCode::INTERNAL_SERVER_ERROR, "Database error".to_string()));
        }
    }
    
    match role_repo.create_role(&request.name, request.description.as_deref()).await {
        Ok(role) => {
            // 如果提供了權限ID，分配權限
            if !request.permission_ids.is_empty() {
                if let Err(e) = role_repo.assign_permissions_to_role(role.id, &request.permission_ids).await {
                    tracing::error!("Failed to assign permissions to new role: {}", e);
                    // 角色已創建，但權限分配失敗，記錄錯誤但不回滾
                }
            }
            
            Ok(Json(ApiResponse::success(role)))
        }
        Err(e) => {
            tracing::error!("Failed to create role: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to create role".to_string()))
        }
    }
}

/// 更新角色
async fn update_role(
    State(state): State<AppState>,
    Path(id): Path<i64>,
    Json(request): Json<UpdateRoleRequest>,
) -> ApiResult<Json<ApiResponse<Role>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    // 檢查角色是否存在
    match role_repo.find_role_by_id(id).await {
        Ok(Some(_)) => {}
        Ok(None) => {
            return Err((StatusCode::NOT_FOUND, "Role not found".to_string()));
        }
        Err(e) => {
            tracing::error!("Failed to check role existence: {}", e);
            return Err((StatusCode::INTERNAL_SERVER_ERROR, "Database error".to_string()));
        }
    }
    
    // 如果要更新名稱，檢查新名稱是否已被其他角色使用
    if let Some(ref new_name) = request.name {
        match role_repo.find_role_by_name(new_name).await {
            Ok(Some(existing_role)) if existing_role.id != id => {
                return Err((StatusCode::CONFLICT, "Role name already exists".to_string()));
            }
            Ok(_) => {}
            Err(e) => {
                tracing::error!("Failed to check role name uniqueness: {}", e);
                return Err((StatusCode::INTERNAL_SERVER_ERROR, "Database error".to_string()));
            }
        }
    }
    
    match role_repo.update_role(id, request.name.as_deref(), request.description.as_deref()).await {
        Ok(role) => {
            // 如果提供了權限ID，更新權限
            if let Some(permission_ids) = request.permission_ids {
                if let Err(e) = role_repo.assign_permissions_to_role(role.id, &permission_ids).await {
                    tracing::error!("Failed to update permissions for role: {}", e);
                    // 角色已更新，但權限更新失敗，記錄錯誤但不回滾
                }
            }
            
            Ok(Json(ApiResponse::success(role)))
        }
        Err(e) => {
            tracing::error!("Failed to update role: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to update role".to_string()))
        }
    }
}

/// 刪除角色
async fn delete_role(
    State(state): State<AppState>,
    Path(id): Path<i64>,
) -> ApiResult<Json<ApiResponse<()>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    // 檢查角色是否存在
    match role_repo.find_role_by_id(id).await {
        Ok(Some(role)) => {
            // 檢查是否為系統預設角色
            if ["super_admin", "admin", "pharmacy", "viewer"].contains(&role.name.as_str()) {
                return Err((StatusCode::FORBIDDEN, "Cannot delete system default roles".to_string()));
            }
        }
        Ok(None) => {
            return Err((StatusCode::NOT_FOUND, "Role not found".to_string()));
        }
        Err(e) => {
            tracing::error!("Failed to check role existence: {}", e);
            return Err((StatusCode::INTERNAL_SERVER_ERROR, "Database error".to_string()));
        }
    }
    
    // TODO: 檢查是否有用戶正在使用此角色
    // 這裡可以添加檢查邏輯，防止刪除正在使用的角色
    
    match role_repo.delete_role(id).await {
        Ok(_) => Ok(Json(ApiResponse::success(()))),
        Err(e) => {
            tracing::error!("Failed to delete role: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to delete role".to_string()))
        }
    }
}

/// 獲取角色權限
async fn get_role_permissions(
    State(state): State<AppState>,
    Path(id): Path<i64>,
) -> ApiResult<Json<ApiResponse<Vec<Permission>>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    match role_repo.find_role_with_permissions(id).await {
        Ok(Some(role)) => Ok(Json(ApiResponse::success(role.permissions))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
        Err(e) => {
            tracing::error!("Failed to fetch role permissions: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to fetch role permissions".to_string()))
        }
    }
}

/// 分配權限給角色
async fn assign_permissions(
    State(state): State<AppState>,
    Path(id): Path<i64>,
    Json(request): Json<AssignPermissionsRequest>,
) -> ApiResult<Json<ApiResponse<()>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    // 檢查角色是否存在
    match role_repo.find_role_by_id(id).await {
        Ok(Some(_)) => {}
        Ok(None) => {
            return Err((StatusCode::NOT_FOUND, "Role not found".to_string()));
        }
        Err(e) => {
            tracing::error!("Failed to check role existence: {}", e);
            return Err((StatusCode::INTERNAL_SERVER_ERROR, "Database error".to_string()));
        }
    }
    
    match role_repo.assign_permissions_to_role(id, &request.permission_ids).await {
        Ok(_) => Ok(Json(ApiResponse::success(()))),
        Err(e) => {
            tracing::error!("Failed to assign permissions: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to assign permissions".to_string()))
        }
    }
}

/// 獲取所有權限
async fn get_all_permissions(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<Vec<Permission>>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    match role_repo.find_all_permissions().await {
        Ok(permissions) => {
            Ok(Json(ApiResponse::success(permissions)))
        }
        Err(e) => {
            tracing::error!("Failed to fetch permissions: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to fetch permissions".to_string()))
        }
    }
}

/// 獲取當前用戶權限
async fn get_current_user_permissions(
    State(state): State<AppState>,
    axum::Extension(auth_context): axum::Extension<crate::api::middleware::AuthContext>,
) -> ApiResult<Json<ApiResponse<crate::models::UserPermissions>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    match role_repo.find_user_permissions(auth_context.claims.user_id).await {
        Ok(Some(permissions)) => Ok(Json(ApiResponse::success(permissions))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "User permissions not found".to_string())),
        Err(e) => {
            tracing::error!("Failed to fetch current user permissions: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to fetch user permissions".to_string()))
        }
    }
}

/// 獲取用戶權限
async fn get_user_permissions(
    State(state): State<AppState>,
    Path(user_id): Path<i64>,
) -> ApiResult<Json<ApiResponse<crate::models::UserPermissions>>> {
    let role_repo = PostgresRoleRepository::new(state.db.clone());
    
    match role_repo.find_user_permissions(user_id).await {
        Ok(Some(permissions)) => Ok(Json(ApiResponse::success(permissions))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "User permissions not found".to_string())),
        Err(e) => {
            tracing::error!("Failed to fetch user permissions: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to fetch user permissions".to_string()))
        }
    }
}