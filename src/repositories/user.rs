use crate::{
    models::User, 
    database::{<PERSON>b<PERSON>ool, DatabaseResult, DatabaseError},
    repositories::base::{BaseRepository, QueryableRepository, RepositoryBase}
};
use async_trait::async_trait;
use sqlx::Row;
use serde::{Deserialize, Serialize};
use tracing::debug;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CreateUser {
    pub username: String,
    pub email: String,
    pub password_hash: String,
    pub pharmacy_name: String,
    pub phone: Option<String>,
    pub line_user_id: Option<String>,
    pub role_id: Option<i64>,  // 新增角色 ID
    pub contact_person: Option<String>,
    pub mobile: Option<String>,
    pub institution_code: Option<String>,
    pub address: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateUser {
    pub username: Option<String>,
    pub email: Option<String>,
    pub pharmacy_name: Option<String>,
    pub phone: Option<String>,
    pub mobile: Option<String>,
    pub contact_person: Option<String>,
    pub institution_code: Option<String>,
    pub address: Option<String>,
    pub line_user_id: Option<String>,
    pub notification_email: Option<bool>,
    pub notification_line: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserFilter {
    pub username: Option<String>,
    pub email: Option<String>,
    pub pharmacy_name: Option<String>,
    pub is_active: Option<bool>,
}

#[async_trait]
pub trait UserRepository: BaseRepository<User, i64> + QueryableRepository<User, UserFilter> {
    async fn find_by_username(&self, username: &str) -> DatabaseResult<Option<User>>;
    async fn find_by_email(&self, email: &str) -> DatabaseResult<Option<User>>;
    async fn find_with_role(&self, id: i64) -> DatabaseResult<Option<crate::models::UserWithRole>>;
    async fn find_all_with_roles(&self) -> DatabaseResult<Vec<crate::models::UserWithRole>>;
    async fn create_user(&self, user: CreateUser) -> DatabaseResult<User>;
    async fn update_user(&self, id: i64, user: UpdateUser) -> DatabaseResult<User>;
    async fn update_password(&self, id: i64, password_hash: &str) -> DatabaseResult<()>;
    async fn update_user_role(&self, id: i64, role_id: i64) -> DatabaseResult<User>;
    #[allow(dead_code)]
    async fn update_notification_preferences(&self, id: i64, email: bool, line: bool) -> DatabaseResult<User>;
}

#[derive(Debug)]
pub struct PostgresUserRepository {
    base: RepositoryBase,
}

impl PostgresUserRepository {
    pub fn new(pool: DbPool) -> Self {
        Self { 
            base: RepositoryBase::new(pool)
        }
    }

    /// 將資料庫行轉換為 User 模型
    fn row_to_user(&self, row: &sqlx::postgres::PgRow) -> User {
        User {
            id: row.get("id"),
            username: row.get("username"),
            email: row.get("email"),
            password_hash: row.get("password_hash"),
            pharmacy_name: row.get("pharmacy_name"),
            phone: row.get("phone"),
            line_user_id: row.get("line_user_id"),
            notification_email: row.get("notification_email"),
            notification_line: row.get("notification_line"),
            role_id: row.get("role_id"),
            contact_person: row.get("contact_person"),
            mobile: row.get("mobile"),
            institution_code: row.get("institution_code"),
            address: row.get("address"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        }
    }
}

#[async_trait]
impl BaseRepository<User, i64> for PostgresUserRepository {
    async fn create(&self, user: User) -> DatabaseResult<User> {
        debug!("Creating user: {}", user.username);
        
        // 檢查用戶名唯一性
        if !self.base.check_unique_constraint("users", "username", &user.username, None).await? {
            return Err(DatabaseError::ConstraintViolation("Username already exists".to_string()));
        }
        
        // 檢查郵箱唯一性
        if !self.base.check_unique_constraint("users", "email", &user.email, None).await? {
            return Err(DatabaseError::ConstraintViolation("Email already exists".to_string()));
        }

        let id: i64 = sqlx::query_scalar(
            r#"
            INSERT INTO users (username, email, password_hash, pharmacy_name, phone, line_user_id, 
                             notification_email, notification_line, role_id, contact_person, mobile, 
                             institution_code, address)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            RETURNING id
            "#
        )
        .bind(&user.username)
        .bind(&user.email)
        .bind(&user.password_hash)
        .bind(&user.pharmacy_name)
        .bind(&user.phone)
        .bind(&user.line_user_id)
        .bind(user.notification_email)
        .bind(user.notification_line)
        .bind(user.role_id)
        .bind(&user.contact_person)
        .bind(&user.mobile)
        .bind(&user.institution_code)
        .bind(&user.address)
        .fetch_one(self.base.pool().as_ref())
        .await
        .map_err(|e| {
            tracing::error!("Failed to create user in base method: {}", e);
            DatabaseError::from(e)
        })?;

        self.find_by_id(id).await?.ok_or_else(|| {
            DatabaseError::NotFound("User not found after creation".to_string())
        })
    }

    async fn find_by_id(&self, id: i64) -> DatabaseResult<Option<User>> {
        debug!("Finding user by id: {}", id);
        
        let row = self.base.execute_query(
            "find user by id",
            sqlx::query(
                r#"
                SELECT id, username, email, password_hash, pharmacy_name, phone, line_user_id, 
                       notification_email, notification_line, role_id, contact_person, mobile,
                       institution_code, address, created_at, updated_at
                FROM users WHERE id = $1
                "#
            )
            .bind(id)
            .fetch_optional(self.base.pool().as_ref())
        ).await?;

        Ok(row.map(|row| self.row_to_user(&row)))
    }

    async fn update(&self, id: i64, user: User) -> DatabaseResult<User> {
        debug!("Updating user with id: {}", id);
        
        // 檢查用戶是否存在
        if !self.base.exists_by_id("users", id).await? {
            return Err(DatabaseError::NotFound("User not found".to_string()));
        }
        
        // 檢查用戶名唯一性（排除當前用戶）
        if !self.base.check_unique_constraint("users", "username", &user.username, Some(id)).await? {
            return Err(DatabaseError::ConstraintViolation("Username already exists".to_string()));
        }
        
        // 檢查郵箱唯一性（排除當前用戶）
        if !self.base.check_unique_constraint("users", "email", &user.email, Some(id)).await? {
            return Err(DatabaseError::ConstraintViolation("Email already exists".to_string()));
        }

        self.base.execute_query(
            "update user",
            sqlx::query(
                r#"
                UPDATE users SET 
                    username = $1, email = $2, pharmacy_name = $3, phone = $4, line_user_id = $5,
                    notification_email = $6, notification_line = $7, role_id = $8, updated_at = CURRENT_TIMESTAMP
                WHERE id = $9
                "#
            )
            .bind(&user.username)
            .bind(&user.email)
            .bind(&user.pharmacy_name)
            .bind(&user.phone)
            .bind(&user.line_user_id)
            .bind(user.notification_email)
            .bind(user.notification_line)
            .bind(user.role_id)
            .bind(id)
            .execute(self.base.pool().as_ref())
        ).await?;

        self.find_by_id(id).await?.ok_or_else(|| {
            DatabaseError::NotFound("User not found after update".to_string())
        })
    }

    async fn delete(&self, id: i64) -> DatabaseResult<bool> {
        debug!("Deleting user with id: {}", id);
        
        let result = self.base.execute_query(
            "delete user",
            sqlx::query("DELETE FROM users WHERE id = $1")
                .bind(id)
                .execute(self.base.pool().as_ref())
        ).await?;

        Ok(result.rows_affected() > 0)
    }

    async fn list(&self, limit: Option<i32>, offset: Option<i32>) -> DatabaseResult<Vec<User>> {
        let limit = limit.unwrap_or(50);
        let offset = offset.unwrap_or(0);

        debug!("Listing users with limit: {}, offset: {}", limit, offset);

        let rows = self.base.execute_query(
            "list users",
            sqlx::query(
                r#"
                SELECT id, username, email, password_hash, pharmacy_name, phone, line_user_id, 
                       notification_email, notification_line, role_id, created_at, updated_at
                FROM users
                ORDER BY created_at DESC
                LIMIT $1 OFFSET $2
                "#
            )
            .bind(limit)
            .bind(offset)
            .fetch_all(self.base.pool().as_ref())
        ).await?;

        Ok(rows.iter().map(|row| self.row_to_user(row)).collect())
    }
}

#[async_trait]
impl QueryableRepository<User, UserFilter> for PostgresUserRepository {
    async fn find_by_filter(&self, filter: UserFilter) -> DatabaseResult<Vec<User>> {
        let mut query = String::from(
            r#"
            SELECT id, username, email, password_hash, pharmacy_name, phone, line_user_id, 
                   notification_email, notification_line, role_id, created_at, updated_at
            FROM users WHERE 1=1
            "#
        );
        
        let mut conditions = Vec::new();
        let mut params: Vec<String> = Vec::new();
        let mut param_count = 1;

        if let Some(username) = &filter.username {
            conditions.push(format!("username LIKE ${}", param_count));
            params.push(format!("%{}%", username));
            param_count += 1;
        }
        
        if let Some(email) = &filter.email {
            conditions.push(format!("email LIKE ${}", param_count));
            params.push(format!("%{}%", email));
            param_count += 1;
        }
        
        if let Some(pharmacy_name) = &filter.pharmacy_name {
            conditions.push(format!("pharmacy_name LIKE ${}", param_count));
            params.push(format!("%{}%", pharmacy_name));
        }

        if !conditions.is_empty() {
            query.push_str(" AND ");
            query.push_str(&conditions.join(" AND "));
        }

        query.push_str(" ORDER BY created_at DESC");

        let mut query_builder = sqlx::query(&query);
        for param in &params {
            query_builder = query_builder.bind(param);
        }

        let rows = self.base.execute_query(
            "find users by filter",
            query_builder.fetch_all(self.base.pool().as_ref())
        ).await?;

        Ok(rows.iter().map(|row| self.row_to_user(row)).collect())
    }

    async fn count_by_filter(&self, filter: UserFilter) -> DatabaseResult<i64> {
        let mut query = String::from("SELECT COUNT(*) FROM users WHERE 1=1");
        
        let mut conditions = Vec::new();
        let mut params: Vec<String> = Vec::new();
        let mut param_count = 1;

        if let Some(username) = &filter.username {
            conditions.push(format!("username LIKE ${}", param_count));
            params.push(format!("%{}%", username));
            param_count += 1;
        }
        
        if let Some(email) = &filter.email {
            conditions.push(format!("email LIKE ${}", param_count));
            params.push(format!("%{}%", email));
            param_count += 1;
        }
        
        if let Some(pharmacy_name) = &filter.pharmacy_name {
            conditions.push(format!("pharmacy_name LIKE ${}", param_count));
            params.push(format!("%{}%", pharmacy_name));
        }

        if !conditions.is_empty() {
            query.push_str(" AND ");
            query.push_str(&conditions.join(" AND "));
        }

        let mut query_builder = sqlx::query_scalar(&query);
        for param in &params {
            query_builder = query_builder.bind(param);
        }

        self.base.execute_query(
            "count users by filter",
            query_builder.fetch_one(self.base.pool().as_ref())
        ).await
    }
}

#[async_trait]
impl UserRepository for PostgresUserRepository {
    async fn find_by_username(&self, username: &str) -> DatabaseResult<Option<User>> {
        debug!("Finding user by username: {}", username);
        
        let row = self.base.execute_query(
            "find user by username",
            sqlx::query(
                r#"
                SELECT id, username, email, password_hash, pharmacy_name, phone, line_user_id, 
                       notification_email, notification_line, role_id, contact_person, mobile,
                       institution_code, address, created_at, updated_at
                FROM users WHERE username = $1
                "#
            )
            .bind(username)
            .fetch_optional(self.base.pool().as_ref())
        ).await?;

        Ok(row.map(|row| self.row_to_user(&row)))
    }

    async fn find_by_email(&self, email: &str) -> DatabaseResult<Option<User>> {
        debug!("Finding user by email: {}", email);
        
        let row = self.base.execute_query(
            "find user by email",
            sqlx::query(
                r#"
                SELECT id, username, email, password_hash, pharmacy_name, phone, line_user_id, 
                       notification_email, notification_line, role_id, contact_person, mobile,
                       institution_code, address, created_at, updated_at
                FROM users WHERE email = $1
                "#
            )
            .bind(email)
            .fetch_optional(self.base.pool().as_ref())
        ).await?;

        Ok(row.map(|row| self.row_to_user(&row)))
    }

    async fn create_user(&self, user: CreateUser) -> DatabaseResult<User> {
        debug!("Creating user with username: {}", user.username);
        
        // 檢查用戶名唯一性
        if !self.base.check_unique_constraint("users", "username", &user.username, None).await? {
            return Err(DatabaseError::ConstraintViolation("Username already exists".to_string()));
        }
        
        // 檢查郵箱唯一性
        if !self.base.check_unique_constraint("users", "email", &user.email, None).await? {
            return Err(DatabaseError::ConstraintViolation("Email already exists".to_string()));
        }

        let id: i64 = sqlx::query_scalar(
            r#"
            INSERT INTO users (username, email, password_hash, pharmacy_name, phone, line_user_id, 
                             notification_email, notification_line, role_id, contact_person, mobile,
                             institution_code, address)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            RETURNING id
            "#
        )
        .bind(&user.username)
        .bind(&user.email)
        .bind(&user.password_hash)
        .bind(&user.pharmacy_name)
        .bind(&user.phone)
        .bind(&user.line_user_id)
        .bind(true) // 預設啟用 email 通知
        .bind(false) // 預設不啟用 line 通知
        .bind(user.role_id.unwrap_or(3)) // 預設為 pharmacy 角色 (id=3)
        .bind(&user.contact_person)
        .bind(&user.mobile)
        .bind(&user.institution_code)
        .bind(&user.address)
        .fetch_one(self.base.pool().as_ref())
        .await
        .map_err(|e| {
            tracing::error!("Failed to create user: {}", e);
            DatabaseError::from(e)
        })?;

        self.find_by_id(id).await?.ok_or_else(|| {
            DatabaseError::NotFound("User not found after creation".to_string())
        })
    }

    async fn update_user(&self, id: i64, user: UpdateUser) -> DatabaseResult<User> {
        debug!("Updating user with id: {}", id);
        
        // 檢查用戶是否存在
        if !self.base.exists_by_id("users", id).await? {
            return Err(DatabaseError::NotFound("User not found".to_string()));
        }

        let mut updates = Vec::new();
        let mut params: Vec<String> = Vec::new();

        if let Some(username) = &user.username {
            if !self.base.check_unique_constraint("users", "username", username, Some(id)).await? {
                return Err(DatabaseError::ConstraintViolation("Username already exists".to_string()));
            }
            updates.push(format!("username = ${}", params.len() + 1));
            params.push(username.clone());
        }
        
        if let Some(email) = &user.email {
            if !self.base.check_unique_constraint("users", "email", email, Some(id)).await? {
                return Err(DatabaseError::ConstraintViolation("Email already exists".to_string()));
            }
            updates.push(format!("email = ${}", params.len() + 1));
            params.push(email.clone());
        }
        
        if let Some(pharmacy_name) = &user.pharmacy_name {
            updates.push(format!("pharmacy_name = ${}", params.len() + 1));
            params.push(pharmacy_name.clone());
        }
        
        if let Some(phone) = &user.phone {
            updates.push(format!("phone = ${}", params.len() + 1));
            params.push(phone.clone());
        }
        
        if let Some(mobile) = &user.mobile {
            updates.push(format!("mobile = ${}", params.len() + 1));
            params.push(mobile.clone());
        }
        
        if let Some(contact_person) = &user.contact_person {
            updates.push(format!("contact_person = ${}", params.len() + 1));
            params.push(contact_person.clone());
        }
        
        if let Some(institution_code) = &user.institution_code {
            updates.push(format!("institution_code = ${}", params.len() + 1));
            params.push(institution_code.clone());
        }
        
        if let Some(address) = &user.address {
            updates.push(format!("address = ${}", params.len() + 1));
            params.push(address.clone());
        }
        
        if let Some(line_user_id) = &user.line_user_id {
            updates.push(format!("line_user_id = ${}", params.len() + 1));
            params.push(line_user_id.clone());
        }
        
        // 通知欄位暫不更新（前端已移除，避免型別衝突）

        if updates.is_empty() {
            // 如果沒有要更新的欄位，只更新 updated_at
            self.base.execute_query(
                "update user timestamp",
                sqlx::query("UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1")
                    .bind(id)
                    .execute(self.base.pool().as_ref())
            ).await?;
        } else {
            updates.push("updated_at = CURRENT_TIMESTAMP".to_string());
            let query = format!("UPDATE users SET {} WHERE id = ${}", updates.join(", "), params.len() + 1);
            
            let mut query_builder = sqlx::query(&query);
            for param in &params {
                query_builder = query_builder.bind(param);
            }
            query_builder = query_builder.bind(id);

            self.base.execute_query(
                "update user",
                query_builder.execute(self.base.pool().as_ref())
            ).await?;
        }

        self.find_by_id(id).await?.ok_or_else(|| {
            DatabaseError::NotFound("User not found after update".to_string())
        })
    }

    async fn update_password(&self, id: i64, password_hash: &str) -> DatabaseResult<()> {
        debug!("Updating password for user with id: {}", id);
        
        // 檢查用戶是否存在
        if !self.base.exists_by_id("users", id).await? {
            return Err(DatabaseError::NotFound("User not found".to_string()));
        }

        self.base.execute_query(
            "update user password",
            sqlx::query(
                "UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2"
            )
            .bind(password_hash)
            .bind(id)
            .execute(self.base.pool().as_ref())
        ).await?;

        Ok(())
    }

    async fn update_notification_preferences(&self, id: i64, email: bool, line: bool) -> DatabaseResult<User> {
        debug!("Updating notification preferences for user with id: {}", id);
        
        // 檢查用戶是否存在
        if !self.base.exists_by_id("users", id).await? {
            return Err(DatabaseError::NotFound("User not found".to_string()));
        }

        self.base.execute_query(
            "update notification preferences",
            sqlx::query(
                "UPDATE users SET notification_email = $1, notification_line = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3"
            )
            .bind(email)
            .bind(line)
            .bind(id)
            .execute(self.base.pool().as_ref())
        ).await?;

        self.find_by_id(id).await?.ok_or_else(|| {
            DatabaseError::NotFound("User not found after update".to_string())
        })
    }

    async fn find_with_role(&self, id: i64) -> DatabaseResult<Option<crate::models::UserWithRole>> {
        debug!("Finding user with role by id: {}", id);
        
        let row = self.base.execute_query(
            "find user with role by id",
            sqlx::query(
                r#"
                SELECT u.id, u.username, u.email, u.pharmacy_name, u.phone, u.line_user_id, 
                       u.notification_email, u.notification_line, u.role_id, u.contact_person, u.mobile,
                       u.institution_code, u.address, u.created_at, u.updated_at,
                       r.name as role_name, r.description as role_description
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE u.id = $1
                "#
            )
            .bind(id)
            .fetch_optional(self.base.pool().as_ref())
        ).await?;

        Ok(row.map(|row| crate::models::UserWithRole {
            id: row.get("id"),
            username: row.get("username"),
            email: row.get("email"),
            pharmacy_name: row.get("pharmacy_name"),
            phone: row.get("phone"),
            line_user_id: row.get("line_user_id"),
            notification_email: row.get("notification_email"),
            notification_line: row.get("notification_line"),
            role_id: row.get("role_id"),
            role_name: row.get("role_name"),
            role_description: row.get("role_description"),
            contact_person: row.get("contact_person"),
            mobile: row.get("mobile"),
            institution_code: row.get("institution_code"),
            address: row.get("address"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        }))
    }

    async fn find_all_with_roles(&self) -> DatabaseResult<Vec<crate::models::UserWithRole>> {
        debug!("Finding all users with roles");
        
        let rows = self.base.execute_query(
            "find all users with roles",
            sqlx::query(
                r#"
                SELECT u.id, u.username, u.email, u.pharmacy_name, u.phone, u.line_user_id, 
                       u.notification_email, u.notification_line, u.role_id, u.contact_person, u.mobile,
                       u.institution_code, u.address, u.created_at, u.updated_at,
                       r.name as role_name, r.description as role_description
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                ORDER BY u.created_at DESC
                "#
            )
            .fetch_all(self.base.pool().as_ref())
        ).await?;

        Ok(rows.iter().map(|row| crate::models::UserWithRole {
            id: row.get("id"),
            username: row.get("username"),
            email: row.get("email"),
            pharmacy_name: row.get("pharmacy_name"),
            phone: row.get("phone"),
            line_user_id: row.get("line_user_id"),
            notification_email: row.get("notification_email"),
            notification_line: row.get("notification_line"),
            role_id: row.get("role_id"),
            role_name: row.get("role_name"),
            role_description: row.get("role_description"),
            contact_person: row.get("contact_person"),
            mobile: row.get("mobile"),
            institution_code: row.get("institution_code"),
            address: row.get("address"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        }).collect())
    }

    async fn update_user_role(&self, id: i64, role_id: i64) -> DatabaseResult<User> {
        debug!("Updating user role for user id: {} to role id: {}", id, role_id);
        
        // 檢查用戶是否存在
        if !self.base.exists_by_id("users", id).await? {
            return Err(DatabaseError::NotFound("User not found".to_string()));
        }

        // 檢查角色是否存在
        if !self.base.exists_by_id("roles", role_id).await? {
            return Err(DatabaseError::NotFound("Role not found".to_string()));
        }

        self.base.execute_query(
            "update user role",
            sqlx::query(
                "UPDATE users SET role_id = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2"
            )
            .bind(role_id)
            .bind(id)
            .execute(self.base.pool().as_ref())
        ).await?;

        self.find_by_id(id).await?.ok_or_else(|| {
            DatabaseError::NotFound("User not found after role update".to_string())
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use crate::database::Database;
    use chrono::Utc;

    async fn create_test_repository() -> PostgresUserRepository {
        // Note: This test requires a running PostgreSQL instance
        let database_url = "postgresql://postgres:password@localhost:5432/test_db";
        let database = Database::new(database_url).await.unwrap();
        PostgresUserRepository::new(database.pool().clone())
    }

    fn create_test_user() -> User {
        User {
            id: 0, // Will be set by database
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hashed_password".to_string(),
            pharmacy_name: "Test Pharmacy".to_string(),
            phone: Some("************".to_string()),
            line_user_id: Some("line123".to_string()),
            notification_email: true,
            notification_line: false,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    fn create_test_create_user() -> CreateUser {
        CreateUser {
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hashed_password".to_string(),
            pharmacy_name: "Test Pharmacy".to_string(),
            phone: Some("************".to_string()),
            line_user_id: Some("line123".to_string()),
        }
    }

    #[tokio::test]
    async fn test_create_user() {
        let repo = create_test_repository().await;
        let create_user = create_test_create_user();
        
        let result = repo.create_user(create_user).await;
        assert!(result.is_ok());
        
        let user = result.unwrap();
        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, "<EMAIL>");
    }

    #[tokio::test]
    async fn test_find_by_username() {
        let repo = create_test_repository().await;
        let create_user = create_test_create_user();
        
        let created_user = repo.create_user(create_user).await.unwrap();
        let found_user = repo.find_by_username("testuser").await.unwrap();
        
        assert!(found_user.is_some());
        assert_eq!(found_user.unwrap().id, created_user.id);
    }

    #[tokio::test]
    async fn test_find_by_email() {
        let repo = create_test_repository().await;
        let create_user = create_test_create_user();
        
        let created_user = repo.create_user(create_user).await.unwrap();
        let found_user = repo.find_by_email("<EMAIL>").await.unwrap();
        
        assert!(found_user.is_some());
        assert_eq!(found_user.unwrap().id, created_user.id);
    }

    #[tokio::test]
    async fn test_update_user() {
        let repo = create_test_repository().await;
        let create_user = create_test_create_user();
        
        let created_user = repo.create_user(create_user).await.unwrap();
        
        let update_user = UpdateUser {
            username: Some("updated_user".to_string()),
            email: None,
            pharmacy_name: Some("Updated Pharmacy".to_string()),
            phone: None,
            line_user_id: None,
            notification_email: None,
            notification_line: None,
        };
        
        let updated_user = repo.update_user(created_user.id, update_user).await.unwrap();
        assert_eq!(updated_user.username, "updated_user");
        assert_eq!(updated_user.pharmacy_name, "Updated Pharmacy");
    }

    #[tokio::test]
    async fn test_delete_user() {
        let repo = create_test_repository().await;
        let create_user = create_test_create_user();
        
        let created_user = repo.create_user(create_user).await.unwrap();
        let deleted = repo.delete(created_user.id).await.unwrap();
        
        assert!(deleted);
        
        let found_user = repo.find_by_id(created_user.id).await.unwrap();
        assert!(found_user.is_none());
    }

    #[tokio::test]
    async fn test_list_users() {
        let repo = create_test_repository().await;
        
        // Create multiple users
        for i in 0..3 {
            let mut create_user = create_test_create_user();
            create_user.username = format!("testuser{}", i);
            create_user.email = format!("test{}@example.com", i);
            repo.create_user(create_user).await.unwrap();
        }
        
        let users = repo.list(Some(10), Some(0)).await.unwrap();
        assert_eq!(users.len(), 3);
    }

    #[tokio::test]
    async fn test_unique_constraint_violation() {
        let repo = create_test_repository().await;
        let create_user = create_test_create_user();
        
        // Create first user
        repo.create_user(create_user.clone()).await.unwrap();
        
        // Try to create another user with same username
        let result = repo.create_user(create_user).await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), DatabaseError::ConstraintViolation(_)));
    }
}