use crate::database::Db<PERSON>ool;
use crate::models::message::{Message, CreateMessageRequest, MessageResponse};
use crate::database::error::DatabaseError;
use sqlx::Row;

pub struct MessageRepository {
    pool: DbPool,
}

impl MessageRepository {
    pub fn new(pool: DbPool) -> Self {
        Self { pool }
    }

    pub async fn create_message(&self, request: CreateMessageRequest, created_by: i32) -> Result<Message, DatabaseError> {
        let row = sqlx::query(
            r#"
            INSERT INTO messages (title, content, message_type, created_by)
            VALUES ($1, $2, $3, $4)
            RETURNING id, title, content, message_type, status, created_by, created_at, updated_at
            "#
        )
        .bind(request.title)
        .bind(request.content)
        .bind(request.message_type)
        .bind(created_by)
        .fetch_one(&*self.pool)
        .await
        .map_err(DatabaseError::from)?;

        let message = Message {
            id: row.get("id"),
            title: row.get("title"),
            content: row.get("content"),
            message_type: row.get("message_type"),
            status: row.get("status"),
            created_by: row.get("created_by"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        };

        Ok(message)
    }

    pub async fn get_active_messages(&self, user_id: i32) -> Result<Vec<MessageResponse>, DatabaseError> {
        let rows = sqlx::query(
            r#"
            SELECT 
                m.id,
                m.title,
                m.content,
                m.message_type,
                m.status,
                m.created_by,
                m.created_at,
                m.updated_at,
                COALESCE(mr.read_at IS NOT NULL, false) as is_read
            FROM messages m
            LEFT JOIN message_reads mr ON m.id = mr.message_id AND mr.user_id = $1
            WHERE m.status = 'active'
            ORDER BY m.created_at DESC
            "#
        )
        .bind(user_id)
        .fetch_all(&*self.pool)
        .await
        .map_err(DatabaseError::from)?;

        let messages = rows.into_iter().map(|row| MessageResponse {
            id: row.get("id"),
            title: row.get("title"),
            content: row.get("content"),
            message_type: row.get("message_type"),
            status: row.get("status"),
            created_by: row.get("created_by"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
            is_read: row.get("is_read"),
        }).collect();

        Ok(messages)
    }

    pub async fn mark_as_read(&self, message_id: i32, user_id: i32) -> Result<(), DatabaseError> {
        sqlx::query(
            r#"
            INSERT INTO message_reads (message_id, user_id)
            VALUES ($1, $2)
            ON CONFLICT (message_id, user_id) DO NOTHING
            "#
        )
        .bind(message_id)
        .bind(user_id)
        .execute(&*self.pool)
        .await
        .map_err(DatabaseError::from)?;

        Ok(())
    }

    pub async fn update_message_status(&self, message_id: i32, status: &str) -> Result<(), DatabaseError> {
        sqlx::query(
            r#"
            UPDATE messages
            SET status = $1, updated_at = CURRENT_TIMESTAMP
            WHERE id = $2
            "#
        )
        .bind(status)
        .bind(message_id)
        .execute(&*self.pool)
        .await
        .map_err(DatabaseError::from)?;

        Ok(())
    }

    pub async fn get_all_messages(&self) -> Result<Vec<MessageResponse>, DatabaseError> {
        let rows = sqlx::query(
            r#"
            SELECT 
                id,
                title,
                content,
                message_type,
                status,
                created_by,
                created_at,
                updated_at
            FROM messages
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(&*self.pool)
        .await
        .map_err(DatabaseError::from)?;

        let messages = rows.into_iter().map(|row| MessageResponse {
            id: row.get("id"),
            title: row.get("title"),
            content: row.get("content"),
            message_type: row.get("message_type"),
            status: row.get("status"),
            created_by: row.get("created_by"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
            is_read: false, // 管理員查看時不需要閱讀狀態
        }).collect();

        Ok(messages)
    }

    pub async fn update_message(&self, message_id: i32, request: CreateMessageRequest) -> Result<Message, DatabaseError> {
        let row = sqlx::query(
            r#"
            UPDATE messages
            SET title = $1, content = $2, message_type = $3, updated_at = CURRENT_TIMESTAMP
            WHERE id = $4
            RETURNING id, title, content, message_type, status, created_by, created_at, updated_at
            "#
        )
        .bind(request.title)
        .bind(request.content)
        .bind(request.message_type)
        .bind(message_id)
        .fetch_one(&*self.pool)
        .await
        .map_err(DatabaseError::from)?;

        let message = Message {
            id: row.get("id"),
            title: row.get("title"),
            content: row.get("content"),
            message_type: row.get("message_type"),
            status: row.get("status"),
            created_by: row.get("created_by"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        };

        Ok(message)
    }

    pub async fn delete_message(&self, message_id: i32) -> Result<(), DatabaseError> {
        sqlx::query(
            r#"
            DELETE FROM messages WHERE id = $1
            "#
        )
        .bind(message_id)
        .execute(&*self.pool)
        .await
        .map_err(DatabaseError::from)?;

        Ok(())
    }
}