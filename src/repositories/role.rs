use crate::{
    models::{Role, Permission, RoleWithPermissions, UserPermissions},
    database::DbPool,
    database::error::DatabaseError,
};
use async_trait::async_trait;
use sqlx::Row;

#[async_trait]
pub trait RoleRepository: Send + Sync {
    async fn find_all_roles(&self) -> Result<Vec<Role>, DatabaseError>;
    async fn find_role_by_id(&self, id: i64) -> Result<Option<Role>, DatabaseError>;
    async fn find_role_by_name(&self, name: &str) -> Result<Option<Role>, DatabaseError>;
    async fn find_role_with_permissions(&self, id: i64) -> Result<Option<RoleWithPermissions>, DatabaseError>;
    async fn find_all_permissions(&self) -> Result<Vec<Permission>, DatabaseError>;
    async fn find_user_permissions(&self, user_id: i64) -> Result<Option<UserPermissions>, DatabaseError>;
    async fn create_role(&self, name: &str, description: Option<&str>) -> Result<Role, DatabaseError>;
    async fn update_role(&self, id: i64, name: Option<&str>, description: Option<&str>) -> Result<Role, DatabaseError>;
    async fn delete_role(&self, id: i64) -> Result<(), DatabaseError>;
    async fn assign_permissions_to_role(&self, role_id: i64, permission_ids: &[i64]) -> Result<(), DatabaseError>;
    async fn remove_permissions_from_role(&self, role_id: i64, permission_ids: &[i64]) -> Result<(), DatabaseError>;
}

pub struct PostgresRoleRepository {
    pool: DbPool,
}

impl PostgresRoleRepository {
    pub fn new(pool: DbPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl RoleRepository for PostgresRoleRepository {
    async fn find_all_roles(&self) -> Result<Vec<Role>, DatabaseError> {
        let roles = sqlx::query_as::<_, Role>(
            "SELECT id, name, description, created_at, updated_at FROM roles ORDER BY name"
        )
        .fetch_all(&*self.pool)
        .await?;
        
        Ok(roles)
    }

    async fn find_role_by_id(&self, id: i64) -> Result<Option<Role>, DatabaseError> {
        let role = sqlx::query_as::<_, Role>(
            "SELECT id, name, description, created_at, updated_at FROM roles WHERE id = $1"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await?;
        
        Ok(role)
    }

    async fn find_role_by_name(&self, name: &str) -> Result<Option<Role>, DatabaseError> {
        let role = sqlx::query_as::<_, Role>(
            "SELECT id, name, description, created_at, updated_at FROM roles WHERE name = $1"
        )
        .bind(name)
        .fetch_optional(&*self.pool)
        .await?;
        
        Ok(role)
    }

    async fn find_role_with_permissions(&self, id: i64) -> Result<Option<RoleWithPermissions>, DatabaseError> {
        // 先查找角色
        let role = match self.find_role_by_id(id).await? {
            Some(role) => role,
            None => return Ok(None),
        };

        // 查找角色的權限
        let permissions = sqlx::query_as::<_, Permission>(
            r#"
            SELECT p.id, p.name, p.description, p.resource, p.action, p.created_at
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = $1
            ORDER BY p.resource, p.action
            "#
        )
        .bind(id)
        .fetch_all(&*self.pool)
        .await?;

        Ok(Some(RoleWithPermissions {
            id: role.id,
            name: role.name,
            description: role.description,
            permissions,
            created_at: role.created_at,
            updated_at: role.updated_at,
        }))
    }

    async fn find_all_permissions(&self) -> Result<Vec<Permission>, DatabaseError> {
        let permissions = sqlx::query_as::<_, Permission>(
            "SELECT id, name, description, resource, action, created_at FROM permissions ORDER BY resource, action"
        )
        .fetch_all(&*self.pool)
        .await?;
        
        Ok(permissions)
    }

    async fn find_user_permissions(&self, user_id: i64) -> Result<Option<UserPermissions>, DatabaseError> {
        let row = sqlx::query(
            r#"
            SELECT 
                u.id as user_id,
                r.name as role_name,
                ARRAY_AGG(p.name) as permissions
            FROM users u
            INNER JOIN roles r ON u.role_id = r.id
            LEFT JOIN role_permissions rp ON r.id = rp.role_id
            LEFT JOIN permissions p ON rp.permission_id = p.id
            WHERE u.id = $1
            GROUP BY u.id, r.name
            "#
        )
        .bind(user_id)
        .fetch_optional(&*self.pool)
        .await?;

        match row {
            Some(row) => {
                let permissions: Vec<String> = row.try_get::<Vec<Option<String>>, _>("permissions")?
                    .into_iter()
                    .filter_map(|p| p)
                    .collect();

                Ok(Some(UserPermissions {
                    user_id: row.try_get("user_id")?,
                    role_name: row.try_get("role_name")?,
                    permissions,
                }))
            }
            None => Ok(None),
        }
    }

    async fn create_role(&self, name: &str, description: Option<&str>) -> Result<Role, DatabaseError> {
        let role = sqlx::query_as::<_, Role>(
            r#"
            INSERT INTO roles (name, description, created_at, updated_at)
            VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id, name, description, created_at, updated_at
            "#
        )
        .bind(name)
        .bind(description)
        .fetch_one(&*self.pool)
        .await?;

        Ok(role)
    }

    async fn update_role(&self, id: i64, name: Option<&str>, description: Option<&str>) -> Result<Role, DatabaseError> {
        let role = sqlx::query_as::<_, Role>(
            r#"
            UPDATE roles 
            SET 
                name = COALESCE($2, name),
                description = COALESCE($3, description),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
            RETURNING id, name, description, created_at, updated_at
            "#
        )
        .bind(id)
        .bind(name)
        .bind(description)
        .fetch_one(&*self.pool)
        .await?;

        Ok(role)
    }

    async fn delete_role(&self, id: i64) -> Result<(), DatabaseError> {
        sqlx::query("DELETE FROM roles WHERE id = $1")
            .bind(id)
            .execute(&*self.pool)
            .await?;

        Ok(())
    }

    async fn assign_permissions_to_role(&self, role_id: i64, permission_ids: &[i64]) -> Result<(), DatabaseError> {
        let mut tx = self.pool.begin().await?;

        // 先刪除現有的權限關聯
        sqlx::query("DELETE FROM role_permissions WHERE role_id = $1")
            .bind(role_id)
            .execute(&mut *tx)
            .await?;

        // 添加新的權限關聯
        for permission_id in permission_ids {
            sqlx::query(
                "INSERT INTO role_permissions (role_id, permission_id) VALUES ($1, $2) ON CONFLICT DO NOTHING"
            )
            .bind(role_id)
            .bind(permission_id)
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    async fn remove_permissions_from_role(&self, role_id: i64, permission_ids: &[i64]) -> Result<(), DatabaseError> {
        for permission_id in permission_ids {
            sqlx::query("DELETE FROM role_permissions WHERE role_id = $1 AND permission_id = $2")
                .bind(role_id)
                .bind(permission_id)
                .execute(&*self.pool)
                .await?;
        }

        Ok(())
    }
}