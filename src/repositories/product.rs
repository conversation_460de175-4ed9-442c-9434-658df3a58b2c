use crate::{models::{Product, CreateProduct, UpdateProduct, ProductFilter}, database::DbPool, error::AppError};
use async_trait::async_trait;
use sqlx::Row;

#[async_trait]
#[allow(dead_code)]
pub trait ProductRepository: Send + Sync {
    async fn create(&self, product: CreateProduct) -> Result<Product, AppError>;
    async fn find_by_id(&self, id: i64) -> Result<Option<Product>, AppError>;
    async fn find_by_nhi_code(&self, nhi_code: &str) -> Result<Option<Product>, AppError>;
    async fn update(&self, id: i64, product: UpdateProduct) -> Result<Product, AppError>;
    async fn delete(&self, id: i64) -> Result<bool, AppError>;
    async fn list(&self, filter: Option<ProductFilter>, limit: Option<i32>, offset: Option<i32>) -> Result<Vec<Product>, AppError>;
    async fn update_stock(&self, id: i64, quantity: i32) -> Result<Product, AppError>;
    async fn find_low_stock(&self) -> Result<Vec<Product>, AppError>;
    async fn bulk_upsert(&self, products: Vec<crate::models::product::ProductImportData>) -> Result<usize, AppError>;
    async fn batch_update_stock(&self, updates: Vec<(i64, i32)>) -> Result<usize, AppError>;
    async fn batch_reserve_stock(&self, reservations: Vec<(i64, i32)>) -> Result<bool, AppError>;
}

pub struct PostgresProductRepository {
    pool: DbPool,
}

impl PostgresProductRepository {
    #[allow(dead_code)]
    pub fn new(pool: DbPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ProductRepository for PostgresProductRepository {
    async fn create(&self, product: CreateProduct) -> Result<Product, AppError> {
        let row = sqlx::query(
            r#"
            INSERT INTO products (nhi_code, name, english_name, ingredients, dosage_form, manufacturer, unit, selling_price, stock_quantity, description)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING id
            "#
        )
        .bind(&product.nhi_code)
        .bind(&product.name)
        .bind(&product.english_name)
        .bind(&product.ingredients)
        .bind(&product.dosage_form)
        .bind(&product.manufacturer)
        .bind(&product.unit)
        .bind(product.selling_price)
        .bind(product.stock_quantity)
        .bind(&product.description)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to create product: {}", e);
            AppError::Database(e)
        })?;
        
        let id: i64 = row.get("id");

        self.find_by_id(id).await?.ok_or_else(|| {
            AppError::NotFound("Product not found after creation".to_string())
        })
    }

    async fn find_by_id(&self, id: i64) -> Result<Option<Product>, AppError> {
        let row = sqlx::query(
            r#"
            SELECT p.id, p.nhi_code, p.name, p.english_name, p.ingredients, p.dosage_form, 
                   p.manufacturer, p.unit, p.selling_price as unit_price, 
                   p.nhi_price, p.stock_quantity, p.description, p.is_active, p.created_at, p.updated_at
            FROM products p
            WHERE p.id = $1
            "#
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to find product by id: {}", e);
            AppError::Database(e)
        })?;

        Ok(row.map(|row| {
            
            Product {
                id: row.get("id"),
                nhi_code: row.get("nhi_code"),
                name: row.get("name"),
                english_name: row.get("english_name"),
                ingredients: row.get("ingredients"),
                dosage_form: row.get("dosage_form"),
                manufacturer: row.get("manufacturer"),
                unit: row.get("unit"),
                selling_price: row.get("unit_price"),
                nhi_price: row.get("nhi_price"),
                
                stock_quantity: row.get("stock_quantity"),
                description: row.get("description"),
                is_active: row.get("is_active"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            }
        }))
    }

    async fn find_by_nhi_code(&self, nhi_code: &str) -> Result<Option<Product>, AppError> {
        let row = sqlx::query(
            r#"
            SELECT p.id, p.nhi_code, p.name, p.english_name, p.ingredients, p.dosage_form, 
                   p.manufacturer, p.unit, p.selling_price as unit_price, 
                   p.nhi_price, p.stock_quantity, p.description, p.is_active, p.created_at, p.updated_at
            FROM products p
            WHERE p.nhi_code = $1
            "#
        )
        .bind(nhi_code)
        .fetch_optional(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to find product by nhi_code: {}", e);
            AppError::Database(e)
        })?;

        Ok(row.map(|row| {
            
            Product {
                id: row.get("id"),
                nhi_code: row.get("nhi_code"),
                name: row.get("name"),
                english_name: row.get("english_name"),
                ingredients: row.get("ingredients"),
                dosage_form: row.get("dosage_form"),
                manufacturer: row.get("manufacturer"),
                unit: row.get("unit"),
                selling_price: row.get("unit_price"),
                nhi_price: row.get("nhi_price"),
                
                stock_quantity: row.get("stock_quantity"),
                description: row.get("description"),
                is_active: row.get("is_active"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            }
        }))
    }

    async fn update(&self, id: i64, product: UpdateProduct) -> Result<Product, AppError> {
        let mut updates = Vec::new();
        let mut params: Vec<String> = Vec::new();

        if let Some(nhi_code) = &product.nhi_code {
            updates.push(format!("nhi_code = ${}", params.len() + 1));
            params.push(nhi_code.clone());
        }
        if let Some(name) = &product.name {
            updates.push(format!("name = ${}", params.len() + 1));
            params.push(name.clone());
        }
        if let Some(manufacturer) = &product.manufacturer {
            updates.push(format!("manufacturer = ${}", params.len() + 1));
            params.push(manufacturer.clone());
        }
        if let Some(unit) = &product.unit {
            updates.push(format!("unit = ${}", params.len() + 1));
            params.push(unit.clone());
        }
        if let Some(selling_price) = &product.selling_price {
            updates.push(format!("selling_price = ${}", params.len() + 1));
            params.push(selling_price.to_string());
        }
        if let Some(stock_quantity) = &product.stock_quantity {
            updates.push(format!("stock_quantity = ${}", params.len() + 1));
            params.push(stock_quantity.to_string());
        }
        if let Some(description) = &product.description {
            updates.push(format!("description = ${}", params.len() + 1));
            params.push(description.clone());
        }
        if let Some(is_active) = &product.is_active {
            updates.push(format!("is_active = ${}", params.len() + 1));
            params.push((*is_active as i32).to_string());
        }

        if updates.is_empty() {
            // 如果沒有要更新的欄位，只更新 updated_at
            sqlx::query("UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = $1")
                .bind(id)
                .execute(&*self.pool)
                .await
                .map_err(|e| {
                    tracing::error!("Failed to update product: {}", e);
                    AppError::Database(e)
                })?;
        } else {
            updates.push("updated_at = CURRENT_TIMESTAMP".to_string());
            let query = format!("UPDATE products SET {} WHERE id = ${}", updates.join(", "), params.len() + 1);
            let mut query_builder = sqlx::query(&query);
            
            // 綁定所有參數
            for param in &params {
                query_builder = query_builder.bind(param);
            }
            query_builder = query_builder.bind(id);

            query_builder
                .execute(&*self.pool)
                .await
                .map_err(|e| {
                    tracing::error!("Failed to update product: {}", e);
                    AppError::Database(e)
                })?;
        }

        self.find_by_id(id).await?.ok_or_else(|| {
            AppError::NotFound("Product not found after update".to_string())
        })
    }

    async fn delete(&self, id: i64) -> Result<bool, AppError> {
        let result = sqlx::query("DELETE FROM products WHERE id = $1")
            .bind(id)
            .execute(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to delete product: {}", e);
                AppError::Database(e)
            })?;

        Ok(result.rows_affected() > 0)
    }

    async fn list(&self, filter: Option<ProductFilter>, limit: Option<i32>, offset: Option<i32>) -> Result<Vec<Product>, AppError> {
        let limit = limit.unwrap_or(50);
        let offset = offset.unwrap_or(0);

        let mut query = String::from(
            r#"
            SELECT p.id, p.nhi_code, p.name, p.english_name, p.ingredients, p.dosage_form, 
                   p.manufacturer, p.unit, p.selling_price as unit_price, 
                   p.nhi_price, p.stock_quantity, p.description, p.is_active, p.created_at, p.updated_at
            FROM products p
            WHERE p.is_active = true
            "#
        );

        let mut conditions = Vec::new();
        let mut param_count = 1;

        // 處理搜尋條件
        if let Some(ref filter_data) = filter {
            if let Some(ref search) = filter_data.search {
                if !search.trim().is_empty() {
                    conditions.push(format!(
                        "(p.name ILIKE ${} OR p.english_name ILIKE ${} OR p.nhi_code ILIKE ${} OR p.manufacturer ILIKE ${})",
                        param_count, param_count + 1, param_count + 2, param_count + 3
                    ));
                    param_count += 4;
                }
            }
            
            if let Some(ref manufacturer) = filter_data.manufacturer {
                if !manufacturer.trim().is_empty() {
                    conditions.push(format!("p.manufacturer ILIKE ${}", param_count));
                    param_count += 1;
                }
            }
        }

        if !conditions.is_empty() {
            query.push_str(" AND ");
            query.push_str(&conditions.join(" AND "));
        }

        query.push_str(&format!(" ORDER BY p.id ASC LIMIT ${} OFFSET ${}", param_count, param_count + 1));

        let mut query_builder = sqlx::query(&query);

        // 綁定搜尋參數
        if let Some(ref filter_data) = filter {
            if let Some(ref search) = filter_data.search {
                if !search.trim().is_empty() {
                    let search_pattern = format!("%{}%", search);
                    query_builder = query_builder
                        .bind(search_pattern.clone())  // name
                        .bind(search_pattern.clone())  // english_name
                        .bind(search_pattern.clone())  // nhi_code
                        .bind(search_pattern);         // manufacturer
                }
            }
            
            if let Some(ref manufacturer) = filter_data.manufacturer {
                if !manufacturer.trim().is_empty() {
                    let manufacturer_pattern = format!("%{}%", manufacturer);
                    query_builder = query_builder.bind(manufacturer_pattern);
                }
            }
        }

        // 綁定 LIMIT 和 OFFSET
        query_builder = query_builder.bind(limit).bind(offset);

        let rows = query_builder
            .fetch_all(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to list products: {}", e);
                AppError::Database(e)
            })?;

        Ok(rows
            .into_iter()
            .map(|row| {
                
                Product {
                    id: row.get("id"),
                    nhi_code: row.get("nhi_code"),
                    name: row.get("name"),
                    english_name: row.get("english_name"),
                    ingredients: row.get("ingredients"),
                    dosage_form: row.get("dosage_form"),
                    manufacturer: row.get("manufacturer"),
                    unit: row.get("unit"),
                    selling_price: row.get("unit_price"),
                nhi_price: row.get("nhi_price"),
                    
                    stock_quantity: row.get("stock_quantity"),
                    description: row.get("description"),
                    is_active: row.get("is_active"),
                    created_at: row.get("created_at"),
                    updated_at: row.get("updated_at"),
                }
            })
            .collect())
    }

    async fn update_stock(&self, id: i64, quantity: i32) -> Result<Product, AppError> {
        sqlx::query("UPDATE products SET stock_quantity = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2")
            .bind(quantity)
            .bind(id)
            .execute(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to update product stock: {}", e);
                AppError::Database(e)
            })?;

        self.find_by_id(id).await?.ok_or_else(|| {
            AppError::NotFound("Product not found after stock update".to_string())
        })
    }

    async fn find_low_stock(&self) -> Result<Vec<Product>, AppError> {
        let rows = sqlx::query(
            r#"
            SELECT id, nhi_code, name, english_name, ingredients, dosage_form, manufacturer, unit, selling_price, stock_quantity, description, is_active, created_at, updated_at
            FROM products
            WHERE stock_quantity <= 10 AND is_active = true
            ORDER BY stock_quantity ASC
            "#
        )
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to find low stock products: {}", e);
            AppError::Database(e)
        })?;

        Ok(rows
            .into_iter()
            .map(|row| {
                
                Product {
                    id: row.get("id"),
                    nhi_code: row.get("nhi_code"),
                    name: row.get("name"),
                    english_name: row.get("english_name"),
                    ingredients: row.get("ingredients"),
                    dosage_form: row.get("dosage_form"),
                    manufacturer: row.get("manufacturer"),
                    unit: row.get("unit"),
                    selling_price: row.get("unit_price"),
                nhi_price: row.get("nhi_price"),
                    
                    stock_quantity: row.get("stock_quantity"),
                    description: row.get("description"),
                    is_active: row.get("is_active"),
                    created_at: row.get("created_at"),
                    updated_at: row.get("updated_at"),
                }
            })
            .collect())
    }

    async fn bulk_upsert(&self, products: Vec<crate::models::product::ProductImportData>) -> Result<usize, AppError> {
        let mut tx = self.pool.begin().await.map_err(|e| {
            tracing::error!("Failed to begin transaction: {}", e);
            AppError::Database(e)
        })?;

        let mut imported_count = 0;

        for product in products {
            // 檢查產品是否已存在
            let existing = sqlx::query(
                "SELECT id FROM products WHERE nhi_code = $1"
            )
            .bind(&product.nhi_code)
            .fetch_optional(&mut *tx)
            .await
            .map_err(|e| {
                tracing::error!("Failed to check existing product: {}", e);
                AppError::Database(e)
            })?;

            if let Some(row) = existing {
                // 更新現有產品
                let id: i64 = row.get("id");
                sqlx::query(
                    r#"
                    UPDATE products 
                    SET name = $1, english_name = $2, ingredients = $3, dosage_form = $4, manufacturer = $5, unit = $6, selling_price = $7, 
                        stock_quantity = $8, description = $9, updated_at = CURRENT_TIMESTAMP
                    WHERE id = $10
                    "#
                )
                .bind(&product.name)
                .bind(&product.english_name)
                .bind(&product.ingredients)
                .bind(&product.dosage_form)
                .bind(&product.manufacturer)
                .bind(&product.unit)
                .bind(product.selling_price)
                .bind(product.stock_quantity)
                .bind(&product.description)
                .bind(id)
                .execute(&mut *tx)
                .await
                .map_err(|e| {
                    tracing::error!("Failed to update product: {}", e);
                    AppError::Database(e)
                })?;
            } else {
                // 插入新產品
                sqlx::query(
                    r#"
                    INSERT INTO products (nhi_code, name, english_name, ingredients, dosage_form, manufacturer, unit, selling_price, stock_quantity, description)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    "#
                )
                .bind(&product.nhi_code)
                .bind(&product.name)
                .bind(&product.english_name)
                .bind(&product.ingredients)
                .bind(&product.dosage_form)
                .bind(&product.manufacturer)
                .bind(&product.unit)
                .bind(product.selling_price)
                .bind(product.stock_quantity)
                .bind(&product.description)
                .execute(&mut *tx)
                .await
                .map_err(|e| {
                    tracing::error!("Failed to insert product: {}", e);
                    AppError::Database(e)
                })?;
            }

            imported_count += 1;
        }

        tx.commit().await.map_err(|e| {
            tracing::error!("Failed to commit transaction: {}", e);
            AppError::Database(e)
        })?;

        Ok(imported_count)
    }

    /// 批量更新庫存 - 性能優化版本
    async fn batch_update_stock(&self, updates: Vec<(i64, i32)>) -> Result<usize, AppError> {
        if updates.is_empty() {
            return Ok(0);
        }

        let mut tx = self.pool.begin().await.map_err(|e| {
            tracing::error!("Failed to begin transaction for batch stock update: {}", e);
            AppError::Database(e)
        })?;

        let mut updated_count = 0;

        // 使用批量處理，每次處理100個項目
        for batch in updates.chunks(100) {
            let mut query = String::from("UPDATE products SET stock_quantity = stock_quantity + (CASE id ");
            let mut params: Vec<i64> = Vec::new();
            
            for (product_id, quantity_change) in batch {
                query.push_str(&format!("WHEN ? THEN ? "));
                params.push(*product_id);
                params.push(*quantity_change as i64);
            }
            
            query.push_str("ELSE 0 END), updated_at = CURRENT_TIMESTAMP WHERE id IN (");
            
            // 添加 IN 子句的占位符
            let placeholders: Vec<String> = batch.iter().map(|_| "?".to_string()).collect();
            query.push_str(&placeholders.join(", "));
            query.push(')');

            // 添加 WHERE 子句的參數
            for (product_id, _) in batch {
                params.push(*product_id);
            }

            let mut query_builder = sqlx::query(&query);
            for param in params {
                query_builder = query_builder.bind(param);
            }
            
            let result = query_builder
                .execute(&mut *tx)
                .await
                .map_err(|e| {
                    tracing::error!("Failed to batch update stock: {}", e);
                    AppError::Database(e)
                })?;

            updated_count += result.rows_affected() as usize;
        }

        tx.commit().await.map_err(|e| {
            tracing::error!("Failed to commit batch stock update transaction: {}", e);
            AppError::Database(e)
        })?;

        tracing::info!("Batch updated stock for {} products", updated_count);
        Ok(updated_count)
    }

    /// 批量預留庫存 - 用於訂單確認時一次性檢查和預留多個商品
    async fn batch_reserve_stock(&self, reservations: Vec<(i64, i32)>) -> Result<bool, AppError> {
        if reservations.is_empty() {
            return Ok(true);
        }

        let mut tx = self.pool.begin().await.map_err(|e| {
            tracing::error!("Failed to begin transaction for batch stock reservation: {}", e);
            AppError::Database(e)
        })?;

        // 首先檢查所有商品的庫存是否足夠
        for (product_id, required_quantity) in &reservations {
            let row = sqlx::query(
                "SELECT stock_quantity FROM products WHERE id = $1 AND is_active = true"
            )
            .bind(product_id)
            .fetch_optional(&mut *tx)
            .await
            .map_err(|e| {
                tracing::error!("Failed to check stock for product {}: {}", product_id, e);
                AppError::Database(e)
            })?;

            match row {
                Some(row) => {
                    let current_stock: i32 = row.get("stock_quantity");
                    if current_stock < *required_quantity {
                        tracing::warn!(
                            "Insufficient stock for product {}: required {}, available {}",
                            product_id, required_quantity, current_stock
                        );
                        return Ok(false);
                    }
                }
                None => {
                    tracing::warn!("Product {} not found or inactive", product_id);
                    return Ok(false);
                }
            }
        }

        // 如果所有商品庫存都足夠，則批量扣減庫存
        let stock_updates: Vec<(i64, i32)> = reservations
            .into_iter()
            .map(|(id, qty)| (id, -qty)) // 負數表示扣減
            .collect();

        for (product_id, quantity_change) in stock_updates {
            sqlx::query(
                r#"
                UPDATE products 
                SET stock_quantity = stock_quantity + $1, 
                    updated_at = CURRENT_TIMESTAMP 
                WHERE id = $2
                "#
            )
            .bind(quantity_change)
            .bind(product_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| {
                tracing::error!("Failed to update stock for product {}: {}", product_id, e);
                AppError::Database(e)
            })?;
        }

        tx.commit().await.map_err(|e| {
            tracing::error!("Failed to commit batch stock reservation transaction: {}", e);
            AppError::Database(e)
        })?;

        Ok(true)
    }
}