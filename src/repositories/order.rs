use crate::{
    models::{Order, OrderItem, OrderDetails, OrderItemDetails, OrderStatus, OrderFilter},
    database::DbPool,
    error::AppError,
};
use async_trait::async_trait;
use sqlx::Row;
use rust_decimal::Decimal;


#[async_trait]
pub trait OrderRepository: Send + Sync {
    async fn create_order(&self, order: Order) -> Result<Order, AppError>;
    async fn create_order_items(&self, order_id: i64, items: Vec<OrderItem>) -> Result<Vec<OrderItem>, AppError>;
    async fn find_by_id(&self, id: i64) -> Result<Option<Order>, AppError>;
    async fn find_by_order_number(&self, order_number: &str) -> Result<Option<Order>, AppError>;
    async fn update_status(&self, id: i64, status: OrderStatus) -> Result<Order, AppError>;
    async fn list_by_user(&self, user_id: i64, filter: Option<OrderFilter>, limit: Option<i32>, offset: Option<i32>) -> Result<Vec<Order>, AppError>;
    async fn list_all_with_users(&self, filter: Option<OrderFilter>, limit: Option<i32>, offset: Option<i32>) -> Result<Vec<serde_json::Value>, AppError>;
    async fn get_order_details(&self, id: i64) -> Result<Option<OrderDetails>, AppError>;
    async fn get_order_items(&self, order_id: i64) -> Result<Vec<OrderItem>, AppError>;
    #[allow(dead_code)]
    async fn update_order(&self, id: i64, total_amount: Decimal) -> Result<Order, AppError>;
    #[allow(dead_code)]
    async fn delete_order(&self, id: i64) -> Result<bool, AppError>;
    async fn count_by_user(&self, user_id: i64, filter: Option<OrderFilter>) -> Result<i64, AppError>;
}

pub struct PostgresOrderRepository {
    pool: DbPool,
}

impl PostgresOrderRepository {
    pub fn new(pool: DbPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl OrderRepository for PostgresOrderRepository {
    async fn create_order(&self, order: Order) -> Result<Order, AppError> {
        tracing::debug!("Creating order with order_number: {}", order.order_number);
        tracing::debug!("Order details: user_id={}, status={}, total_amount={}, notes={:?}", 
            order.user_id, order.status.to_string(), order.total_amount, order.notes);
        
        // 使用 RETURNING id 來獲取插入後的 ID
        let id: i64 = sqlx::query_scalar(
            r#"
            INSERT INTO orders (order_number, user_id, status, total_amount, notes, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id
            "#
        )
        .bind(&order.order_number)
        .bind(order.user_id)
        .bind(order.status.to_string())
        .bind(order.total_amount)
        .bind(&order.notes)
        .bind(order.created_at)
        .bind(order.updated_at)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to insert order: {}", e);
            AppError::Database(e)
        })?;

        tracing::debug!("Order created with id: {}", id);

        // 返回創建的訂單，但使用傳入的資料加上新的 ID
        Ok(Order {
            id,
            order_number: order.order_number,
            user_id: order.user_id,
            status: order.status,
            total_amount: order.total_amount,
            notes: order.notes,
            created_at: order.created_at,
            updated_at: order.updated_at,
        })
    }

    async fn create_order_items(&self, order_id: i64, items: Vec<OrderItem>) -> Result<Vec<OrderItem>, AppError> {
        let mut created_items = Vec::new();

        for item in items {
            let id: i64 = sqlx::query_scalar(
                r#"
                INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id
                "#
            )
            .bind(order_id)
            .bind(item.product_id)
            .bind(item.quantity)
            .bind(item.unit_price)
            .bind(item.subtotal)
            .fetch_one(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to create order item: {}", e);
                AppError::Database(e)
            })?;

            let created_item = OrderItem {
                id,
                order_id,
                product_id: item.product_id,
                quantity: item.quantity,
                unit_price: item.unit_price,
                subtotal: item.subtotal,
            };

            created_items.push(created_item);
        }

        Ok(created_items)
    }

    async fn find_by_id(&self, id: i64) -> Result<Option<Order>, AppError> {
        let row = sqlx::query(
            r#"
            SELECT id, order_number, user_id, status, total_amount, notes, created_at, updated_at
            FROM orders WHERE id = $1
            "#
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to find order by id: {}", e);
            AppError::Database(e)
        })?;

        Ok(row.map(|row| {
            let total_amount: Decimal = row.get("total_amount");
            let status_str: String = row.get("status");
            
            Order {
                id: row.get("id"),
                order_number: row.get("order_number"),
                user_id: row.get("user_id"),
                status: OrderStatus::from(status_str),
                total_amount,
                notes: row.get("notes"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            }
        }))
    }

    async fn find_by_order_number(&self, order_number: &str) -> Result<Option<Order>, AppError> {
        let row = sqlx::query(
            r#"
            SELECT id, order_number, user_id, status, total_amount, notes, created_at, updated_at
            FROM orders WHERE order_number = $1
            "#
        )
        .bind(order_number)
        .fetch_optional(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to find order by order_number: {}", e);
            AppError::Database(e)
        })?;

        Ok(row.map(|row| {
            let total_amount: Decimal = row.get("total_amount");
            let status_str: String = row.get("status");
            
            Order {
                id: row.get("id"),
                order_number: row.get("order_number"),
                user_id: row.get("user_id"),
                status: OrderStatus::from(status_str),
                total_amount,
                notes: row.get("notes"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            }
        }))
    }

    async fn update_status(&self, id: i64, status: OrderStatus) -> Result<Order, AppError> {
        sqlx::query(
            r#"
            UPDATE orders 
            SET status = $1, updated_at = CURRENT_TIMESTAMP
            WHERE id = $2
            "#
        )
        .bind(status.to_string())
        .bind(id)
        .execute(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to update order status: {}", e);
            AppError::Database(e)
        })?;

        self.find_by_id(id).await?.ok_or_else(|| {
            AppError::NotFound("Order not found after status update".to_string())
        })
    }

    async fn list_by_user(&self, user_id: i64, _filter: Option<OrderFilter>, limit: Option<i32>, offset: Option<i32>) -> Result<Vec<Order>, AppError> {
        let limit = limit.unwrap_or(50);
        let offset = offset.unwrap_or(0);

        // Simple query without dynamic parameter building for now
        let rows = sqlx::query(
            r#"
            SELECT id, order_number, user_id, status, total_amount, notes, created_at, updated_at
            FROM orders
            WHERE user_id = $1
            ORDER BY created_at DESC 
            LIMIT $2 OFFSET $3
            "#
        )
        .bind(user_id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to list orders by user: {}", e);
            AppError::Database(e)
        })?;

        Ok(rows
            .into_iter()
            .map(|row| {
                let total_amount: Decimal = row.get("total_amount");
                let status_str: String = row.get("status");
                
                Order {
                    id: row.get("id"),
                    order_number: row.get("order_number"),
                    user_id: row.get("user_id"),
                    status: OrderStatus::from(status_str),
                    total_amount,
                    notes: row.get("notes"),
                    created_at: row.get("created_at"),
                    updated_at: row.get("updated_at"),
                }
            })
            .collect())
    }

    async fn list_all_with_users(&self, filter: Option<OrderFilter>, limit: Option<i32>, offset: Option<i32>) -> Result<Vec<serde_json::Value>, AppError> {
        let limit = limit.unwrap_or(50);
        let offset = offset.unwrap_or(0);

        // 構建動態查詢
        let mut query = String::from(
            r#"
            SELECT 
                o.id, o.order_number, o.user_id, o.status, o.total_amount, o.notes, o.created_at, o.updated_at,
                u.username, u.pharmacy_name
            FROM orders o
            JOIN users u ON o.user_id = u.id
            "#
        );

        let mut conditions = Vec::new();
        let mut param_count = 0;

        // 添加篩選條件
        if let Some(filter) = &filter {
            if let Some(status) = &filter.status {
                param_count += 1;
                conditions.push(format!("o.status = ${}", param_count));
            }

            if let Some(start_date) = &filter.start_date {
                param_count += 1;
                conditions.push(format!("o.created_at >= ${}", param_count));
            }

            if let Some(end_date) = &filter.end_date {
                param_count += 1;
                conditions.push(format!("o.created_at <= ${}", param_count));
            }
        }

        // 添加WHERE子句
        if !conditions.is_empty() {
            query.push_str(" WHERE ");
            query.push_str(&conditions.join(" AND "));
        }

        // 添加排序和分頁
        query.push_str(" ORDER BY o.created_at DESC");
        param_count += 1;
        query.push_str(&format!(" LIMIT ${}", param_count));
        param_count += 1;
        query.push_str(&format!(" OFFSET ${}", param_count));

        // 構建查詢
        let mut sqlx_query = sqlx::query(&query);

        // 綁定篩選參數
        if let Some(filter) = &filter {
            if let Some(status) = &filter.status {
                sqlx_query = sqlx_query.bind(status.to_string());
            }

            if let Some(start_date) = &filter.start_date {
                sqlx_query = sqlx_query.bind(start_date);
            }

            if let Some(end_date) = &filter.end_date {
                sqlx_query = sqlx_query.bind(end_date);
            }
        }

        // 綁定分頁參數
        sqlx_query = sqlx_query.bind(limit).bind(offset);

        let rows = sqlx_query
            .fetch_all(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to list all orders with users: {}", e);
                AppError::Database(e)
            })?;

        let orders: Vec<serde_json::Value> = rows
            .into_iter()
            .map(|row| {
                let total_amount: Decimal = row.get("total_amount");
                let status_str: String = row.get("status");
                
                serde_json::json!({
                    "id": row.get::<i64, _>("id"),
                    "order_number": row.get::<String, _>("order_number"),
                    "user_id": row.get::<i64, _>("user_id"),
                    "status": status_str,
                    "total_amount": total_amount,
                    "notes": row.get::<Option<String>, _>("notes"),
                    "created_at": row.get::<chrono::DateTime<chrono::Utc>, _>("created_at"),
                    "updated_at": row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at"),
                    "username": row.get::<String, _>("username"),
                    "pharmacy_name": row.get::<Option<String>, _>("pharmacy_name")
                })
            })
            .collect();

        Ok(orders)
    }

    async fn get_order_details(&self, id: i64) -> Result<Option<OrderDetails>, AppError> {
        let order = match self.find_by_id(id).await? {
            Some(order) => order,
            None => return Ok(None),
        };

        let rows = sqlx::query(
            r#"
            SELECT 
                oi.id, oi.order_id, oi.product_id, oi.quantity, oi.unit_price, oi.total_price as subtotal,
                COALESCE(p.name, '(已下架)') as product_name, COALESCE(p.nhi_code, '') as product_nhi_code
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = $1
            ORDER BY oi.id ASC
            "#
        )
        .bind(id)
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to get order details: {}", e);
            AppError::Database(e)
        })?;

        let items: Vec<OrderItemDetails> = rows
            .into_iter()
            .map(|row| {
                let unit_price: Decimal = row.get("unit_price");
                let subtotal: Decimal = row.get("subtotal");
                
                OrderItemDetails {
                    item: OrderItem {
                        id: row.get("id"),
                        order_id: row.get("order_id"),
                        product_id: row.get("product_id"),
                        quantity: row.get("quantity"),
                        unit_price,
                        subtotal,
                    },
                    product_name: row.get("product_name"),
                    product_nhi_code: row.get("product_nhi_code"),
                }
            })
            .collect();

        Ok(Some(OrderDetails { order, items }))
    }

    async fn get_order_items(&self, order_id: i64) -> Result<Vec<OrderItem>, AppError> {
        let rows = sqlx::query(
            r#"
            SELECT id, order_id, product_id, quantity, unit_price, total_price as subtotal
            FROM order_items WHERE order_id = $1
            ORDER BY id ASC
            "#
        )
        .bind(order_id)
        .fetch_all(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to get order items: {}", e);
            AppError::Database(e)
        })?;

        Ok(rows
            .into_iter()
            .map(|row| {
                let unit_price: Decimal = row.get("unit_price");
                let subtotal: Decimal = row.get("subtotal");
                
                OrderItem {
                    id: row.get("id"),
                    order_id: row.get("order_id"),
                    product_id: row.get("product_id"),
                    quantity: row.get("quantity"),
                    unit_price,
                    subtotal,
                }
            })
            .collect())
    }

    async fn update_order(&self, id: i64, total_amount: Decimal) -> Result<Order, AppError> {
        sqlx::query(
            r#"
            UPDATE orders 
            SET total_amount = $1, updated_at = CURRENT_TIMESTAMP
            WHERE id = $2
            "#
        )
        .bind(total_amount)
        .bind(id)
        .execute(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to update order: {}", e);
            AppError::Database(e)
        })?;

        self.find_by_id(id).await?.ok_or_else(|| {
            AppError::NotFound("Order not found after update".to_string())
        })
    }

    async fn delete_order(&self, id: i64) -> Result<bool, AppError> {
        // 先刪除訂單項目
        sqlx::query("DELETE FROM order_items WHERE order_id = $1")
            .bind(id)
            .execute(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to delete order items: {}", e);
                AppError::Database(e)
            })?;

        // 再刪除訂單
        let result = sqlx::query("DELETE FROM orders WHERE id = $1")
            .bind(id)
            .execute(&*self.pool)
            .await
            .map_err(|e| {
                tracing::error!("Failed to delete order: {}", e);
                AppError::Database(e)
            })?;

        Ok(result.rows_affected() > 0)
    }

    async fn count_by_user(&self, user_id: i64, _filter: Option<OrderFilter>) -> Result<i64, AppError> {
        // 簡化查詢，暫時忽略 filter 參數
        let count: i64 = sqlx::query_scalar(
            r#"
            SELECT COUNT(*)::BIGINT FROM orders WHERE user_id = $1
            "#
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await
        .map_err(|e| {
            tracing::error!("Failed to count orders by user: {}", e);
            AppError::Database(e)
        })?;

        Ok(count)
    }
}

