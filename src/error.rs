use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use thiserror::Error;
use tracing::{error, warn, info};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::error::Error;
// use crate::security::{DataMasker, DataMaskingConfig};

pub type AppResult<T> = Result<T, AppError>;

/// 錯誤回應格式
#[derive(serde::Serialize)]
pub struct ErrorResponse {
    pub error: String,
    pub error_code: String,
    pub status: u16,
    pub request_id: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<serde_json::Value>,
}

/// 錯誤嚴重程度
#[derive(Debug, Clone, Copy)]
pub enum ErrorSeverity {
    Low,      // 使用者輸入錯誤等
    Medium,   // 業務邏輯錯誤
    High,     // 系統錯誤
    Critical, // 嚴重系統故障
}

#[derive(Debug, Error)]
#[allow(dead_code)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("Authentication error: {0}")]
    Authentication(String),
    
    #[error("Authorization error: {0}")]
    Authorization(String),
    
    #[error("Forbidden: {0}")]
    Forbidden(String),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("File processing error: {0}")]
    FileProcessing(String),
    
    #[error("Notification error: {0}")]
    Notification(String),
    
    #[error("External service error: {0}")]
    ExternalService(String),
    
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Internal server error: {0}")]
    Internal(String),
    
    #[error("Configuration error: {0}")]
    Configuration(String),
    
    #[error("Cloud storage error: {0}")]
    CloudStorage(String),
    
    #[error("JWT error: {0}")]
    Jwt(#[from] jsonwebtoken::errors::Error),
    
    #[error("Bcrypt error: {0}")]
    Bcrypt(#[from] bcrypt::BcryptError),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Reqwest error: {0}")]
    Reqwest(#[from] reqwest::Error),
    
    #[error("Database error: {0}")]
    DatabaseError(#[from] crate::database::error::DatabaseError),
    
    #[error("Business logic error: {0}")]
    BusinessLogic(String),
    
    #[error("Rate limit exceeded: {0}")]
    RateLimit(String),
    
    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),
    
    #[error("Timeout error: {0}")]
    Timeout(String),
}

impl AppError {
    /// 取得錯誤代碼
    pub fn error_code(&self) -> &'static str {
        match self {
            AppError::Database(_) => "DATABASE_ERROR",
            AppError::Authentication(_) => "AUTHENTICATION_ERROR",
            AppError::Authorization(_) => "AUTHORIZATION_ERROR",
            AppError::Forbidden(_) => "FORBIDDEN_ERROR",
            AppError::Validation(_) => "VALIDATION_ERROR",
            AppError::NotFound(_) => "NOT_FOUND",
            AppError::FileProcessing(_) => "FILE_PROCESSING_ERROR",
            AppError::Notification(_) => "NOTIFICATION_ERROR",
            AppError::ExternalService(_) => "EXTERNAL_SERVICE_ERROR",
            AppError::Internal(_) => "INTERNAL_ERROR",
            AppError::Configuration(_) => "CONFIGURATION_ERROR",
            AppError::CloudStorage(_) => "CLOUD_STORAGE_ERROR",
            AppError::Jwt(_) => "JWT_ERROR",
            AppError::Bcrypt(_) => "BCRYPT_ERROR",
            AppError::Io(_) => "IO_ERROR",
            AppError::Reqwest(_) => "REQWEST_ERROR",
            AppError::DatabaseError(_) => "DATABASE_OPERATION_ERROR",
            AppError::BusinessLogic(_) => "BUSINESS_LOGIC_ERROR",
            AppError::RateLimit(_) => "RATE_LIMIT_ERROR",
            AppError::ServiceUnavailable(_) => "SERVICE_UNAVAILABLE",
            AppError::Timeout(_) => "TIMEOUT_ERROR",
        }
    }

    /// 取得錯誤嚴重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            AppError::Validation(_) | AppError::NotFound(_) | AppError::FileProcessing(_) => ErrorSeverity::Low,
            AppError::Authentication(_) | AppError::Authorization(_) | AppError::Forbidden(_) | AppError::BusinessLogic(_) | AppError::RateLimit(_) => ErrorSeverity::Medium,
            AppError::Database(_) | AppError::DatabaseError(_) | AppError::Notification(_) | AppError::ExternalService(_) | AppError::CloudStorage(_) | AppError::Reqwest(_) | AppError::ServiceUnavailable(_) | AppError::Timeout(_) => ErrorSeverity::High,
            AppError::Internal(_) | AppError::Configuration(_) | AppError::Jwt(_) | AppError::Bcrypt(_) | AppError::Io(_) => ErrorSeverity::Critical,
        }
    }

    /// 取得使用者友善的錯誤訊息
    pub fn user_message(&self) -> &str {
        match self {
            AppError::Database(_) => "資料庫錯誤，請稍後再試",
            AppError::Authentication(_) => "認證失敗，請重新登入",
            AppError::Authorization(_) => "權限不足，無法執行此操作",
            AppError::Forbidden(_) => "禁止訪問，權限不足",
            AppError::Validation(msg) => msg,
            AppError::NotFound(msg) => msg,
            AppError::FileProcessing(msg) => msg,
            AppError::Notification(_) => "通知服務暫時無法使用",
            AppError::ExternalService(_) => "外部服務暫時無法使用",
            AppError::Internal(_) => "系統內部錯誤，請聯繫管理員",
            AppError::Configuration(_) => "系統配置錯誤，請聯繫管理員",
            AppError::CloudStorage(_) => "雲端儲存服務暫時無法使用",
            AppError::Jwt(_) => "認證令牌無效，請重新登入",
            AppError::Bcrypt(_) => "密碼處理錯誤",
            AppError::Io(_) => "檔案系統錯誤",
            AppError::Reqwest(_) => "網路請求錯誤",
            AppError::DatabaseError(_) => "資料庫操作失敗",
            AppError::BusinessLogic(msg) => msg,
            AppError::RateLimit(_) => "請求過於頻繁，請稍後再試",
            AppError::ServiceUnavailable(_) => "服務暫時無法使用",
            AppError::Timeout(_) => "請求超時，請稍後再試",
        }
    }

    /// 是否應該記錄詳細錯誤
    pub fn should_log_details(&self) -> bool {
        matches!(self.severity(), ErrorSeverity::High | ErrorSeverity::Critical)
    }

    /// 取得 HTTP 狀態碼
    #[allow(dead_code)]
    pub fn status_code(&self) -> StatusCode {
        match self {
            AppError::Database(_) | AppError::DatabaseError(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::Authentication(_) | AppError::Jwt(_) => StatusCode::UNAUTHORIZED,
            AppError::Authorization(_) => StatusCode::FORBIDDEN,
            AppError::Validation(_) | AppError::FileProcessing(_) => StatusCode::BAD_REQUEST,
            AppError::NotFound(_) => StatusCode::NOT_FOUND,
            AppError::RateLimit(_) => StatusCode::TOO_MANY_REQUESTS,
            AppError::ServiceUnavailable(_) | AppError::ExternalService(_) => StatusCode::SERVICE_UNAVAILABLE,
            AppError::Timeout(_) => StatusCode::REQUEST_TIMEOUT,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        }
    }

    /// 記錄錯誤
    pub fn log_error(&self, request_id: &str) {
        match self.severity() {
            ErrorSeverity::Low => {
                warn!(
                    request_id = request_id,
                    error_code = self.error_code(),
                    error = %self,
                    "Low severity error occurred"
                );
            }
            ErrorSeverity::Medium => {
                warn!(
                    request_id = request_id,
                    error_code = self.error_code(),
                    error = %self,
                    "Medium severity error occurred"
                );
            }
            ErrorSeverity::High => {
                error!(
                    request_id = request_id,
                    error_code = self.error_code(),
                    error = %self,
                    "High severity error occurred"
                );
            }
            ErrorSeverity::Critical => {
                error!(
                    request_id = request_id,
                    error_code = self.error_code(),
                    error = %self,
                    "Critical error occurred"
                );
            }
        }
    }
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let request_id = Uuid::new_v4().to_string();
        
        // 記錄錯誤
        self.log_error(&request_id);

        let status = match self {
            AppError::Database(_) | AppError::DatabaseError(_) => StatusCode::INTERNAL_SERVER_ERROR,
            AppError::Authentication(_) | AppError::Jwt(_) => StatusCode::UNAUTHORIZED,
            AppError::Authorization(_) => StatusCode::FORBIDDEN,
            AppError::Validation(_) | AppError::FileProcessing(_) => StatusCode::BAD_REQUEST,
            AppError::NotFound(_) => StatusCode::NOT_FOUND,
            AppError::RateLimit(_) => StatusCode::TOO_MANY_REQUESTS,
            AppError::ServiceUnavailable(_) | AppError::ExternalService(_) => StatusCode::SERVICE_UNAVAILABLE,
            AppError::Timeout(_) => StatusCode::REQUEST_TIMEOUT,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        };

        let error_response = ErrorResponse {
            error: self.user_message().to_string(),
            error_code: self.error_code().to_string(),
            status: status.as_u16(),
            request_id,
            details: if self.should_log_details() {
                Some(json!({
                    "internal_error": self.to_string(),
                    "severity": match self.severity() {
                        ErrorSeverity::Low => "low",
                        ErrorSeverity::Medium => "medium", 
                        ErrorSeverity::High => "high",
                        ErrorSeverity::Critical => "critical",
                    }
                }))
            } else {
                None
            },
        };

        (status, Json(error_response)).into_response()
    }
}
/// 錯誤處理輔助函數
#[allow(dead_code)]
pub mod helpers {
    use super::*;

    /// 建立驗證錯誤
    pub fn validation_error(message: impl Into<String>) -> AppError {
        AppError::Validation(message.into())
    }

    /// 建立未找到錯誤
    pub fn not_found_error(resource: impl Into<String>) -> AppError {
        AppError::NotFound(format!("{} 未找到", resource.into()))
    }

    /// 建立認證錯誤
    pub fn auth_error(message: impl Into<String>) -> AppError {
        AppError::Authentication(message.into())
    }

    /// 建立授權錯誤
    pub fn authorization_error(message: impl Into<String>) -> AppError {
        AppError::Authorization(message.into())
    }

    /// 建立業務邏輯錯誤
    pub fn business_logic_error(message: impl Into<String>) -> AppError {
        AppError::BusinessLogic(message.into())
    }

    /// 建立檔案處理錯誤
    pub fn file_processing_error(message: impl Into<String>) -> AppError {
        AppError::FileProcessing(message.into())
    }

    /// 建立通知錯誤
    pub fn notification_error(message: impl Into<String>) -> AppError {
        AppError::Notification(message.into())
    }

    /// 建立外部服務錯誤
    pub fn external_service_error(service: impl Into<String>, message: impl Into<String>) -> AppError {
        AppError::ExternalService(format!("{}: {}", service.into(), message.into()))
    }

    /// 建立雲端儲存錯誤
    pub fn cloud_storage_error(message: impl Into<String>) -> AppError {
        AppError::CloudStorage(message.into())
    }

    /// 建立內部錯誤
    pub fn internal_error(message: impl Into<String>) -> AppError {
        AppError::Internal(message.into())
    }

    /// 建立配置錯誤
    pub fn config_error(message: impl Into<String>) -> AppError {
        AppError::Configuration(message.into())
    }

    /// 建立速率限制錯誤
    pub fn rate_limit_error(message: impl Into<String>) -> AppError {
        AppError::RateLimit(message.into())
    }

    /// 建立服務不可用錯誤
    pub fn service_unavailable_error(service: impl Into<String>) -> AppError {
        AppError::ServiceUnavailable(format!("{} 服務暫時無法使用", service.into()))
    }

    /// 建立超時錯誤
    pub fn timeout_error(operation: impl Into<String>) -> AppError {
        AppError::Timeout(format!("{} 操作超時", operation.into()))
    }
}

/// 錯誤統計
#[derive(Debug, Default, Clone)]
#[allow(dead_code)]
pub struct ErrorStats {
    pub total_errors: u64,
    pub errors_by_code: std::collections::HashMap<String, u64>,
    pub errors_by_severity: std::collections::HashMap<String, u64>,
}

#[allow(dead_code)]
impl ErrorStats {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn record_error(&mut self, error: &AppError) {
        self.total_errors += 1;
        
        let error_code = error.error_code().to_string();
        *self.errors_by_code.entry(error_code).or_insert(0) += 1;
        
        let severity = match error.severity() {
            ErrorSeverity::Low => "low",
            ErrorSeverity::Medium => "medium",
            ErrorSeverity::High => "high", 
            ErrorSeverity::Critical => "critical",
        }.to_string();
        *self.errors_by_severity.entry(severity).or_insert(0) += 1;
    }
}

/// 錯誤上下文信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct ErrorContext {
    pub timestamp: DateTime<Utc>,
    pub request_id: Option<String>,
    pub user_id: Option<i64>,
    pub endpoint: Option<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub additional_data: HashMap<String, serde_json::Value>,
}

impl Default for ErrorContext {
    fn default() -> Self {
        Self {
            timestamp: Utc::now(),
            request_id: None,
            user_id: None,
            endpoint: None,
            ip_address: None,
            user_agent: None,
            additional_data: HashMap::new(),
        }
    }
}

impl ErrorContext {
    #[allow(dead_code)]
    pub fn new() -> Self {
        Self::default()
    }

    #[allow(dead_code)]
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }

    #[allow(dead_code)]
    pub fn with_user_id(mut self, user_id: i64) -> Self {
        self.user_id = Some(user_id);
        self
    }

    #[allow(dead_code)]
    pub fn with_endpoint(mut self, endpoint: String) -> Self {
        self.endpoint = Some(endpoint);
        self
    }

    #[allow(dead_code)]
    pub fn with_ip_address(mut self, ip_address: String) -> Self {
        self.ip_address = Some(ip_address);
        self
    }

    #[allow(dead_code)]
    pub fn with_user_agent(mut self, user_agent: String) -> Self {
        self.user_agent = Some(user_agent);
        self
    }

    #[allow(dead_code)]
    pub fn with_data<T: serde::Serialize>(mut self, key: String, value: T) -> Self {
        if let Ok(json_value) = serde_json::to_value(value) {
            self.additional_data.insert(key, json_value);
        }
        self
    }
}

/// 增強的錯誤追蹤器
#[allow(dead_code)]
pub struct ErrorTracker {
    // masker: DataMasker,
    stats: ErrorStats,
}

impl ErrorTracker {
    #[allow(dead_code)]
    pub fn new() -> Self {
        Self {
            // masker: DataMasker::new(DataMaskingConfig::default()),
            stats: ErrorStats::new(),
        }
    }

    /// 記錄錯誤並生成追蹤報告
    #[allow(dead_code)]
    pub fn track_error(&mut self, error: &AppError, context: ErrorContext) -> String {
        let error_id = Uuid::new_v4().to_string();
        
        // 記錄統計信息
        self.stats.record_error(error);
        
        // 創建詳細的錯誤報告
        let error_report = self.create_error_report(error, &context, &error_id);
        
        // 根據錯誤嚴重程度選擇日誌級別
        match error.severity() {
            ErrorSeverity::Low => {
                info!(
                    error_id = %error_id,
                    error_code = %error.error_code(),
                    "Low severity error occurred: {}",
                    error.to_string() // self.masker.auto_mask_text(&error.to_string())
                );
            }
            ErrorSeverity::Medium => {
                warn!(
                    error_id = %error_id,
                    error_code = %error.error_code(),
                    context = ?context,
                    "Medium severity error occurred: {}",
                    error.to_string() // self.masker.auto_mask_text(&error.to_string())
                );
            }
            ErrorSeverity::High => {
                error!(
                    error_id = %error_id,
                    error_code = %error.error_code(),
                    context = ?context,
                    "High severity error occurred: {}",
                    error.to_string() // self.masker.auto_mask_text(&error.to_string())
                );
            }
            ErrorSeverity::Critical => {
                error!(
                    error_id = %error_id,
                    error_code = %error.error_code(),
                    context = ?context,
                    "CRITICAL ERROR: {}",
                    error.to_string() // self.masker.auto_mask_text(&error.to_string())
                );
                
                // 對於關鍵錯誤，可以觸發告警系統
                self.trigger_alert(&error_report);
            }
        }
        
        error_id
    }

    /// 創建詳細的錯誤報告
    #[allow(dead_code)]
    fn create_error_report(&self, error: &AppError, context: &ErrorContext, error_id: &str) -> serde_json::Value {
        json!({
            "error_id": error_id,
            "error_code": error.error_code(),
            "error_type": format!("{:?}", error),
            "severity": match error.severity() {
                ErrorSeverity::Low => "low",
                ErrorSeverity::Medium => "medium", 
                ErrorSeverity::High => "high",
                ErrorSeverity::Critical => "critical"
            },
            "message": error.to_string(), // self.masker.auto_mask_text(&error.to_string()),
            "http_status": error.status_code().as_u16(),
            "context": context,
            "timestamp": context.timestamp,
            "stack_trace": self.extract_stack_trace(error),
            "similar_errors_count": self.stats.errors_by_code.get(error.error_code()).unwrap_or(&0),
            "system_info": self.get_system_info()
        })
    }

    /// 提取堆疊追蹤信息
    #[allow(dead_code)]
    fn extract_stack_trace(&self, error: &AppError) -> Option<String> {
        // 在這裡可以添加更複雜的堆疊追蹤邏輯
        // 目前返回錯誤的源鏈
        let mut sources = Vec::new();
        let mut current = error.source();
        
        while let Some(source) = current {
            sources.push(source.to_string()); // self.masker.auto_mask_text(&source.to_string()));
            current = source.source();
        }
        
        if sources.is_empty() {
            None
        } else {
            Some(sources.join(" -> "))
        }
    }

    /// 獲取系統信息
    #[allow(dead_code)]
    fn get_system_info(&self) -> serde_json::Value {
        json!({
            "version": env!("CARGO_PKG_VERSION"),
            "build_timestamp": "unknown",
            "rust_version": "unknown",
        })
    }

    /// 觸發告警（關鍵錯誤）
    #[allow(dead_code)]
    fn trigger_alert(&self, error_report: &serde_json::Value) {
        // 這裡可以集成告警系統（如發送郵件、Slack 通知等）
        error!(
            alert = "CRITICAL_ERROR",
            report = %error_report,
            "Critical error alert triggered"
        );
        
        // 可以添加更多告警邏輯：
        // - 發送郵件給管理員
        // - 發送 Slack/Teams 通知
        // - 寫入告警日誌文件
        // - 觸發監控系統告警
    }

    /// 獲取錯誤統計信息
    #[allow(dead_code)]
    pub fn get_stats(&self) -> &ErrorStats {
        &self.stats
    }

    /// 生成錯誤趨勢報告
    #[allow(dead_code)]
    pub fn generate_trend_report(&self) -> serde_json::Value {
        json!({
            "total_errors": self.stats.total_errors,
            "errors_by_code": self.stats.errors_by_code,
            "errors_by_severity": self.stats.errors_by_severity,
            "generated_at": Utc::now(),
            "top_error_codes": self.get_top_error_codes(5),
            "critical_error_rate": self.calculate_critical_error_rate(),
        })
    }

    /// 獲取前 N 個最常見的錯誤代碼
    #[allow(dead_code)]
    fn get_top_error_codes(&self, limit: usize) -> Vec<(String, u64)> {
        let mut errors: Vec<_> = self.stats.errors_by_code.iter()
            .map(|(code, count)| (code.clone(), *count))
            .collect();
        
        errors.sort_by(|a, b| b.1.cmp(&a.1));
        errors.truncate(limit);
        errors
    }

    /// 計算關鍵錯誤率
    #[allow(dead_code)]
    fn calculate_critical_error_rate(&self) -> f64 {
        if self.stats.total_errors == 0 {
            0.0
        } else {
            let critical_errors = self.stats.errors_by_severity.get("critical").unwrap_or(&0);
            (*critical_errors as f64) / (self.stats.total_errors as f64) * 100.0
        }
    }
}

/// 錯誤處理中間件助手
#[allow(dead_code)]
pub struct ErrorMiddlewareHelper;

impl ErrorMiddlewareHelper {
    /// 從 HTTP 請求中提取錯誤上下文
    #[allow(dead_code)]
    pub fn extract_context_from_request(request: &axum::http::Request<axum::body::Body>) -> ErrorContext {
        let mut context = ErrorContext::new();
        
        // 提取請求 ID
        if let Some(request_id) = request.headers().get("x-request-id") {
            if let Ok(id_str) = request_id.to_str() {
                context = context.with_request_id(id_str.to_string());
            }
        }
        
        // 提取端點信息
        context = context.with_endpoint(format!("{} {}", request.method(), request.uri()));
        
        // 提取 User-Agent
        if let Some(user_agent) = request.headers().get("user-agent") {
            if let Ok(agent_str) = user_agent.to_str() {
                context = context.with_user_agent(agent_str.to_string());
            }
        }
        
        // 提取 IP 地址（從多個可能的標頭）
        let ip_headers = ["x-forwarded-for", "x-real-ip", "cf-connecting-ip"];
        for header_name in &ip_headers {
            if let Some(ip_header) = request.headers().get(*header_name) {
                if let Ok(ip_str) = ip_header.to_str() {
                    context = context.with_ip_address(ip_str.split(',').next().unwrap_or(ip_str).trim().to_string());
                    break;
                }
            }
        }
        
        context
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_codes() {
        let validation_error = AppError::Validation("測試驗證錯誤".to_string());
        assert_eq!(validation_error.error_code(), "VALIDATION_ERROR");
        
        let not_found_error = AppError::NotFound("使用者未找到".to_string());
        assert_eq!(not_found_error.error_code(), "NOT_FOUND");
    }

    #[test]
    fn test_error_severity() {
        let validation_error = AppError::Validation("測試".to_string());
        assert!(matches!(validation_error.severity(), ErrorSeverity::Low));
        
        let auth_error = AppError::Authentication("測試".to_string());
        assert!(matches!(auth_error.severity(), ErrorSeverity::Medium));
        
        let db_error = AppError::Database(sqlx::Error::RowNotFound);
        assert!(matches!(db_error.severity(), ErrorSeverity::High));
        
        let internal_error = AppError::Internal("測試".to_string());
        assert!(matches!(internal_error.severity(), ErrorSeverity::Critical));
    }

    #[test]
    fn test_user_messages() {
        let validation_error = AppError::Validation("無效的電子郵件格式".to_string());
        assert_eq!(validation_error.user_message(), "無效的電子郵件格式");
        
        let db_error = AppError::Database(sqlx::Error::RowNotFound);
        assert_eq!(db_error.user_message(), "資料庫錯誤，請稍後再試");
    }

    #[test]
    fn test_should_log_details() {
        let validation_error = AppError::Validation("測試".to_string());
        assert!(!validation_error.should_log_details());
        
        let db_error = AppError::Database(sqlx::Error::RowNotFound);
        assert!(db_error.should_log_details());
    }

    #[test]
    fn test_helper_functions() {
        let validation_err = helpers::validation_error("測試驗證錯誤");
        assert!(matches!(validation_err, AppError::Validation(_)));
        
        let not_found_err = helpers::not_found_error("使用者");
        assert!(matches!(not_found_err, AppError::NotFound(_)));
        
        let auth_err = helpers::auth_error("認證失敗");
        assert!(matches!(auth_err, AppError::Authentication(_)));
    }

    #[test]
    fn test_error_stats() {
        let mut stats = ErrorStats::new();
        
        let validation_error = AppError::Validation("測試".to_string());
        stats.record_error(&validation_error);
        
        let auth_error = AppError::Authentication("測試".to_string());
        stats.record_error(&auth_error);
        
        assert_eq!(stats.total_errors, 2);
        assert_eq!(stats.errors_by_code.get("VALIDATION_ERROR"), Some(&1));
        assert_eq!(stats.errors_by_code.get("AUTHENTICATION_ERROR"), Some(&1));
        assert_eq!(stats.errors_by_severity.get("low"), Some(&1));
        assert_eq!(stats.errors_by_severity.get("medium"), Some(&1));
    }
}