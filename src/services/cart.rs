use std::sync::Arc;
use async_trait::async_trait;
use serde::{Serialize, Deserialize};

use crate::error::{AppError, AppResult};
use crate::models::{CartDetails, CartSummary, AddToCartRequest, UpdateCartItemRequest};
use crate::models::validation::Validate;
use crate::repositories::cart::CartRepository;
use crate::services::product::ProductService;

#[async_trait]
pub trait CartService: Send + Sync {
    async fn get_cart(&self, user_id: i64) -> AppResult<Option<CartDetails>>;
    async fn add_to_cart(&self, user_id: i64, request: AddToCartRequest) -> AppResult<CartDetails>;
    async fn update_cart_item(&self, user_id: i64, cart_item_id: i64, request: UpdateCartItemRequest) -> AppResult<CartDetails>;
    async fn remove_from_cart(&self, user_id: i64, cart_item_id: i64) -> AppResult<CartDetails>;
    async fn clear_cart(&self, user_id: i64) -> AppResult<bool>;
    async fn clear_cart_without_stock_restore(&self, user_id: i64) -> AppResult<bool>;
    async fn get_cart_summary(&self, user_id: i64) -> AppResult<CartSummary>;
    async fn validate_cart_stock(&self, user_id: i64) -> AppResult<CartValidationResult>;
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CartValidationResult {
    pub is_valid: bool,
    pub total_amount: rust_decimal::Decimal,
    pub total_items: i32,
    pub invalid_items: Vec<CartValidationItem>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CartValidationItem {
    pub cart_item_id: i64,
    pub product_id: i64,
    pub product_name: String,
    pub requested_quantity: i32,
    pub available_stock: i32,
    pub issue: String,
}

pub struct CartServiceImpl {
    cart_repository: Arc<dyn CartRepository>,
    product_service: Arc<dyn ProductService>,
}

impl CartServiceImpl {
    pub fn new(
        cart_repository: Arc<dyn CartRepository>,
        product_service: Arc<dyn ProductService>,
    ) -> Self {
        Self {
            cart_repository,
            product_service,
        }
    }
}

#[async_trait]
impl CartService for CartServiceImpl {
    async fn get_cart(&self, user_id: i64) -> AppResult<Option<CartDetails>> {
        self.cart_repository.get_cart_details(user_id).await
    }

    async fn add_to_cart(&self, user_id: i64, request: AddToCartRequest) -> AppResult<CartDetails> {
        // 驗證請求
        request.validate().map_err(|errors| {
            AppError::Validation(format!("Invalid add to cart request: {:?}", errors))
        })?;

        // 檢查產品是否存在並獲取價格
        let product = self.product_service.get_product_by_id(request.product_id).await?
            .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", request.product_id)))?;

        // 檢查產品是否啟用
        if !product.is_active {
            return Err(AppError::Validation("Product is not active".to_string()));
        }

        // 檢查庫存是否充足
        if !product.has_sufficient_stock(request.quantity) {
            return Err(AppError::Validation(format!(
                "Insufficient stock: requested {}, available {}",
                request.quantity, product.stock_quantity
            )));
        }

        // 獲取或創建購物車
        let cart = self.cart_repository.get_or_create_cart(user_id).await?;

        // 檢查是否已經有相同產品在購物車中
        let existing_item = self.cart_repository.find_cart_item(cart.id, request.product_id).await?;
        
        let total_quantity = if let Some(existing) = &existing_item {
            existing.quantity + request.quantity
        } else {
            request.quantity
        };

        // 再次檢查總數量是否超過庫存
        if !product.has_sufficient_stock(total_quantity) {
            return Err(AppError::Validation(format!(
                "Insufficient stock: total requested {}, available {}",
                total_quantity, product.stock_quantity
            )));
        }

        // 扣除庫存
        let new_stock = product.stock_quantity - request.quantity;
        self.product_service.update_stock(request.product_id, new_stock).await?;

        // 添加項目到購物車
        self.cart_repository.add_item(
            cart.id,
            request.product_id,
            request.quantity,
            product.selling_price,
        ).await?;

        // 返回更新後的購物車詳情
        self.cart_repository.get_cart_details(user_id).await?
            .ok_or_else(|| AppError::Internal("Failed to retrieve cart after adding item".to_string()))
    }

    async fn update_cart_item(&self, user_id: i64, cart_item_id: i64, request: UpdateCartItemRequest) -> AppResult<CartDetails> {
        // 驗證請求
        request.validate().map_err(|errors| {
            AppError::Validation(format!("Invalid update cart item request: {:?}", errors))
        })?;

        // 確保購物車存在且屬於該用戶
        let cart = self.cart_repository.find_cart_by_user_id(user_id).await?
            .ok_or_else(|| AppError::NotFound("Cart not found".to_string()))?;

        // 獲取購物車項目
        let cart_items = self.cart_repository.get_cart_items(cart.id).await?;
        let cart_item = cart_items.iter()
            .find(|item| item.id == cart_item_id)
            .ok_or_else(|| AppError::NotFound("Cart item not found".to_string()))?;

        // 檢查產品庫存
        let product = self.product_service.get_product_by_id(cart_item.product_id).await?
            .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", cart_item.product_id)))?;

        // 計算數量差異
        let quantity_diff = request.quantity - cart_item.quantity;
        
        if quantity_diff > 0 {
            // 增加數量，需要檢查庫存並扣除
            if !product.has_sufficient_stock(quantity_diff) {
                return Err(AppError::Validation(format!(
                    "Insufficient stock: additional requested {}, available {}",
                    quantity_diff, product.stock_quantity
                )));
            }
            // 扣除額外的庫存
            let new_stock = product.stock_quantity - quantity_diff;
            self.product_service.update_stock(cart_item.product_id, new_stock).await?;
        } else if quantity_diff < 0 {
            // 減少數量，需要恢復庫存
            let restore_quantity = -quantity_diff;
            let new_stock = product.stock_quantity + restore_quantity;
            self.product_service.update_stock(cart_item.product_id, new_stock).await?;
        }
        // quantity_diff == 0 時不需要調整庫存

        // 更新項目數量
        self.cart_repository.update_item_quantity(cart_item_id, request.quantity).await?;

        // 返回更新後的購物車詳情
        self.cart_repository.get_cart_details(user_id).await?
            .ok_or_else(|| AppError::Internal("Failed to retrieve cart after updating item".to_string()))
    }

    async fn remove_from_cart(&self, user_id: i64, cart_item_id: i64) -> AppResult<CartDetails> {
        // 確保購物車存在且屬於該用戶
        let cart = self.cart_repository.find_cart_by_user_id(user_id).await?
            .ok_or_else(|| AppError::NotFound("Cart not found".to_string()))?;

        // 獲取購物車項目以驗證所有權
        let cart_items = self.cart_repository.get_cart_items(cart.id).await?;
        let cart_item = cart_items.iter()
            .find(|item| item.id == cart_item_id)
            .ok_or_else(|| AppError::NotFound("Cart item not found".to_string()))?;

        // 恢復庫存
        let product = self.product_service.get_product_by_id(cart_item.product_id).await?
            .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", cart_item.product_id)))?;
        
        let new_stock = product.stock_quantity + cart_item.quantity;
        self.product_service.update_stock(cart_item.product_id, new_stock).await?;

        // 移除項目
        let removed = self.cart_repository.remove_item(cart_item_id).await?;
        if !removed {
            return Err(AppError::NotFound("Cart item not found".to_string()));
        }

        // 返回更新後的購物車詳情
        self.cart_repository.get_cart_details(user_id).await?
            .ok_or_else(|| AppError::Internal("Failed to retrieve cart after removing item".to_string()))
    }

    async fn clear_cart(&self, user_id: i64) -> AppResult<bool> {
        let cart = self.cart_repository.find_cart_by_user_id(user_id).await?
            .ok_or_else(|| AppError::NotFound("Cart not found".to_string()))?;

        // 獲取所有購物車項目以恢復庫存
        let cart_items = self.cart_repository.get_cart_items(cart.id).await?;
        
        // 恢復每個項目的庫存
        for cart_item in &cart_items {
            let product = self.product_service.get_product_by_id(cart_item.product_id).await?
                .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", cart_item.product_id)))?;
            
            let new_stock = product.stock_quantity + cart_item.quantity;
            self.product_service.update_stock(cart_item.product_id, new_stock).await?;
        }

        self.cart_repository.clear_cart(cart.id).await
    }

    async fn clear_cart_without_stock_restore(&self, user_id: i64) -> AppResult<bool> {
        let cart = self.cart_repository.find_cart_by_user_id(user_id).await?
            .ok_or_else(|| AppError::NotFound("Cart not found".to_string()))?;

        // 直接清空購物車，不恢復庫存（用於訂單創建後）
        self.cart_repository.clear_cart(cart.id).await
    }

    async fn get_cart_summary(&self, user_id: i64) -> AppResult<CartSummary> {
        match self.cart_repository.get_cart_details(user_id).await? {
            Some(cart_details) => Ok(cart_details.get_summary()),
            None => Ok(CartSummary {
                total_items: 0,
                total_amount: rust_decimal::Decimal::ZERO,
                items_count: 0,
            }),
        }
    }

    async fn validate_cart_stock(&self, user_id: i64) -> AppResult<CartValidationResult> {
        let cart_details = match self.cart_repository.get_cart_details(user_id).await? {
            Some(details) => details,
            None => {
                return Ok(CartValidationResult {
                    is_valid: true,
                    total_amount: rust_decimal::Decimal::ZERO,
                    total_items: 0,
                    invalid_items: vec![],
                });
            }
        };

        let mut invalid_items = Vec::new();

        for item_detail in &cart_details.items {
            let product = self.product_service.get_product_by_id(item_detail.item.product_id).await?
                .ok_or_else(|| AppError::NotFound(format!("Product with id {} not found", item_detail.item.product_id)))?;

            // 檢查產品是否啟用
            if !product.is_active {
                invalid_items.push(CartValidationItem {
                    cart_item_id: item_detail.item.id,
                    product_id: item_detail.item.product_id,
                    product_name: item_detail.product_name.clone(),
                    requested_quantity: item_detail.item.quantity,
                    available_stock: product.stock_quantity,
                    issue: "Product is no longer active".to_string(),
                });
                continue;
            }

            // 檢查庫存是否充足
            if !product.has_sufficient_stock(item_detail.item.quantity) {
                invalid_items.push(CartValidationItem {
                    cart_item_id: item_detail.item.id,
                    product_id: item_detail.item.product_id,
                    product_name: item_detail.product_name.clone(),
                    requested_quantity: item_detail.item.quantity,
                    available_stock: product.stock_quantity,
                    issue: format!(
                        "Insufficient stock: requested {}, available {}",
                        item_detail.item.quantity, product.stock_quantity
                    ),
                });
            }

            // 檢查價格是否有變化（可選的警告）
            if (item_detail.item.unit_price - product.selling_price).abs() > rust_decimal::Decimal::from_f64_retain(0.01).unwrap() {
                invalid_items.push(CartValidationItem {
                    cart_item_id: item_detail.item.id,
                    product_id: item_detail.item.product_id,
                    product_name: item_detail.product_name.clone(),
                    requested_quantity: item_detail.item.quantity,
                    available_stock: product.stock_quantity,
                    issue: format!(
                        "Price has changed: cart price {}, current price {}",
                        item_detail.item.unit_price, product.selling_price
                    ),
                });
            }
        }

        Ok(CartValidationResult {
            is_valid: invalid_items.is_empty(),
            total_amount: cart_details.total_amount,
            total_items: cart_details.total_items,
            invalid_items,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::{Product, CartItemDetails};
    use crate::repositories::cart::CartRepository;
    use crate::services::product::ProductService;
    use async_trait::async_trait;
    use mockall::mock;
    use rust_decimal_macros::dec;
    use chrono::Utc;

    mock! {
        CartRepo {}

        #[async_trait]
        impl CartRepository for CartRepo {
            async fn create_cart(&self, user_id: i64) -> Result<Cart, AppError>;
            async fn find_cart_by_user_id(&self, user_id: i64) -> Result<Option<Cart>, AppError>;
            async fn get_or_create_cart(&self, user_id: i64) -> Result<Cart, AppError>;
            async fn add_item(&self, cart_id: i64, product_id: i64, quantity: i32, unit_price: rust_decimal::Decimal) -> Result<CartItem, AppError>;
            async fn update_item_quantity(&self, cart_item_id: i64, quantity: i32) -> Result<CartItem, AppError>;
            async fn remove_item(&self, cart_item_id: i64) -> Result<bool, AppError>;
            async fn find_cart_item(&self, cart_id: i64, product_id: i64) -> Result<Option<CartItem>, AppError>;
            async fn get_cart_details(&self, user_id: i64) -> Result<Option<CartDetails>, AppError>;
            async fn clear_cart(&self, cart_id: i64) -> Result<bool, AppError>;
            async fn get_cart_items(&self, cart_id: i64) -> Result<Vec<CartItem>, AppError>;
            async fn update_cart_timestamp(&self, cart_id: i64) -> Result<(), AppError>;
        }
    }

    mock! {
        ProductService {}

        #[async_trait]
        impl ProductService for ProductService {
            async fn import_from_excel(&self, file_data: Vec<u8>) -> AppResult<crate::models::product::ImportResult>;
            async fn import_from_csv(&self, file_data: Vec<u8>) -> AppResult<crate::models::product::ImportResult>;
            async fn get_products(&self, filter: Option<crate::models::product::ProductFilter>, page: Option<u32>, limit: Option<u32>) -> AppResult<Vec<Product>>;
            async fn get_product_by_id(&self, id: i64) -> AppResult<Option<Product>>;
            async fn get_product_by_nhi_code(&self, nhi_code: &str) -> AppResult<Option<Product>>;
            async fn update_stock(&self, product_id: i64, quantity: i32) -> AppResult<Product>;
            async fn get_low_stock_products(&self) -> AppResult<Vec<Product>>;
            async fn check_stock_availability(&self, product_id: i64, required_quantity: i32) -> AppResult<bool>;
            async fn get_stock_status(&self, product_id: i64) -> AppResult<crate::services::product::StockStatus>;
            async fn bulk_update_stock(&self, updates: Vec<crate::services::product::StockUpdate>) -> AppResult<Vec<Product>>;
        }
    }

    fn create_test_cart() -> Cart {
        Cart {
            id: 1,
            user_id: 1,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    fn create_test_product() -> Product {
        Product {
            id: 1,
            nhi_code: "A001".to_string(),
            name: "阿司匹林".to_string(),
            manufacturer: "台廠".to_string(),
            unit: "盒".to_string(),
            unit_price: dec!(50.0),
            stock_quantity: 100,
            description: Some("止痛藥".to_string()),
            is_active: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    fn create_test_cart_item() -> CartItem {
        CartItem {
            id: 1,
            cart_id: 1,
            product_id: 1,
            quantity: 2,
            unit_price: dec!(50.0),
            subtotal: dec!(100.0),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    fn create_test_cart_details() -> CartDetails {
        let cart = create_test_cart();
        let cart_item = create_test_cart_item();
        
        CartDetails {
            cart,
            items: vec![CartItemDetails {
                item: cart_item,
                product_name: "阿司匹林".to_string(),
                product_nhi_code: "A001".to_string(),
                available_stock: 100,
            }],
            total_amount: dec!(100.0),
            total_items: 2,
        }
    }

    #[tokio::test]
    async fn test_get_cart_success() {
        let mut mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_cart_details())));

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.get_cart(1).await;
        assert!(result.is_ok());
        
        let cart_details = result.unwrap();
        assert!(cart_details.is_some());
        assert_eq!(cart_details.unwrap().total_amount, dec!(100.0));
    }

    #[tokio::test]
    async fn test_add_to_cart_success() {
        let mut mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let request = AddToCartRequest {
            product_id: 1,
            quantity: 2,
        };

        // Mock product service
        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_product())));

        // Mock cart repository
        mock_cart_repo
            .expect_get_or_create_cart()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(create_test_cart()));

        mock_cart_repo
            .expect_add_item()
            .times(1)
            .returning(|_, _, _, _| Ok(create_test_cart_item()));

        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_cart_details())));

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.add_to_cart(1, request).await;
        assert!(result.is_ok());
        
        let cart_details = result.unwrap();
        assert_eq!(cart_details.total_amount, dec!(100.0));
    }

    #[tokio::test]
    async fn test_add_to_cart_insufficient_stock() {
        let mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let request = AddToCartRequest {
            product_id: 1,
            quantity: 200, // 超過庫存
        };

        // Mock product with insufficient stock
        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_product()))); // 只有 100 庫存

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.add_to_cart(1, request).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Insufficient stock"));
    }

    #[tokio::test]
    async fn test_add_to_cart_inactive_product() {
        let mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let request = AddToCartRequest {
            product_id: 1,
            quantity: 2,
        };

        // Mock inactive product
        let mut inactive_product = create_test_product();
        inactive_product.is_active = false;

        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(inactive_product.clone())));

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.add_to_cart(1, request).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Product is not active"));
    }

    #[tokio::test]
    async fn test_update_cart_item_success() {
        let mut mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        let request = UpdateCartItemRequest { quantity: 3 };

        // Mock cart repository
        mock_cart_repo
            .expect_find_cart_by_user_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_cart())));

        mock_cart_repo
            .expect_get_cart_items()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(vec![create_test_cart_item()]));

        mock_cart_repo
            .expect_update_item_quantity()
            .times(1)
            .with(mockall::predicate::eq(1), mockall::predicate::eq(3))
            .returning(|_, _| Ok(create_test_cart_item()));

        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_cart_details())));

        // Mock product service
        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_product())));

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.update_cart_item(1, 1, request).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_remove_from_cart_success() {
        let mut mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        // Mock cart repository
        mock_cart_repo
            .expect_find_cart_by_user_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_cart())));

        mock_cart_repo
            .expect_get_cart_items()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(vec![create_test_cart_item()]));

        mock_cart_repo
            .expect_remove_item()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(true));

        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_cart_details())));

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.remove_from_cart(1, 1).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_clear_cart_success() {
        let mut mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        mock_cart_repo
            .expect_find_cart_by_user_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_cart())));

        mock_cart_repo
            .expect_clear_cart()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(true));

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.clear_cart(1).await;
        assert!(result.is_ok());
        assert!(result.unwrap());
    }

    #[tokio::test]
    async fn test_get_cart_summary() {
        let mut mock_cart_repo = MockCartRepo::new();
        let mock_product_service = MockProductService::new();

        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_cart_details())));

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.get_cart_summary(1).await;
        assert!(result.is_ok());
        
        let summary = result.unwrap();
        assert_eq!(summary.total_items, 2);
        assert_eq!(summary.total_amount, dec!(100.0));
        assert_eq!(summary.items_count, 1);
    }

    #[tokio::test]
    async fn test_validate_cart_stock_valid() {
        let mut mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_cart_details())));

        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_product())));

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.validate_cart_stock(1).await;
        assert!(result.is_ok());
        
        let validation = result.unwrap();
        assert!(validation.is_valid);
        assert_eq!(validation.invalid_items.len(), 0);
    }

    #[tokio::test]
    async fn test_validate_cart_stock_insufficient() {
        let mut mock_cart_repo = MockCartRepo::new();
        let mut mock_product_service = MockProductService::new();

        // Create cart details with high quantity
        let mut cart_details = create_test_cart_details();
        cart_details.items[0].item.quantity = 200; // 超過庫存

        mock_cart_repo
            .expect_get_cart_details()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(move |_| Ok(Some(cart_details.clone())));

        mock_product_service
            .expect_get_product_by_id()
            .times(1)
            .with(mockall::predicate::eq(1))
            .returning(|_| Ok(Some(create_test_product()))); // 只有 100 庫存

        let service = CartServiceImpl::new(
            Arc::new(mock_cart_repo),
            Arc::new(mock_product_service),
        );

        let result = service.validate_cart_stock(1).await;
        assert!(result.is_ok());
        
        let validation = result.unwrap();
        assert!(!validation.is_valid);
        assert_eq!(validation.invalid_items.len(), 1);
        assert!(validation.invalid_items[0].issue.contains("Insufficient stock"));
    }
}