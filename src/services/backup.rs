use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::time::Instant;
use tracing::{info, warn, error, debug};

use crate::error::{AppResult, AppError};
use crate::database::connection::DbPool;
use crate::logging::structured_logging;
use std::path::Path;
use tokio::fs;
use uuid::Uuid;
use google_cloud_storage::client::{Client as GcsClient, ClientConfig};
use google_cloud_auth::credentials::CredentialsFile;
use tokio_cron_scheduler::{JobScheduler, Job};
use std::sync::Arc;
use sqlx::Row;

#[derive(Debug, Serialize, Deserialize)]
pub struct BackupInfo {
    pub id: String,
    pub file_path: String,
    pub file_size: u64,
    pub created_at: DateTime<Utc>,
    pub cloud_url: Option<String>,
}

pub struct BackupServiceImpl {
    db_pool: DbPool,
    gcs_client: Option<GcsClient>,
    backup_dir: String,
    bucket_name: String,
    scheduler: Arc<JobScheduler>,
}

impl BackupServiceImpl {
    pub async fn new(
        db_pool: DbPool,
        bucket_name: String,
        credentials_path: Option<String>,
    ) -> AppResult<Self> {
        let backup_dir = std::env::var("BACKUP_DIR").unwrap_or_else(|_| "./backups".to_string());
        
        // 初始化 GCS 客戶端
        let gcs_client = if let Some(creds_path) = credentials_path {
            match Self::create_gcs_client(&creds_path).await {
                Ok(client) => {
                    info!("GCS client initialized successfully");
                    Some(client)
                }
                Err(e) => {
                    warn!("Failed to create GCS client: {}. Backups will be local only.", e);
                    None
                }
            }
        } else {
            warn!("No GCP credentials provided. Backups will be local only.");
            None
        };

        // 初始化排程器
        let scheduler = Arc::new(JobScheduler::new().await.map_err(|e| {
            AppError::Configuration(format!("Failed to create scheduler: {}", e))
        })?);

        Ok(Self {
            db_pool,
            gcs_client,
            backup_dir,
            bucket_name,
            scheduler,
        })
    }

    async fn create_gcs_client(credentials_path: &str) -> AppResult<GcsClient> {
        let credentials = CredentialsFile::new_from_file(credentials_path.to_string())
            .await
            .map_err(|e| AppError::Configuration(format!("Failed to load GCP credentials: {}", e)))?;

        let config = ClientConfig::default().with_credentials(credentials).await
            .map_err(|e| AppError::Configuration(format!("Failed to configure GCS client: {}", e)))?;

        Ok(GcsClient::new(config))
    }

    async fn ensure_backup_dir(&self) -> AppResult<()> {
        if !Path::new(&self.backup_dir).exists() {
            fs::create_dir_all(&self.backup_dir).await
                .map_err(|e| AppError::FileProcessing(
                    format!("Failed to create backup directory: {}", e)
                ))?;
        }
        Ok(())
    }

    async fn create_backup_file(&self) -> AppResult<String> {
        self.ensure_backup_dir().await?;
        
        let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
        let backup_id = Uuid::new_v4().simple().to_string();
        let filename = format!("pharmacy_backup_{}_{}.sql", timestamp, backup_id);
        let _file_path = format!("{}/{}", self.backup_dir, filename);
        
        // 對於 PostgreSQL，我們使用 pg_dump 命令進行備份
        // 這裡暫時返回錯誤，建議使用外部 pg_dump 工具
        Err(AppError::Configuration(
            "PostgreSQL backup requires pg_dump. Please use external backup tools.".to_string()
        ))
    }

    async fn extract_db_path(&self) -> AppResult<String> {
        // 從連線池中提取資料庫路徑
        // 這是一個簡化的實作，實際上可能需要從配置中獲取
        Ok("pharmacy.db".to_string())
    }

    async fn upload_to_gcs(&self, file_path: &str) -> AppResult<String> {
        let gcs_client = self.gcs_client.as_ref()
            .ok_or_else(|| AppError::CloudStorage("GCS client not available".to_string()))?;

        let file_name = Path::new(file_path)
            .file_name()
            .and_then(|name| name.to_str())
            .ok_or_else(|| AppError::FileProcessing("Invalid file path".to_string()))?
            .to_string();
        
        let file_content = fs::read(file_path).await
            .map_err(|e| AppError::FileProcessing(
                format!("Failed to read backup file: {}", e)
            ))?;
        
        // 使用正確的 GCS API
        let media = google_cloud_storage::http::objects::upload::Media {
            name: file_name.clone().into(),
            content_type: "application/octet-stream".into(),
            content_length: Some(file_content.len() as u64),
        };

        gcs_client
            .upload_object(
                &google_cloud_storage::http::objects::upload::UploadObjectRequest {
                    bucket: self.bucket_name.clone(),
                    ..Default::default()
                },
                file_content,
                &google_cloud_storage::http::objects::upload::UploadType::Simple(media),
            )
            .await
            .map_err(|e| AppError::CloudStorage(format!("Failed to upload to GCS: {}", e)))?;
        
        let cloud_url = format!(
            "gs://{}/{}",
            self.bucket_name,
            file_name
        );
        
        info!("Backup uploaded to GCS: {}", cloud_url);
        Ok(cloud_url)
    }

    async fn log_backup(&self, backup_info: &BackupInfo) -> AppResult<()> {
        let file_size = backup_info.file_size as i64;
        let status = "completed";
        let uploaded_to_gcp = backup_info.cloud_url.is_some();
        
        sqlx::query(
            r#"
            INSERT INTO backup_logs (backup_file, file_size, status, uploaded_to_gcp, created_at)
            VALUES ($1, $2, $3, $4, $5)
            "#
        )
        .bind(&backup_info.file_path)
        .bind(file_size)
        .bind(status)
        .bind(uploaded_to_gcp)
        .bind(backup_info.created_at)
        .execute(self.db_pool.as_ref())
        .await
        .map_err(|e| AppError::Database(e))?;
        
        Ok(())
    }

    async fn compress_backup(&self, file_path: &str) -> AppResult<String> {
        use std::process::Command;
        
        let compressed_path = format!("{}.gz", file_path);
        
        let output = Command::new("gzip")
            .arg("-c")
            .arg(file_path)
            .output()
            .map_err(|e| AppError::FileProcessing(format!("Failed to compress backup: {}", e)))?;
        
        if !output.status.success() {
            return Err(AppError::FileProcessing(
                format!("Compression failed: {}", String::from_utf8_lossy(&output.stderr))
            ));
        }
        
        fs::write(&compressed_path, output.stdout).await
            .map_err(|e| AppError::FileProcessing(format!("Failed to write compressed file: {}", e)))?;
        
        // 刪除原始檔案
        fs::remove_file(file_path).await
            .map_err(|e| AppError::FileProcessing(format!("Failed to remove original file: {}", e)))?;
        
        info!("Backup compressed: {}", compressed_path);
        Ok(compressed_path)
    }
}

#[async_trait]
impl BackupService for BackupServiceImpl {
    async fn create_backup(&self) -> AppResult<BackupInfo> {
        let start_time = Instant::now();
        
        info!("開始資料庫備份作業");
        
        let file_path = self.create_backup_file().await?;
        
        // 壓縮備份檔案
        let compressed_path = self.compress_backup(&file_path).await?;
        
        let metadata = fs::metadata(&compressed_path).await
            .map_err(|e| AppError::FileProcessing(
                format!("Failed to get file metadata: {}", e)
            ))?;
        
        let backup_id = Uuid::new_v4().simple().to_string();
        let created_at = Utc::now();
        let file_size = metadata.len();
        
        debug!(
            backup_id = %backup_id,
            file_path = %compressed_path,
            file_size = file_size,
            "備份檔案建立完成"
        );
        
        let mut backup_info = BackupInfo {
            id: backup_id.clone(),
            file_path: compressed_path.clone(),
            file_size,
            created_at,
            cloud_url: None,
        };
        
        // 上傳到雲端（如果可用）
        if self.gcs_client.is_some() {
            match self.upload_to_gcs(&compressed_path).await {
                Ok(cloud_url) => {
                    backup_info.cloud_url = Some(cloud_url.clone());
                    info!(
                        backup_id = %backup_id,
                        cloud_url = %cloud_url,
                        "備份已上傳至雲端"
                    );
                }
                Err(e) => {
                    warn!(
                        backup_id = %backup_id,
                        error = %e,
                        "備份上傳雲端失敗"
                    );
                }
            }
        } else {
            info!(
                backup_id = %backup_id,
                "GCS 客戶端不可用，備份僅儲存在本地"
            );
        }
        
        // 記錄備份
        if let Err(e) = self.log_backup(&backup_info).await {
            warn!("Failed to log backup: {}", e);
        }
        
        let duration = start_time.elapsed();
        
        // 記錄備份操作
        structured_logging::log_backup_operation(
            "create_backup",
            "database",
            Some(file_size),
            duration.as_millis() as u64,
            true,
            backup_info.cloud_url.as_deref(),
            None,
        );
        
        info!(
            backup_id = %backup_id,
            file_path = %compressed_path,
            file_size = file_size,
            duration_ms = duration.as_millis(),
            cloud_uploaded = backup_info.cloud_url.is_some(),
            "備份建立成功完成"
        );
        
        Ok(backup_info)
    }

    async fn upload_to_cloud(&self, backup_path: &str) -> AppResult<String> {
        self.upload_to_gcs(backup_path).await
    }

    async fn restore_from_backup(&self, backup_url: &str) -> AppResult<()> {
        info!("Starting database restore from: {}", backup_url);
        
        let backup_data = if backup_url.starts_with("gs://") {
            // 從 GCS 下載
            let gcs_client = self.gcs_client.as_ref()
                .ok_or_else(|| AppError::CloudStorage("GCS client not available".to_string()))?;
            
            // 解析 GCS URL
            let url_parts: Vec<&str> = backup_url.trim_start_matches("gs://").split('/').collect();
            if url_parts.len() < 2 {
                return Err(AppError::CloudStorage("Invalid GCS URL format".to_string()));
            }
            
            let bucket = url_parts[0];
            let object_name = url_parts[1..].join("/");
            
            gcs_client
                .download_object(
                    &google_cloud_storage::http::objects::get::GetObjectRequest {
                        bucket: bucket.to_string(),
                        object: object_name,
                        ..Default::default()
                    },
                    &google_cloud_storage::http::objects::download::Range::default(),
                )
                .await
                .map_err(|e| AppError::CloudStorage(format!("Failed to download from GCS: {}", e)))?
        } else {
            // 從本地檔案讀取
            fs::read(backup_url).await
                .map_err(|e| AppError::FileProcessing(
                    format!("Failed to read backup file: {}", e)
                ))?
        };
        
        // 解壓縮備份檔案（如果是 .gz 格式）
        let decompressed_data = if backup_url.ends_with(".gz") {
            use std::process::Command;
            
            let temp_file = format!("/tmp/restore_backup_{}.gz", Uuid::new_v4().simple());
            fs::write(&temp_file, &backup_data).await
                .map_err(|e| AppError::FileProcessing(format!("Failed to write temp file: {}", e)))?;
            
            let output = Command::new("gunzip")
                .arg("-c")
                .arg(&temp_file)
                .output()
                .map_err(|e| AppError::FileProcessing(format!("Failed to decompress backup: {}", e)))?;
            
            // 清理臨時檔案
            let _ = fs::remove_file(&temp_file).await;
            
            if !output.status.success() {
                return Err(AppError::FileProcessing(
                    format!("Decompression failed: {}", String::from_utf8_lossy(&output.stderr))
                ));
            }
            
            output.stdout
        } else {
            backup_data
        };
        
        // 備份當前資料庫
        let current_db_backup = format!("pharmacy_backup_before_restore_{}.db", 
            Utc::now().format("%Y%m%d_%H%M%S"));
        
        if Path::new("pharmacy.db").exists() {
            fs::copy("pharmacy.db", &current_db_backup).await
                .map_err(|e| AppError::FileProcessing(
                    format!("Failed to backup current database: {}", e)
                ))?;
            info!("Current database backed up to: {}", current_db_backup);
        }
        
        // 還原資料庫
        fs::write("pharmacy.db", decompressed_data).await
            .map_err(|e| AppError::FileProcessing(
                format!("Failed to restore database: {}", e)
            ))?;
        
        info!("Database restored from backup: {}", backup_url);
        Ok(())
    }

    async fn cleanup_old_backups(&self) -> AppResult<()> {
        info!("Starting cleanup of old backups...");
        
        let retention_days = std::env::var("BACKUP_RETENTION_DAYS")
            .unwrap_or_else(|_| "30".to_string())
            .parse::<i64>()
            .unwrap_or(30);
        
        let cutoff_date = Utc::now() - chrono::Duration::days(retention_days);
        
        // 清理本地備份檔案
        let mut local_cleaned = 0;
        if Path::new(&self.backup_dir).exists() {
            let mut entries = fs::read_dir(&self.backup_dir).await
                .map_err(|e| AppError::FileProcessing(
                    format!("Failed to read backup directory: {}", e)
                ))?;
            
            while let Some(entry) = entries.next_entry().await
                .map_err(|e| AppError::FileProcessing(
                    format!("Failed to read directory entry: {}", e)
                ))? {
                
                let metadata = entry.metadata().await
                    .map_err(|e| AppError::FileProcessing(
                        format!("Failed to get file metadata: {}", e)
                    ))?;
                
                let created: DateTime<Utc> = DateTime::from(metadata.created()
                    .map_err(|e| AppError::FileProcessing(
                        format!("Failed to get file creation time: {}", e)
                    ))?);
                
                if created < cutoff_date {
                    fs::remove_file(entry.path()).await
                        .map_err(|e| AppError::FileProcessing(
                            format!("Failed to delete old backup: {}", e)
                        ))?;
                    
                    local_cleaned += 1;
                    info!("Deleted old local backup: {:?}", entry.path());
                }
            }
        }
        
        // 清理資料庫記錄中的舊備份
        let db_cleaned = sqlx::query("DELETE FROM backup_logs WHERE created_at < $1")
            .bind(cutoff_date)
            .execute(self.db_pool.as_ref())
            .await
            .map_err(|e| AppError::Database(e))?
            .rows_affected();
        
        info!("Cleanup completed: {} local files, {} database records", local_cleaned, db_cleaned);
        Ok(())
    }

    async fn list_backups(&self) -> AppResult<Vec<BackupInfo>> {
        let rows = sqlx::query(
            r#"
            SELECT 
                id,
                backup_file,
                file_size,
                created_at,
                uploaded_to_gcp
            FROM backup_logs 
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(self.db_pool.as_ref())
        .await
        .map_err(|e| AppError::Database(e))?;
        
        let backups = rows.into_iter().map(|row| {
            let id: i64 = row.get("id");
            let backup_file: String = row.get("backup_file");
            let file_size: i64 = row.get("file_size");
            let created_at: Option<chrono::NaiveDateTime> = row.get("created_at");
            let uploaded_to_gcp: bool = row.get("uploaded_to_gcp");
            
            let cloud_url = if uploaded_to_gcp { 
                Some(format!("gs://{}/{}", self.bucket_name, 
                    std::path::Path::new(&backup_file)
                        .file_name()
                        .unwrap()
                        .to_str()
                        .unwrap()))
            } else { 
                None 
            };
            
            BackupInfo {
                id: id.to_string(),
                file_path: backup_file,
                file_size: file_size as u64,
                created_at: created_at.map(|dt| dt.and_utc()).unwrap_or_else(|| Utc::now()),
                cloud_url,
            }
        }).collect();
        
        Ok(backups)
    }

    async fn schedule_daily_backup(&self) -> AppResult<()> {
        info!("Setting up daily backup schedule...");
        
        let scheduler = self.scheduler.clone();
        let service = Arc::new(self.clone());
        
        // 每天凌晨 2 點執行備份
        let job = Job::new_async("0 0 2 * * *", move |_uuid, _l| {
            let service = service.clone();
            Box::pin(async move {
                match service.create_backup().await {
                    Ok(backup_info) => {
                        info!("Scheduled backup completed: {}", backup_info.file_path);
                    }
                    Err(e) => {
                        error!("Scheduled backup failed: {}", e);
                    }
                }
            })
        })
        .map_err(|e| AppError::Configuration(format!("Failed to create backup job: {}", e)))?;
        
        scheduler.add(job).await
            .map_err(|e| AppError::Configuration(format!("Failed to add backup job: {}", e)))?;
        
        scheduler.start().await
            .map_err(|e| AppError::Configuration(format!("Failed to start scheduler: {}", e)))?;
        
        info!("Daily backup scheduled for 2:00 AM");
        Ok(())
    }
}

// 為了支援 Arc<BackupServiceImpl>，需要實作 Clone
impl Clone for BackupServiceImpl {
    fn clone(&self) -> Self {
        Self {
            db_pool: self.db_pool.clone(),
            gcs_client: self.gcs_client.clone(),
            backup_dir: self.backup_dir.clone(),
            bucket_name: self.bucket_name.clone(),
            scheduler: self.scheduler.clone(),
        }
    }
}

#[async_trait]
pub trait BackupService: Send + Sync {
    async fn create_backup(&self) -> AppResult<BackupInfo>;
    #[allow(dead_code)]
    async fn upload_to_cloud(&self, backup_path: &str) -> AppResult<String>;
    #[allow(dead_code)]
    async fn restore_from_backup(&self, backup_url: &str) -> AppResult<()>;
    async fn cleanup_old_backups(&self) -> AppResult<()>;
    async fn list_backups(&self) -> AppResult<Vec<BackupInfo>>;
    async fn schedule_daily_backup(&self) -> AppResult<()>;
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use crate::database::connection::{ConnectionManager, ConnectionConfig};

    async fn create_test_service() -> (BackupServiceImpl, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        let config = ConnectionConfig {
            database_url: "postgresql://postgres:password@localhost:5432/test_backup_db".to_string(),
            ..Default::default()
        };
        
        let connection_manager = ConnectionManager::new(config).await.unwrap();
        let db_pool = connection_manager.pool().clone();
        
        // 建立測試表格
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS backup_logs (
                id SERIAL PRIMARY KEY,
                backup_file VARCHAR(255) NOT NULL,
                file_size BIGINT NOT NULL,
                status VARCHAR(20) NOT NULL,
                uploaded_to_gcp BOOLEAN NOT NULL DEFAULT false,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            "#
        )
        .execute(&*db_pool)
        .await
        .unwrap();
        
        let service = BackupServiceImpl::new(
            db_pool,
            "test-bucket".to_string(),
            None,
        ).await.unwrap();
        
        (service, temp_dir)
    }

    #[tokio::test]
    async fn test_backup_service_creation() {
        let (_service, _temp_dir) = create_test_service().await;
        // 如果能建立服務就表示測試通過
    }

    #[tokio::test]
    async fn test_ensure_backup_dir() {
        let (service, _temp_dir) = create_test_service().await;
        let result = service.ensure_backup_dir().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_list_backups() {
        let (service, _temp_dir) = create_test_service().await;
        let result = service.list_backups().await;
        assert!(result.is_ok());
        assert!(result.unwrap().is_empty());
    }

    #[tokio::test]
    async fn test_cleanup_old_backups() {
        let (service, _temp_dir) = create_test_service().await;
        
        // 測試清理功能不會出錯
        let result = service.cleanup_old_backups().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_restore_from_local_backup() {
        let (service, temp_dir) = create_test_service().await;
        
        // 建立一個測試備份檔案
        let backup_content = b"test backup content";
        let backup_path = temp_dir.path().join("test_backup.db");
        std::fs::write(&backup_path, backup_content).unwrap();
        
        // 測試從本地檔案恢復
        let result = service.restore_from_backup(backup_path.to_str().unwrap()).await;
        assert!(result.is_ok());
        
        // 驗證資料庫檔案已被恢復
        let restored_content = std::fs::read("pharmacy.db").unwrap();
        assert_eq!(restored_content, backup_content);
    }

    #[tokio::test]
    async fn test_schedule_daily_backup() {
        let (service, _temp_dir) = create_test_service().await;
        
        // 測試排程功能不會出錯
        let result = service.schedule_daily_backup().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_compress_backup() {
        let (service, temp_dir) = create_test_service().await;
        
        // 建立一個測試檔案
        let test_content = b"test file content for compression";
        let test_file = temp_dir.path().join("test_file.db");
        std::fs::write(&test_file, test_content).unwrap();
        
        // 測試壓縮功能
        let result = service.compress_backup(test_file.to_str().unwrap()).await;
        
        // 如果系統有 gzip 命令，測試應該成功
        // 如果沒有，會返回錯誤，這也是預期的行為
        match result {
            Ok(compressed_path) => {
                assert!(compressed_path.ends_with(".gz"));
                assert!(std::path::Path::new(&compressed_path).exists());
            }
            Err(e) => {
                // 如果系統沒有 gzip 命令，這是預期的錯誤
                assert!(e.to_string().contains("Failed to compress backup"));
            }
        }
    }
}