use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON><PERSON>, <PERSON>ial<PERSON>, Deserialize)]
pub struct Config {
    pub database_url: String,
    pub jwt_secret: String,
    pub server_port: u16,
    pub email: EmailConfig,
    pub line: LineConfig,
    pub gcp: GcpConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EmailConfig {
    pub smtp_host: String,
    pub smtp_port: u16,
    pub smtp_username: String,
    pub smtp_password: String,
    pub from_email: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LineConfig {
    pub channel_access_token: String,
    pub channel_secret: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GcpConfig {
    pub project_id: String,
    pub storage_bucket: String,
    pub credentials_path: Option<String>,
}

impl Config {
    pub fn from_env() -> Result<Self, env::VarError> {
        Ok(Config {
            database_url: env::var("DATABASE_URL").unwrap_or_else(|_| "postgresql://localhost:5432/pharmacy".to_string()),
            jwt_secret: env::var("JWT_SECRET")?,
            server_port: env::var("PORT")
                .unwrap_or_else(|_| "8080".to_string())
                .parse()
                .unwrap_or(8080),
            email: EmailConfig {
                smtp_host: env::var("SMTP_HOST").unwrap_or_else(|_| "smtp.gmail.com".to_string()),
                smtp_port: env::var("SMTP_PORT").unwrap_or_else(|_| "587".to_string()).parse().unwrap_or(587),
                smtp_username: env::var("SMTP_USERNAME").unwrap_or_else(|_| "<EMAIL>".to_string()),
                smtp_password: env::var("SMTP_PASSWORD").unwrap_or_else(|_| "your-app-password".to_string()),
                from_email: env::var("FROM_EMAIL").unwrap_or_else(|_| "<EMAIL>".to_string()),
            },
            line: LineConfig {
                channel_access_token: env::var("LINE_CHANNEL_ACCESS_TOKEN").unwrap_or_else(|_| "your-line-channel-access-token".to_string()),
                channel_secret: env::var("LINE_CHANNEL_SECRET").unwrap_or_else(|_| "your-line-channel-secret".to_string()),
            },
            gcp: GcpConfig {
                project_id: env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "your-gcp-project-id".to_string()),
                storage_bucket: env::var("GCP_STORAGE_BUCKET").unwrap_or_else(|_| "your-backup-bucket-name".to_string()),
                credentials_path: env::var("GOOGLE_APPLICATION_CREDENTIALS").ok(),
            },
        })
    }
}