use sqlx::PgPool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 載入環境變數
    dotenvy::dotenv().ok();

    // 取得資料庫 URL
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");

    // 建立資料庫連線（如果資料庫不存在會自動建立）
    let pool = PgPool::connect(&database_url).await?;

    // 執行遷移
    // sqlx::migrate!("./migrations")
    //     .run(&pool)
    //     .await?;

    println!("Database migrations completed successfully!");

    Ok(())
} 