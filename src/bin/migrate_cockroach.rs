use std::env;
use sqlx::postgres::PgPoolOptions;
use sqlx::{Pool, Postgres};
use dotenvy::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require".to_string());
    
    println!("正在連接到 CockroachDB 資料庫...");
    
    let pool: Pool<Postgres> = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 成功連接到資料庫");
    
    // 手動執行遷移，跳過 pg_advisory_lock
    println!("開始執行資料庫遷移...");
    
    // 執行初始架構
    println!("執行初始架構遷移...");
    let initial_schema = include_str!("../../migrations/20240101000001_initial_schema.sql");
    execute_sql_statements(&pool, initial_schema).await?;
    println!("✅ 初始架構遷移完成");
    
    // 執行購物車表格
    println!("執行購物車表格遷移...");
    let cart_schema = include_str!("../../migrations/20240102000001_add_cart_tables.sql");
    execute_sql_statements(&pool, cart_schema).await?;
    println!("✅ 購物車表格遷移完成");
    
    // 執行角色系統（跳過 pg_advisory_lock）
    println!("執行角色系統遷移...");
    let role_schema = include_str!("../../migrations/20240103000001_add_user_roles.sql");
    execute_sql_statements(&pool, role_schema).await?;
    println!("✅ 角色系統遷移完成");
    
    // 執行性能索引（已合併到初始遷移中）
    println!("性能索引已包含在初始遷移中，跳過...");
    
    // 執行產品欄位（已合併到初始遷移中）
    println!("產品欄位已包含在初始遷移中，跳過...");
    
    // 插入範例產品
    println!("插入範例產品...");
    let sample_products = include_str!("../../migrations/20240104000002_insert_sample_products.sql");
    execute_sql_statements(&pool, sample_products).await?;
    println!("✅ 範例產品插入完成");
    
    println!("✅ 所有資料庫遷移完成！");
    
    // 檢查表格是否建立成功
    let tables: Vec<String> = sqlx::query_scalar(
        "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("資料庫中的表格:");
    for table in tables {
        println!("  📋 {}", table);
    }
    
    // 檢查用戶數量
    let user_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM users")
        .fetch_one(&pool)
        .await?;
    println!("用戶數量: {}", user_count);
    
    // 檢查產品數量
    let product_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM products")
        .fetch_one(&pool)
        .await?;
    println!("產品數量: {}", product_count);
    
    Ok(())
}

async fn execute_sql_statements(pool: &Pool<Postgres>, sql: &str) -> Result<(), Box<dyn std::error::Error>> {
    // 分割 SQL 語句
    let statements: Vec<&str> = sql
        .split(';')
        .map(|s| s.trim())
        .filter(|s| !s.is_empty() && !s.starts_with("--"))
        .collect();
    
    for (i, statement) in statements.iter().enumerate() {
        if !statement.trim().is_empty() {
            println!("  執行語句 {}: {}", i + 1, statement.lines().next().unwrap_or("").trim());
            sqlx::query(statement).execute(pool).await?;
        }
    }
    
    Ok(())
} 