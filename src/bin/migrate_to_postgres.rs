use std::env;
use sqlx::postgres::PgPoolOptions;
use sqlx::{Pool, Postgres};
use dotenvy::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=verify-full".to_string());
    
    println!("正在連接到 PostgreSQL 資料庫...");
    
    let pool: Pool<Postgres> = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 成功連接到資料庫");
    
    // 執行遷移
    println!("開始執行資料庫遷移...");
    // sqlx::migrate!("./migrations")
    //     .run(&pool)
    //     .await?;
    
    println!("✅ 資料庫遷移完成！");
    
    // 檢查表格是否建立成功
    let tables: Vec<String> = sqlx::query_scalar(
        "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("資料庫中的表格:");
    for table in tables {
        println!("  📋 {}", table);
    }
    
    Ok(())
}