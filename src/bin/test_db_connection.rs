use std::env;
use sqlx::postgres::PgPoolOptions;
use dotenvy::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();
    
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require".to_string());
    
    println!("🔗 測試 CockroachDB 連接...");
    println!("使用連接字串: {}", database_url.replace("MIYlINYSePJ-eoC0lnPqsQ", "***"));
    
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&database_url)
        .await?;
    
    println!("✅ 成功連接到 CockroachDB!");
    
    // 測試簡單查詢
    let version: String = sqlx::query_scalar("SELECT version()")
        .fetch_one(&pool)
        .await?;
    
    println!("📋 資料庫版本: {}", version);
    
    // 測試資料讀取
    let user_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM users")
        .fetch_one(&pool)
        .await?;
    
    let product_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM products")
        .fetch_one(&pool)
        .await?;
    
    let order_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM orders")
        .fetch_one(&pool)
        .await?;
    
    println!("📊 資料統計:");
    println!("  👥 使用者: {} 筆", user_count);
    println!("  📦 產品: {} 筆", product_count);
    println!("  📋 訂單: {} 筆", order_count);
    
    // 測試讀取具體資料
    #[derive(sqlx::FromRow)]
    struct User {
        id: i64,
        username: String,
        pharmacy_name: String,
    }
    
    let users: Vec<User> = sqlx::query_as::<_, User>(
        "SELECT id, username, pharmacy_name FROM users ORDER BY id LIMIT 3"
    )
    .fetch_all(&pool)
    .await?;
    
    println!("\n🧪 前3位使用者:");
    for user in users {
        println!("  ID: {}, 使用者: {}, 藥局: {}", user.id, user.username, user.pharmacy_name);
    }
    
    println!("\n🎉 所有測試通過！你的應用程式可以正常連接 CockroachDB");
    
    Ok(())
}