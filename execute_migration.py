#!/usr/bin/env python3
"""
執行價格結構重構 Migration
"""
import psycopg2
import os
from dotenv import load_dotenv

def execute_migration():
    print("🚀 開始執行 Migration...")
    
    # 載入環境變數
    load_dotenv()
    database_url = os.getenv('DATABASE_URL')
    
    if not database_url:
        print("❌ 無法找到 DATABASE_URL 環境變數")
        return False
    
    try:
        # 連接資料庫
        print("🔗 連接資料庫...")
        conn = psycopg2.connect(database_url)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # 讀取 Migration 檔案
        print("📖 讀取 Migration 檔案...")
        with open('migrations/20250809000001_refactor_price_structure.sql', 'r', encoding='utf-8') as f:
            migration_sql = f.read()
        
        # 執行 Migration
        print("🔄 執行 Migration...")
        cursor.execute(migration_sql)
        
        print("✅ Migration 執行成功！")
        
        # 驗證結果
        print("🔍 驗證 Migration 結果...")
        
        # 檢查欄位是否重命名成功
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'products' 
            AND column_name IN ('unit_price', 'selling_price')
        """)
        columns = [row[0] for row in cursor.fetchall()]
        
        if 'selling_price' in columns and 'unit_price' not in columns:
            print("✅ 欄位重命名成功：unit_price → selling_price")
        else:
            print("⚠️ 欄位重命名可能有問題")
            print(f"   找到的欄位: {columns}")
        
        # 檢查觸發器是否建立
        cursor.execute("""
            SELECT trigger_name 
            FROM information_schema.triggers 
            WHERE trigger_name IN ('trigger_sync_prices', 'trigger_sync_nhi_prices')
        """)
        triggers = [row[0] for row in cursor.fetchall()]
        
        if len(triggers) == 2:
            print("✅ 同步觸發器建立成功")
        else:
            print(f"⚠️ 觸發器建立可能有問題，找到: {triggers}")
        
        # 檢查函數是否建立
        cursor.execute("""
            SELECT routine_name 
            FROM information_schema.routines 
            WHERE routine_name IN ('sync_product_prices', 'sync_nhi_prices')
        """)
        functions = [row[0] for row in cursor.fetchall()]
        
        if len(functions) == 2:
            print("✅ 同步函數建立成功")
        else:
            print(f"⚠️ 函數建立可能有問題，找到: {functions}")
        
        cursor.close()
        conn.close()
        
        print("\n🎊 Migration 執行完成！")
        print("📋 下一步：測試價格同步機制")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration 執行失敗: {e}")
        return False

if __name__ == "__main__":
    success = execute_migration()
    if success:
        print("\n🎯 建議執行測試腳本驗證同步機制：")
        print("   python test_price_sync.py")
    else:
        print("\n💡 如果遇到問題，請檢查：")
        print("   1. 資料庫連線是否正常")
        print("   2. 是否有足夠的權限執行 DDL 操作")
        print("   3. nhi_prices 和 products 表是否存在")