#!/usr/bin/env python3
"""
測試修復後的篩選功能
"""

import requests
import json

BASE_URL = "http://localhost:8080"

def test_filter_functionality():
    """測試篩選功能"""
    print("🔍 === 測試修復後的篩選功能 ===")
    print()
    
    # 登錄獲取令牌
    admin_user = {"username": "admin", "password": "admin123"}
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json=admin_user)
    
    if login_response.status_code != 200:
        print("❌ 登錄失敗")
        return False
    
    token = login_response.json().get('data', {}).get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    # 測試不同狀態的篩選
    statuses = ['pending', 'confirmed', 'processing', 'delivered', 'cancelled']
    
    print("📊 各狀態訂單數量:")
    total_filtered = 0
    
    for status in statuses:
        response = requests.get(f"{BASE_URL}/api/orders/all?status={status}", headers=headers)
        
        if response.status_code == 200:
            orders = response.json().get('data', {}).get('orders', [])
            count = len(orders)
            total_filtered += count
            
            print(f"   {status.capitalize():12}: {count:2d} 個訂單")
            
            # 驗證返回的訂單確實是該狀態
            if count > 0:
                first_order = orders[0]
                actual_status = first_order.get('status', '').lower()
                expected_status = status.lower()
                
                if actual_status == expected_status:
                    print(f"      ✅ 狀態驗證正確")
                else:
                    print(f"      ❌ 狀態驗證失敗: 期望 {expected_status}, 實際 {actual_status}")
        else:
            print(f"   {status.capitalize():12}: API錯誤 ({response.status_code})")
    
    # 測試無篩選的總數
    print("\n📋 總訂單數量驗證:")
    all_response = requests.get(f"{BASE_URL}/api/orders/all", headers=headers)
    
    if all_response.status_code == 200:
        all_orders = all_response.json().get('data', {}).get('orders', [])
        total_all = len(all_orders)
        
        print(f"   無篩選總數: {total_all} 個訂單")
        print(f"   篩選總和:   {total_filtered} 個訂單")
        
        if total_all == total_filtered:
            print("   ✅ 數量一致，篩選功能正確")
        else:
            print("   ⚠️  數量不一致，可能有其他狀態的訂單")
    
    # 測試日期篩選（如果有的話）
    print("\n📅 測試日期篩選:")
    date_response = requests.get(
        f"{BASE_URL}/api/orders/all?start_date=2025-08-06T00:00:00Z", 
        headers=headers
    )
    
    if date_response.status_code == 200:
        date_orders = date_response.json().get('data', {}).get('orders', [])
        print(f"   2025-08-06後的訂單: {len(date_orders)} 個")
        print("   ✅ 日期篩選API響應正常")
    else:
        print(f"   ❌ 日期篩選失敗: {date_response.status_code}")
    
    # 測試組合篩選
    print("\n🔗 測試組合篩選:")
    combo_response = requests.get(
        f"{BASE_URL}/api/orders/all?status=pending&start_date=2025-08-06T00:00:00Z", 
        headers=headers
    )
    
    if combo_response.status_code == 200:
        combo_orders = combo_response.json().get('data', {}).get('orders', [])
        print(f"   待處理且2025-08-06後: {len(combo_orders)} 個訂單")
        print("   ✅ 組合篩選API響應正常")
        
        # 驗證組合篩選結果
        if len(combo_orders) > 0:
            sample_order = combo_orders[0]
            if sample_order.get('status', '').lower() == 'pending':
                print("   ✅ 組合篩選狀態正確")
            else:
                print("   ❌ 組合篩選狀態錯誤")
    else:
        print(f"   ❌ 組合篩選失敗: {combo_response.status_code}")
    
    print("\n🎉 篩選功能測試完成！")
    return True

if __name__ == "__main__":
    test_filter_functionality()