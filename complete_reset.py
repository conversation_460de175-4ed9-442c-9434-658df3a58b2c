#!/usr/bin/env python3
import psycopg2

DATABASE_URL = "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require"

def complete_reset():
    print("🔄 完全重置健保價格結構...")
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("1. 移除所有約束...")
        # 移除所有相關約束
        constraints_to_drop = [
            "ALTER TABLE products DROP CONSTRAINT IF EXISTS fk_products_nhi_code CASCADE",
            "ALTER TABLE products DROP CONSTRAINT IF EXISTS fk_products_nhi_price CASCADE", 
            "ALTER TABLE products DROP CONSTRAINT IF EXISTS products_nhi_code_key CASCADE",
        ]
        
        for sql in constraints_to_drop:
            try:
                cursor.execute(sql)
                print(f"   ✅ 執行: {sql.split()[-3]}")
            except Exception as e:
                print(f"   ⚠️  {e}")
        
        print("2. 移除欄位...")
        # 移除欄位
        columns_to_drop = [
            "ALTER TABLE products DROP COLUMN IF EXISTS nhi_code CASCADE",
            "ALTER TABLE products DROP COLUMN IF EXISTS nhi_price CASCADE"
        ]
        
        for sql in columns_to_drop:
            try:
                cursor.execute(sql)
                print(f"   ✅ 移除欄位成功")
            except Exception as e:
                print(f"   ⚠️  {e}")
        
        print("3. 移除舊表...")
        cursor.execute("DROP TABLE IF EXISTS nhi_prices CASCADE")
        print("   ✅ nhi_prices 表已移除")
        
        print("4. 創建全新的 nhi_prices 表...")
        cursor.execute("""
            CREATE TABLE nhi_prices (
                nhi_code VARCHAR(20) PRIMARY KEY,
                nhi_price DECIMAL(10,2) NOT NULL,
                selling_price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("   ✅ nhi_prices 表已創建")
        
        # 創建索引
        cursor.execute("CREATE INDEX idx_nhi_price ON nhi_prices (nhi_price)")
        cursor.execute("CREATE INDEX idx_selling_price ON nhi_prices (selling_price)")
        print("   ✅ 索引已創建")
        
        print("5. 生成健保價格數據...")
        # 獲取所有不同的價格
        cursor.execute("SELECT DISTINCT unit_price FROM products WHERE unit_price IS NOT NULL ORDER BY unit_price")
        unique_prices = [row[0] for row in cursor.fetchall()]
        print(f"   找到 {len(unique_prices)} 種不同價格")
        
        # 插入健保價格數據
        for i, price in enumerate(unique_prices, 1):
            nhi_code = f"A{i:03d}"
            cursor.execute("""
                INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price)
                VALUES (%s, %s, %s)
            """, (nhi_code, float(price), float(price)))
        print(f"   ✅ 已插入 {len(unique_prices)} 筆健保價格")
        
        print("6. 為 products 添加 nhi_code 欄位...")
        cursor.execute("ALTER TABLE products ADD COLUMN nhi_code VARCHAR(20)")
        print("   ✅ nhi_code 欄位已添加")
        
        print("7. 更新產品的 nhi_code...")
        for i, price in enumerate(unique_prices, 1):
            nhi_code = f"A{i:03d}"
            cursor.execute("UPDATE products SET nhi_code = %s WHERE unit_price = %s", (nhi_code, price))
        print("   ✅ 所有產品的 nhi_code 已更新")
        
        print("8. 添加外鍵約束...")
        cursor.execute("""
            ALTER TABLE products 
            ADD CONSTRAINT fk_products_nhi_code 
            FOREIGN KEY (nhi_code) REFERENCES nhi_prices(nhi_code)
        """)
        print("   ✅ 外鍵約束已添加")
        
        # 最終檢查
        print("\n📊 最終檢查...")
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        nhi_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM products WHERE nhi_code IS NOT NULL")  
        products_count = cursor.fetchone()[0]
        
        print(f"   ✅ nhi_prices 表: {nhi_count} 筆記錄")
        print(f"   ✅ products 表有 nhi_code: {products_count} 筆記錄")
        
        # 顯示範例
        cursor.execute("SELECT nhi_code, nhi_price, selling_price FROM nhi_prices LIMIT 3")
        print("   健保價格範例:")
        for code, nhi_price, selling_price in cursor.fetchall():
            print(f"     {code}: 健保價={nhi_price} → 賣價={selling_price}")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 完全重置成功！現在使用 nhi_code 作為主鍵")
        return True
        
    except Exception as e:
        print(f"❌ 重置失敗: {e}")
        return False

if __name__ == "__main__":
    success = complete_reset()
    print(f"\n{'🎊 重置成功!' if success else '❌ 重置失敗'}")