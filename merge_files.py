#!/usr/bin/env python3
import pandas as pd
import sys

def merge_xlsx_files():
    print("正在合併 1_3.xlsx 和 2_3.xlsx...")
    
    # 讀取兩個檔案，不使用第一行作為欄位名稱
    df1 = pd.read_excel('1_3.xlsx', header=None)
    df2 = pd.read_excel('2_3.xlsx', header=None)
    
    print(f"1_3.xlsx: {len(df1)} 行, {len(df1.columns)} 欄")
    print(f"2_3.xlsx: {len(df2)} 行, {len(df2.columns)} 欄")
    
    # 確定最大欄位數
    max_cols = max(len(df1.columns), len(df2.columns))
    print(f"合併後將有 {max_cols} 欄")
    
    # 如果 df1 欄位數較少，補齊空欄位
    if len(df1.columns) < max_cols:
        missing_cols = max_cols - len(df1.columns)
        print(f"為 1_3.xlsx 補齊 {missing_cols} 個空欄位")
        
        # 添加空欄位
        for i in range(missing_cols):
            col_index = len(df1.columns) + i
            df1[col_index] = None
    
    # 如果 df2 欄位數較少，補齊空欄位
    if len(df2.columns) < max_cols:
        missing_cols = max_cols - len(df2.columns)
        print(f"為 2_3.xlsx 補齊 {missing_cols} 個空欄位")
        
        # 添加空欄位
        for i in range(missing_cols):
            col_index = len(df2.columns) + i
            df2[col_index] = None
    
    # 重新排序欄位，確保順序一致
    df1 = df1.reindex(columns=range(max_cols))
    df2 = df2.reindex(columns=range(max_cols))
    
    # 合併兩個 DataFrame
    merged_df = pd.concat([df1, df2], ignore_index=True)
    
    print(f"合併後總行數: {len(merged_df)}")
    print(f"合併後總欄數: {len(merged_df.columns)}")
    
    # 儲存合併後的檔案
    output_file = 'merged_1_3_2_3.xlsx'
    print(f"正在儲存到 {output_file}...")
    merged_df.to_excel(output_file, index=False, header=False)
    
    print("合併完成！")
    print(f"輸入檔案:")
    print(f"  - 1_3.xlsx: {len(df1)} 行")
    print(f"  - 2_3.xlsx: {len(df2)} 行")
    print(f"輸出檔案: {output_file} ({len(merged_df)} 行, {len(merged_df.columns)} 欄)")
    
    # 顯示前幾行作為預覽
    print(f"\n合併後前5行預覽:")
    print(merged_df.head())

if __name__ == "__main__":
    try:
        merge_xlsx_files()
    except Exception as e:
        print(f"錯誤: {e}")
        sys.exit(1)