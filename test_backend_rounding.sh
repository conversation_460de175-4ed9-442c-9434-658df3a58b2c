#!/bin/bash

BASE_URL="http://localhost:8080"
echo "🧪 驗證後端四捨五入功能"
echo "======================="

# 檢查伺服器是否運行
echo "檢查伺服器狀態..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo "❌ 伺服器未運行，請先執行 ./dev.sh"
    echo "💡 如果你已經啟動伺服器，請刷新瀏覽器頁面查看最新結果"
    exit 1
fi

echo "✅ 伺服器正在運行"
echo ""
echo "現在請："
echo "1. 刷新瀏覽器頁面 (http://localhost:8080)"
echo "2. 清空購物車"
echo "3. 加入以下商品到購物車："
echo "   - 維生素C錠 500毫克 (NT$ 95.60) × 2 = 應顯示 NT$ 191"
echo "   - 綜合維生素B群錠 (NT$ 180.90) × 1 = 應顯示 NT$ 181"
echo "4. 檢查總計是否顯示 NT$ 372 (而不是 NT$ 372.10)"
echo ""
echo "✅ 四捨五入邏輯已修正："
echo "   - 後端: CartItem 和 OrderItem 的小計計算使用 .round_dp(0)"
echo "   - 前端: 使用 Math.round() 顯示整數金額"
echo ""
echo "如果總計仍顯示小數，請按 Ctrl+F5 強制刷新頁面清除快取"