<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API 測試頁面</h1>
    <button onclick="testLogin()">測試登入</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const result = document.getElementById('result');
            try {
                result.innerHTML = '正在測試...';
                
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'testuser',
                        password: 'password123'
                    })
                });
                
                const data = await response.json();
                result.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
            } catch (error) {
                result.innerHTML = '<p style="color: red;">錯誤: ' + error.message + '</p>';
                console.error('登入測試錯誤:', error);
            }
        }
    </script>
</body>
</html>