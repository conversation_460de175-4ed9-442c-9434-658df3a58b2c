/* 基本樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 必填欄位星號 */
.required {
    color: #e74c3c;
    font-weight: bold;
    margin-left: 3px;
}

/* 水平表單排列 */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group-inline {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-group-inline label {
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group-inline input {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group-inline input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 響應式設計 - 小螢幕時垂直排列 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    font-size: 21px; /* 字體放大50% (14px * 1.5 = 21px) */
}

.container {
    max-width: 1280px;  /* 調整為19寸螢幕適用寬度 */
    margin: 0 auto;
    padding: 20px;
}

/* 標題 */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-logo {
    height: 50px;
    width: auto;
    border-radius: 8px;
}

.title-section {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

header h1 {
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.subtitle {
    font-size: 0.9rem;
    font-weight: 400;
    opacity: 0.9;
    margin: 0;
    color: #f0f0f0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 卡片樣式 */
.card {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card h2,
.card h3 {
    margin-bottom: 20px;
    color: #333;
}

/* 表單樣式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 24px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* 按鈕樣式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    font-size: 24px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* 導航標籤 */
.nav-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.nav-tab {
    padding: 15px 25px;
    background: none;
    border: none;
    font-size: 24px;
    font-weight: 500;
    cursor: pointer;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.nav-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.nav-tab:hover {
    color: #667eea;
}

/* 標籤內容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 搜尋列 */
.search-bar {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.search-bar input {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 24px;
}

/* 產品管理樣式 */
.product-header {
    margin-bottom: 25px;
}

.product-header h3 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 30px;
    font-weight: 600;
}

.search-controls {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.search-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.search-bar label {
    font-weight: 600;
    color: #495057;
    min-width: 80px;
}

.search-bar input {
    flex: 1;
    min-width: 300px;
    padding: 10px 15px;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    font-size: 21px;
}

.filter-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-options select {
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    background: white;
    font-size: 21px;
    min-width: 150px;
}

/* 產品表格樣式 */
.products-table-container {
    overflow-x: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
}

.products-table {
    width: 100%;
    border-collapse: collapse;
}

.products-table .no-data,
.products-table .error {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

.products-table .error {
    color: #dc3545;
}

.products-table .low-stock {
    color: #dc3545;
    font-weight: bold;
}

background: white;
font-size: 21px;
}

.products-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    font-weight: 600;
    padding: 12px 8px;
    text-align: center;
    border-bottom: 2px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.products-table th:last-child {
    border-right: none;
}

.products-table td {
    padding: 10px 8px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    vertical-align: middle;
}

.products-table td:last-child {
    border-right: none;
}

.products-table tbody tr:hover {
    background-color: #f8f9fa;
}

.products-table tbody tr:nth-child(even) {
    background-color: #fdfdfd;
}

/* 產品狀態樣式 */
.product-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
    display: inline-block;
}

.product-status.in-stock {
    background: #d4edda;
    color: #155724;
}

.product-status.low-stock {
    background: #fff3cd;
    color: #856404;
}

.product-status.out-of-stock {
    background: #f8d7da;
    color: #721c24;
}

/* 操作按鈕樣式 */
.product-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.quantity-input {
    width: 50px;
    padding: 4px 6px;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    text-align: center;
    font-size: 18px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 18px;
    border-radius: 4px;
}

/* 分頁樣式 */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-top: 1px solid #dee2e6;
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-info {
    color: #6c757d;
    font-size: 21px;
}

/* 購物車數量輸入框樣式 */
.quantity-input-cart {
    width: 60px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    margin: 0 8px;
    font-size: 21px;
    transition: border-color 0.3s;
}

.quantity-input-cart:focus {
    outline: none;
    border-color: #667eea;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-input-group {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 0 10px;
}

.page-input-group span {
    color: #6c757d;
    font-size: 21px;
}

#page-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-align: center;
    font-size: 21px;
}

#page-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.page-number {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    text-decoration: none;
    border-radius: 4px;
    font-size: 21px;
    cursor: pointer;
    transition: all 0.2s;
}

.page-number:hover {
    background: #e9ecef;
}

.page-number.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 產品詳細資訊樣式 */
.product-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}

.product-name-cell {
    text-align: left;
    max-width: 200px;
    word-wrap: break-word;
}

.product-price {
    font-weight: 600;
    color: #28a745;
}

.stock-quantity {
    font-weight: 600;
}

.stock-low {
    color: #ffc107;
}

.stock-out {
    color: #dc3545;
}

/* 購物車樣式 */
.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    margin-bottom: 5px;
}

.cart-item-price {
    color: #667eea;
    font-weight: 500;
}

.cart-item-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.cart-summary {
    border-top: 2px solid #eee;
    padding-top: 20px;
    margin-top: 20px;
    text-align: right;
}

.total {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
}

/* 訂單樣式 */
.order-item {
    border: 2px solid #eee;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    background: white;
}

.order-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

/* 對齊欄位：左=訂單編號 中=狀態 右=金額 */
.order-header .order-number { flex: 1; min-width: 0; }
.order-header .order-status { width: 140px; display: flex; justify-content: center; }
.order-header .order-total { width: 140px; text-align: right; }

.order-number {
    font-weight: 600;
    color: #333;
}

.order-status {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 21px;
    font-weight: 500;
}

.order-status.pending {
    background: #fff3cd;
    color: #856404;
}

.order-status.confirmed {
    background: #d4edda;
    color: #155724;
}

.order-total {
    font-size: 18px;
    font-weight: 600;
    color: #667eea;
}

.order-notes {
    margin-top: 10px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 5px;
    font-size: 21px;
    color: #6c757d;
    border-left: 3px solid #dee2e6;
}

/* 載入指示器 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading p {
    color: white;
    font-size: 18px;
}

/* 訊息提示 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 5px;
    font-weight: 500;
    z-index: 1001;
    animation: slideIn 0.3s ease-out;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 註冊連結 */
.register-link {
    text-align: center;
    margin-top: 20px;
}

.register-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.register-link a:hover {
    text-decoration: underline;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-tab {
        flex: 1;
        min-width: 120px;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .cart-item-actions {
        width: 100%;
        justify-content: space-between;
    }

    .search-bar {
        flex-direction: column;
    }
}

/* 系統
管理樣式 */
.admin-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.admin-nav-tab {
    padding: 8px 16px;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.admin-nav-tab.active {
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: bold;
}

.admin-nav-tab:hover {
    background-color: #f8f9fa;
}

.admin-tab-content {
    display: none;
}

.admin-tab-content.active {
    display: block;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.admin-table th,
.admin-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.admin-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.admin-table tr:hover {
    background-color: #f5f5f5;
}

.role-actions {
    display: flex;
    gap: 5px;
}

.role-actions .btn {
    padding: 4px 8px;
    font-size: 18px;
}

/* 權限列表樣式 */
.permissions-group {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.permissions-group-header {
    background-color: #f8f9fa;
    padding: 10px 15px;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
}

.permissions-group-content {
    padding: 15px;
}

.permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.permission-item:last-child {
    border-bottom: none;
}

.permission-name {
    font-weight: bold;
}

.permission-description {
    color: #666;
    font-size: 21px;
}

.permission-action {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 18px;
}

/* 模態框樣式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 900px;  /* 增加模態框寬度 */
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
}

.modal-header h3 {
    margin: 0;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

.modal-body {
    padding: 20px;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* 權限複選框網格 */
.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 5px;
}

.permission-checkbox-group {
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
}

.permission-checkbox-group h5 {
    margin: 0 0 10px 0;
    color: #007bff;
    font-size: 21px;
    font-weight: bold;
}

.permission-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.permission-checkbox input {
    margin-right: 8px;
}

.permission-checkbox label {
    font-size: 13px;
    cursor: pointer;
}

/* 權限列表樣式 */
.permission-group {
    margin-bottom: 25px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.permission-group h4 {
    background-color: #f5f5f5;
    margin: 0;
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    color: #333;
    font-size: 24px;
}

.permission-items {
    padding: 15px;
}

.permission-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
    padding: 10px;
    background-color: #fafafa;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}

.permission-item:last-child {
    margin-bottom: 0;
}

.permission-item strong {
    color: #333;
    font-size: 21px;
    margin-bottom: 4px;
}

.permission-item span {
    color: #666;
    font-size: 13px;
}

/* 用戶搜尋 */
.user-search {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.user-search input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 用戶權限卡片 */
.user-permission-card {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
}

.user-permission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.user-name {
    font-weight: bold;
    font-size: 24px;
}

.user-role {
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 18px;
}

.user-permissions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.user-permission-tag {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .admin-nav {
        flex-direction: column;
    }

    .admin-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .user-permission-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* 購物車圖標按鈕樣式 */
.btn-cart-icon {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s;
    color: #667eea;
}

.btn-cart-icon:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: scale(1.1);
}

.btn-cart-icon:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: #ccc;
}

.btn-cart-icon:disabled:hover {
    background: none;
    transform: none;
}

/* 訂單詳情模態框樣式 */
.order-detail-modal .modal-content {
    max-width: 1200px;  /* 增加訂單詳情寬度 */
}

.order-detail-header {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.order-detail-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

.order-detail-info>div {
    display: flex;
    flex-direction: column;
}

.order-detail-info label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 21px;
}

.order-detail-info span {
    color: #333;
    font-size: 24px;
}

.order-items-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.order-items-table th,
.order-items-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.order-items-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #495057;
}

.order-items-table tbody tr:hover {
    background-color: #f8f9fa;
}

.order-items-table .product-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}

.order-items-table .price {
    text-align: right;
    font-weight: 600;
    color: #28a745;
}

.order-items-table .quantity {
    text-align: center;
    font-weight: 600;
}

.order-total-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    text-align: right;
}

.order-total-amount {
    font-size: 30px;
    font-weight: 700;
    color: #667eea;
    margin-top: 10px;
}

.order-status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 21px;
    font-weight: 500;
    text-align: center;
}

.order-status-badge.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.order-status-badge.confirmed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.order-status-badge.processing {
    background: #cce5f6;
    color: #004085;
    border: 1px solid #b3d7ff;
}

.order-status-badge.shipped {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.order-status-badge.delivered {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.order-status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.order-number-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.2s;
}

.order-number-link:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .order-detail-modal .modal-content {
        width: 95%;
        max-width: none;
    }

    .order-detail-info {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .order-items-table {
        font-size: 21px;
    }

    .order-items-table th,
    .order-items-table td {
        padding: 8px 4px;
    }
}

/* 管理員訂單管理樣式 */
.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.admin-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 300px;
}

.order-filters {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.order-filters select,
.order-filters input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 21px;
}

.order-filters select {
    min-width: 120px;
}



.order-stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.stat-item {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    text-align: center;
    min-width: 80px;
}

.stat-item .stat-number {
    font-size: 18px;
    font-weight: bold;
    color: #007bff;
    display: block;
}

.stat-item .stat-label {
    font-size: 18px;
    color: #666;
    margin-top: 2px;
}

/* 管理員訂單項目樣式 */
.admin-order-item {
    border-left: 4px solid #007bff;
    background: #f8f9fa;
}

.admin-order-item .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.admin-order-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.admin-order-actions .btn {
    padding: 4px 8px;
    font-size: 18px;
    border-radius: 3px;
}

.order-user {
    background: #e3f2fd;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 8px 0;
    font-size: 21px;
}

.order-user strong {
    color: #1976d2;
}

.order-item .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 14px;
    border-radius: 16px;
    font-size: 20px; /* 放大一倍 */
    font-weight: 700;
    line-height: 1;
    min-width: 96px; /* 固定寬度確保對齊 */
    text-transform: none;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-processing {
    background: #cce5ff;
    color: #004085;
}

.status-shipped {
    background: #e2e3e5;
    color: #383d41;
}

.status-delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .orders-header {
        flex-direction: column;
        align-items: stretch;
    }

    .admin-controls {
        min-width: auto;
    }

    .order-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .order-filters select {
        width: 100%;
    }

    .order-stats {
        justify-content: space-between;
    }

    .stat-item {
        flex: 1;
        min-width: 60px;
    }
}

/* 批次操作樣式 
*/
.batch-operations {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.batch-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.selected-count {
    font-size: 21px;
    color: #666;
    font-weight: bold;
}

.order-checkbox {
    margin-right: 10px;
    transform: scale(1.2);
}

.order-item.selected {
    background: #e3f2fd;
    border-left-color: #2196f3;
}

.order-item {
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s;
}

.order-item:hover {
    background: #f5f5f5;
}

.order-item.selected:hover {
    background: #e1f5fe;
}

.order-actions {
    display: flex;
    gap: 5px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.order-actions .btn {
    padding: 3px 8px;
    font-size: 11px;
    border-radius: 3px;
}

.btn-status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.btn-status-processing {
    background: #cce5ff;
    color: #004085;
    border: 1px solid #74b9ff;
}

.btn-status-shipped {
    background: #d4edda;
    color: #155724;
    border: 1px solid #00b894;
}

.btn-status-pending:hover {
    background: #ffeaa7;
}

.btn-status-processing:hover {
    background: #74b9ff;
    color: white;
}

.btn-status-shipped:hover {
    background: #00b894;
    color: white;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .batch-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .batch-controls select,
    .batch-controls button {
        width: 100%;
    }

    .order-actions {
        justify-content: center;
    }
}

/* 大螢幕優化 (針對 27 吋螢幕等) */
@media (min-width: 1920px) {
    .container {
        max-width: 2200px;
        padding: 30px 40px;
    }
    
    /* 產品表格優化 */
    .product-table th,
    .product-table td {
        padding: 15px 20px;
        font-size: 24px;
    }
    
    .product-table th:first-child,
    .product-table td:first-child {
        min-width: 120px;  /* 產品代碼欄位 */
    }
    
    .product-table th:nth-child(2),
    .product-table td:nth-child(2) {
        min-width: 400px;  /* 產品名稱欄位 */
    }
    
    /* 搜尋區域 */
    .search-section {
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .search-controls {
        gap: 20px;
    }
    
    /* 模態框 */
    .modal-content {
        max-width: 1200px;
    }
    
    .order-detail-modal .modal-content {
        max-width: 1500px;
    }
    
    /* 卡片佈局更寬 */
    .product-card {
        padding: 25px;
    }
    
    /* 按鈕更大 */
    .btn {
        padding: 12px 24px;
        font-size: 24px;
    }
    
    /* 標題區域 */
    header {
        padding: 30px;
        margin-bottom: 40px;
    }
    
    .header-logo {
        height: 60px;
    }
}

/* 產品網格卡片佈局 */
.products-grid-container {
    width: 100%;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    /* 讓容器本身成為可滾動區，避免表頭與內容因捲軸寬度不一致而錯位 */
    max-height: 600px;
    overflow-y: auto;
    scrollbar-gutter: stable;
    /* 統一欄位寬度設定（預設） */
    --grid-columns: 180px 600px 200px 200px 200px 200px 90px;
}

.products-grid-header {
    display: grid;
    grid-template-columns: var(--grid-columns);
    background: #4a9eff;
    color: white;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    /* 固定在容器頂部 */
    position: sticky;
    top: 0;
    z-index: 2;
}

.grid-header-item {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.grid-header-item:last-child {
    border-right: none;
}

.products-grid-body {
    /* 滾動交由容器處理，避免表頭與內容寬度不一致 */
}

/* 產品卡片 */
.product-card {
    display: grid;
    grid-template-columns: var(--grid-columns);
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
    background: white;
    min-height: 80px;
    align-items: center;
}

.product-card:hover {
    background: #f8f9ff;
}

.product-card:last-child {
    border-bottom: none;
}

/* 健保資訊欄 */
.nhi-info {
    padding: 20px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    border-right: 1px solid #f0f0f0;
    height: 100%;
}

.drug-icon {
    width: 40px;
    height: 40px;
    background: #e3f2fd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    color: #1976d2;
}

.nhi-details {
    flex: 1;
}

.nhi-code {
    font-weight: bold;
    color: #333;
    font-size: 18px;
    margin-bottom: 2px;
}

.nhi-level {
    font-size: 11px;
    color: #666;
}

.nhi-expiry {
    font-size: 10px;
    color: #999;
    margin-top: 2px;
}

/* 產品名稱欄 */
.product-name {
    padding: 20px 12px;
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.product-title {
    font-weight: bold;
    color: #333;
    font-size: 21px;
    margin-bottom: 4px;
}

.product-ingredients {
    font-size: 18px;
    color: #666;
}

/* 價格欄 */
.product-price {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.price-value {
    font-weight: bold;
    color: #333;
    font-size: 24px;
}

.price-unit {
    font-size: 11px;
    color: #666;
}

/* 數量欄 */
.product-quantity {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.quantity-input {
    width: 60px;
    padding: 5px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 18px;
}

.quantity-label {
    font-size: 10px;
    color: #666;
    margin-top: 2px;
}

/* 功能欄 */
.product-actions {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
}

.action-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.2s ease;
}

.action-btn.download {
    background: #e8f5e8;
    color: #4caf50;
}

.action-btn.favorite {
    background: #fff3e0;
    color: #ff9800;
}
.action-btn.cart {
    background: #e8f5e8;
    color: #4caf50;
    font-size: 16px;
}
.action-btn.cart:disabled {
    background: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
    transform: none;
}

.action-btn.refresh {
    background: #e3f2fd;
    color: #2196f3;
}

.action-btn:hover {
    transform: scale(1.1);
}

/* 狀態欄 */
.product-status {
    padding: 20px 12px;
    text-align: center;
    border-right: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.product-status .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 14px;
    border-radius: 16px;
    font-size: 20px; /* 放大一倍 */
    font-weight: 700;
    line-height: 1;
    text-transform: none;
    min-width: 96px; /* 統一寬度，避免看起來不對齊 */
}

.status-badge.in-stock {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-badge.supply-interrupted {
    background: #fff3e0;
    color: #f57c00;
}

.status-badge.out-of-stock {
    background: #ffebee;
    color: #d32f2f;
}

/* 保存欄 */
.product-save {
    padding: 20px 12px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.save-btn {
    background: #4a9eff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background-color 0.2s ease;
}

.save-btn:hover {
    background: #357abd;
}

/* 響應式設計 */
@media (max-width: 1400px) {
    .products-grid-container {
        --grid-columns: 160px 500px 180px 200px 200px 200px 80px;
    }
}

@media (max-width: 1200px) {
    .products-grid-container {
        --grid-columns: 140px 450px 160px 200px 200px 200px 70px;
    }
    
    .drug-icon {
        width: 35px;
        height: 35px;
        font-size: 18px;
    }
}

@media (max-width: 768px) {
    .products-grid-container {
        overflow-x: auto;
        --grid-columns: 150px 200px 80px 200px 200px 200px 60px;
    }
    
    .products-grid-header,
    .product-card {
        min-width: 1100px;
    }
}

/* 訊息管理樣式 */
#messages-admin {
    padding: 20px 0;
}

.messages-list {
    margin-top: 20px;
}

.message-item {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 20px;
    transition: all 0.3s ease;
}

.message-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.message-item.unread {
    border-left: 4px solid #667eea;
    background: #f8f9ff;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.message-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.message-meta {
    display: flex;
    align-items: center;
    gap: 10px;
}

.message-type {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 18px;
    font-weight: 500;
}

.message-type.announcement {
    background: #e7f3ff;
    color: #0066cc;
}

.message-type.system {
    background: #f5f5f5;
    color: #666666;
}

.message-type.promotion {
    background: #fff0e6;
    color: #cc6600;
}

.message-content {
    color: #666;
    line-height: 1.6;
    margin: 10px 0;
    font-size: 21px;
}

.message-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    color: #888;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.message-date {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.message-actions {
    display: flex;
    gap: 8px;
}

.message-actions .btn {
    padding: 6px 12px;
    font-size: 18px;
    border-radius: 4px;
}

.message-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 18px;
    font-weight: 500;
}

.message-status.active {
    background: #d4edda;
    color: #155724;
}

.message-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* 訊息篩選器樣式 */
.messages-filters {
    display: flex;
    gap: 20px;
    align-items: end;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 120px;
}

.filter-group label {
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.filter-group select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 21px;
    background: white;
}

/* 響應式設計 - 訊息管理 */
@media (max-width: 768px) {
    .messages-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .message-header {
        flex-direction: column;
        gap: 10px;
    }

    .message-meta {
        justify-content: flex-start;
    }

    .message-footer {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .message-actions {
        width: 100%;
        justify-content: flex-start;
    }
}