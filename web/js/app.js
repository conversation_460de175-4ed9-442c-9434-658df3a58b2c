// API 基礎設定
const API_BASE = "http://localhost:8080";
console.log("API_BASE 設定為:", API_BASE);
console.log("當前時間戳:", new Date().toISOString());

// 檢查並清除不同域名的舊快取（只在域名改變時清除）
const currentDomain = localStorage.getItem("current_domain");
if (currentDomain && currentDomain !== API_BASE) {
  console.log(`域名已改變 (${currentDomain} → ${API_BASE})，清除舊快取`);
  localStorage.clear();
}
localStorage.setItem("current_domain", API_BASE);

// 讀取認證資料並添加詳細日誌
let authToken = localStorage.getItem("authToken");
let tokenExpiry = localStorage.getItem("tokenExpiry");
let currentUser = null;

// 啟動時的認證狀態檢查
console.log("認證狀態:", authToken ? "已登入" : "未登入");

// Token 管理函數
function setAuthToken(token, expiresIn) {
  authToken = token;
  const expiryTime = Date.now() + expiresIn * 1000;
  tokenExpiry = expiryTime;

  localStorage.setItem("authToken", token);
  localStorage.setItem("tokenExpiry", expiryTime.toString());

  console.log(`Token 更新：有效期至 ${new Date(expiryTime).toLocaleString()}`);
}

function isTokenExpired() {
  if (!authToken || !tokenExpiry) {
    return true;
  }

  const now = Date.now();
  const expiry = parseInt(tokenExpiry);

  // 只在真正過期時才認為過期，不提前判斷
  return now >= expiry;
}

function isTokenNearExpiry() {
  if (!authToken || !tokenExpiry) {
    return false;
  }

  const now = Date.now();
  const expiry = parseInt(tokenExpiry);

  // 提前 10 分鐘認為即將過期，用於自動刷新
  return now >= expiry - 10 * 60 * 1000;
}

function isTokenValid() {
  return authToken && tokenExpiry && !isTokenExpired();
}

function clearAuthToken() {
  authToken = null;
  tokenExpiry = null;
  localStorage.removeItem("authToken");
  localStorage.removeItem("tokenExpiry");
  localStorage.removeItem("user");
  console.log('已清除認證資料');
}

// API 請求函數 - 增強版
async function apiRequest(url, options = {}) {
  const fullUrl = `${API_BASE}${url}`;
  console.log("發送 API 請求到:", fullUrl);

  // 如果正在刷新 token，等待完成
  if (window.isRefreshingToken) {
    await new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (!window.isRefreshingToken) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  const config = {
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
    ...options,
  };

  // 添加認證 token
  if (authToken && isTokenValid()) {
    config.headers["Authorization"] = `Bearer ${authToken}`;
  }

  try {
    const response = await fetch(fullUrl, config);

    if (response.status === 401) {
      console.log(`收到 401 錯誤，資源: ${url}`);

      // 如果是刷新 token 的請求，直接返回錯誤
      if (url.includes('/auth/refresh')) {
        throw new Error("認證 token 無效");
      }

      // 嘗試自動刷新 token
      if (authToken) {
        console.log("嘗試自動刷新 token...");
        const refreshSuccess = await attemptTokenRefresh();

        if (refreshSuccess && isTokenValid()) {
          console.log("刷新成功，重試原始請求");
          // 更新 Authorization header
          config.headers["Authorization"] = `Bearer ${authToken}`;

          // 重試原始請求
          const retryResponse = await fetch(fullUrl, config);
          const retryData = await retryResponse.json();

          if (retryResponse.ok) {
            return retryData;
          } else {
            throw new Error(retryData.error || `HTTP ${retryResponse.status}`);
          }
        } else {
          console.log("Token 刷新失敗，需要重新登入");
        }
      }

      // 刷新失敗或無 token，清除認證並跳轉到登入頁
      clearAuthToken();
      currentUser = null;
      showLogin();
      throw new Error("請重新登入");
    }

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP ${response.status}`);
    }

    return data;
  } catch (error) {
    // 網路連線錯誤處理
    if (error.name === "TypeError") {
      if (!authToken) {
        console.log("未登入狀態，網路錯誤");
        throw new Error("請先登入");
      } else {
        console.log("網路連線異常:", error.message);
        throw new Error("網路連線異常，請檢查網路連線");
      }
    }

    console.error(`API 請求失敗 (${url}):`, error.message);
    throw error;
  }
}

// DOM 元素
const loginSection = document.getElementById("login-section");
const registerSection = document.getElementById("register-section");
const mainContent = document.getElementById("main-content");
const userInfo = document.getElementById("user-info");
const loading = document.getElementById("loading");
const message = document.getElementById("message");

// 自動 token 刷新機制
function setupAutomaticTokenRefresh() {
  // 每 5 分鐘檢查一次 token 狀態
  setInterval(async () => {
    if (!authToken || !isTokenValid()) {
      return;
    }

    if (isTokenNearExpiry()) {
      console.log("定期檢查：發現 token 即將過期，自動刷新...");
      const refreshSuccess = await attemptTokenRefresh();
      if (refreshSuccess) {
        console.log("定期刷新成功");
      } else {
        console.log("定期刷新失敗");
      }
    }
  }, 5 * 60 * 1000); // 5 分鐘
}

// 頁面隱藏/顯示事件處理
document.addEventListener('visibilitychange', async () => {
  if (!document.hidden && authToken) {
    console.log('頁面重新顯示，檢查 token 狀態');

    if (isTokenExpired()) {
      console.log('頁面返回時發現 token 已過期，嘗試刷新');
      const refreshSuccess = await attemptTokenRefresh();
      if (!refreshSuccess) {
        showMessage('登入已過期，請重新登入', 'warning');
        clearAuthToken();
        showLogin();
      }
    } else if (isTokenNearExpiry()) {
      console.log('頁面返回時發現 token 即將過期，預先刷新');
      await attemptTokenRefresh();
    }
  }
});

// 初始化應用程式
document.addEventListener("DOMContentLoaded", function () {
  console.log('DOM 已載入，開始初始化...');

  setTimeout(async () => {
    await initializeApp();
    setupEventListeners();
    setupAutomaticTokenRefresh();

    console.log('=== 應用程式初始化完成 ===');
  }, 0);
});

// 初始化應用程式 - 增強版
async function initializeApp() {
  console.log("=== 開始初始化應用程式 ===");
  console.log("authToken:", authToken ? "存在" : "不存在");
  console.log("tokenExpiry:", tokenExpiry ? new Date(parseInt(tokenExpiry)).toLocaleString() : "不存在");

  // 先嘗試從 localStorage 恢復用戶資料
  const savedUser = localStorage.getItem("user");
  if (savedUser) {
    try {
      currentUser = JSON.parse(savedUser);
      console.log("從 localStorage 恢復用戶資料:", currentUser.username);
    } catch (error) {
      console.error("解析用戶資料失敗:", error);
      localStorage.removeItem("user");
    }
  }

  // 檢查 token 狀態
  if (!authToken) {
    console.log("沒有 token，顯示登入頁面");
    showLogin();
    return;
  }

  // Token 已完全過期
  if (isTokenExpired()) {
    console.log("Token 已過期，嘗試刷新...");
    const refreshSuccess = await attemptTokenRefresh();
    if (refreshSuccess && isTokenValid()) {
      console.log("Token 刷新成功，繼續初始化");
      await completeInitialization();
      return;
    } else {
      console.log("Token 刷新失敗，需要重新登入");
      clearAuthToken();
      showLogin();
      return;
    }
  }

  // Token 有效但即將過期，先刷新再繼續
  if (isTokenNearExpiry()) {
    console.log("Token 即將過期，預先刷新...");
    const refreshSuccess = await attemptTokenRefresh();
    if (!refreshSuccess) {
      console.warn("Token 預先刷新失敗，但當前 token 仍有效");
    }
  }

  // Token 有效，完成初始化
  console.log("Token 有效，完成應用初始化");
  await completeInitialization();
}

// 完成應用初始化
async function completeInitialization() {
  try {
    // 如果已有用戶資料，先顯示界面再更新
    if (currentUser && currentUser.username) {
      console.log("使用快取用戶資料快速顯示界面");
      updateUserInfo();
      updateUIBasedOnPermissions();
      showMainContent();

      // 在背景驗證和更新用戶資料
      try {
        await getCurrentUser();
        console.log("背景更新用戶資料成功");
      } catch (error) {
        console.warn("背景更新用戶資料失敗，使用快取資料:", error.message);
        // 如果是網路錯誤，不影響用戶體驗
        if (error.name === "TypeError" || error.message.includes('Network')) {
          showMessage("網路連線異常，使用離線模式", "warning");
        }
      }
    } else {
      // 沒有快取資料，需要從服務器獲取
      console.log("沒有快取資料，從服務器獲取用戶資料");
      await getCurrentUser();
      updateUserInfo();
      updateUIBasedOnPermissions();
      showMainContent();
    }
  } catch (error) {
    console.error("完成初始化時發生錯誤:", error);

    // 根據錯誤類型決定處理方式
    if (error.message.includes("401") || error.message.includes("請重新登入")) {
      console.log("認證失效，嘗試刷新 token...");
      const refreshSuccess = await attemptTokenRefresh();
      if (refreshSuccess) {
        try {
          await getCurrentUser();
          updateUserInfo();
          updateUIBasedOnPermissions();
          showMainContent();
          return;
        } catch (retryError) {
          console.error("刷新後仍然失敗:", retryError);
        }
      }
      // 刷新失敗，清除認證
      clearAuthToken();
      showLogin();
      showMessage("登入已過期，請重新登入", "warning");
    } else if (error.name === "TypeError" && currentUser) {
      // 網路錯誤但有快取資料，使用離線模式
      console.log("網路錯誤，使用快取資料離線模式");
      updateUserInfo();
      updateUIBasedOnPermissions();
      showMainContent();
      showMessage("網路連線異常，使用離線模式", "warning");
    } else {
      // 其他錯誤
      console.error("初始化失敗:", error);
      showMessage("應用初始化失敗: " + error.message, "error");
      clearAuthToken();
      showLogin();
    }
  }
}

// 設定事件監聽器
function setupEventListeners() {
  // 登入表單
  document.getElementById("login-form").addEventListener("submit", handleLogin);

  // 註冊表單
  document
    .getElementById("register-form")
    .addEventListener("submit", handleRegister);

  // 個人資料表單
  document
    .getElementById("profile-form")
    .addEventListener("submit", handleProfileUpdate);

  // 顯示註冊/登入表單
  document
    .getElementById("show-register")
    .addEventListener("click", showRegister);
  document.getElementById("show-login").addEventListener("click", showLogin);

  // 登出
  document.getElementById("logout-btn").addEventListener("click", logout);

  // 導航標籤
  document.querySelectorAll(".nav-tab").forEach((tab) => {
    tab.addEventListener("click", () => switchTab(tab.dataset.tab));
  });

  // 管理員標籤導航
  document.querySelectorAll(".admin-nav-tab").forEach((tab) => {
    tab.addEventListener("click", () => switchAdminTab(tab.dataset.adminTab));
  });

  // 產品搜尋
  document
    .getElementById("search-btn")
    .addEventListener("click", searchProducts);
  document
    .getElementById("product-search")
    .addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        searchProducts();
      }
    });

  // 清除搜尋
  document
    .getElementById("clear-search-btn")
    .addEventListener("click", clearSearch);

  // 分頁控制
  document
    .getElementById("prev-page")
    .addEventListener("click", () => changePage(currentPage - 1));
  document
    .getElementById("next-page")
    .addEventListener("click", () => changePage(currentPage + 1));
  document
    .getElementById("goto-page")
    .addEventListener("click", gotoPage);
  document
    .getElementById("page-input")
    .addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        gotoPage();
      }
    });

  // 購物車操作
  document.getElementById("checkout-btn").addEventListener("click", checkout);
  document
    .getElementById("clear-cart-btn")
    .addEventListener("click", clearCart);
}

// 顯示/隱藏函數
function showLogin() {
  loginSection.style.display = "block";
  registerSection.style.display = "none";
  mainContent.style.display = "none";
  userInfo.style.display = "none";
}

function showRegister() {
  loginSection.style.display = "none";
  registerSection.style.display = "block";
  mainContent.style.display = "none";
  userInfo.style.display = "none";
}

function showMainContent() {
  loginSection.style.display = "none";
  registerSection.style.display = "none";
  mainContent.style.display = "block";
  userInfo.style.display = "flex";

  // 更新用戶資訊和權限顯示
  updateUserInfo();
  updateUIBasedOnPermissions();

  // 預設載入產品頁面
  switchTab("products");
}

function showLoading() {
  if (loading) loading.style.display = "flex";
}

function hideLoading() {
  if (loading) loading.style.display = "none";
}

function showMessage(text, type = "success") {
  if (message) {
    message.textContent = text;
    message.className = `message ${type}`;
    message.style.display = "block";

    setTimeout(() => {
      message.style.display = "none";
    }, 3000);
  }
}

// 登入處理
async function handleLogin(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const loginData = {
    username: formData.get("username"),
    password: formData.get("password"),
  };

  try {
    showLoading();
    const response = await apiRequest("/api/auth/login", {
      method: "POST",
      body: JSON.stringify(loginData),
    });

    if (response.success) {
      setAuthToken(response.data.token, response.data.expires_in);
      currentUser = response.data.user;
      // 將permissions資料附加到user物件
      currentUser.permissions = response.data.permissions;
      localStorage.setItem("user", JSON.stringify(currentUser));

      showMessage("登入成功！");
      showMainContent();
    } else {
      throw new Error(response.error || "登入失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  } finally {
    hideLoading();
  }
}

// 註冊處理
async function handleRegister(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const password = formData.get("password");
  const passwordConfirm = formData.get("password_confirm");

  // 密碼確認驗證
  if (password !== passwordConfirm) {
    showMessage("密碼與確認密碼不符", "error");
    return;
  }

  const registerData = {
    username: formData.get("username"),
    email: formData.get("email"),
    password: password,
    pharmacy_name: formData.get("pharmacy_name"),
    contact_person: formData.get("contact_person"),
    phone: formData.get("phone"),
    mobile: formData.get("mobile"),
    institution_code: formData.get("institution_code"),
    address: formData.get("address"),
  };

  try {
    showLoading();
    const response = await apiRequest("/api/auth/register", {
      method: "POST",
      body: JSON.stringify(registerData),
    });

    if (response.success) {
      showMessage("註冊成功！請登入");
      showLogin();
      document.getElementById("username").value = registerData.username;
    } else {
      throw new Error(response.error || "註冊失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  } finally {
    hideLoading();
  }
}

// 處理個人資料更新
async function handleProfileUpdate(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const profileData = {
    pharmacy_name: formData.get("pharmacy_name"),
    phone: formData.get("phone"),
    mobile: formData.get("mobile"),
    contact_person: formData.get("contact_person"),
    institution_code: formData.get("institution_code"),
    address: formData.get("address"),
  };

  try {
    showLoading();
    const response = await apiRequest("/api/auth/profile", {
      method: "PUT",
      body: JSON.stringify(profileData),
    });

    if (response.success) {
      // 更新本地用戶資料
      if (currentUser && response.data) {
        // 後端回傳結構 { success, data: { ...profile } }
        const profile = response.data;
        Object.assign(currentUser, profile);
        localStorage.setItem("user", JSON.stringify(currentUser));
      }

      showMessage("個人資料更新成功！");
      updateUserInfo(); // 更新顯示的用戶資訊
    } else {
      throw new Error(response.error || "個人資料更新失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  } finally {
    hideLoading();
  }
}

// 載入個人資料到表單
function loadProfileData() {
  if (!currentUser) return;

  const form = document.getElementById("profile-form");
  if (!form) return;

  // 填入用戶資料
  const fields = [
    { id: "profile-username", value: currentUser.username },
    { id: "profile-email-addr", value: currentUser.email },
    { id: "profile-pharmacy-name", value: currentUser.pharmacy_name || "" },
    { id: "profile-phone", value: currentUser.phone || "" },
    { id: "profile-mobile", value: currentUser.mobile || "" },
    { id: "profile-contact-person", value: currentUser.contact_person || "" },
    { id: "profile-institution-code", value: currentUser.institution_code || "" },
    { id: "profile-address", value: currentUser.address || "" },
  ];

  fields.forEach(field => {
    const element = document.getElementById(field.id);
    if (element) {
      element.value = field.value;
    }
  });

  // 設定複選框
  // 通知選項已移除，不再設定 checkbox
}

// 嘗試刷新 token - 增強版
async function attemptTokenRefresh() {
  if (!authToken) {
    console.log('沒有 token，無法刷新');
    return false;
  }

  // 防止重複刷新
  if (window.isRefreshingToken) {
    console.log('正在刷新中，等待結果...');
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (!window.isRefreshingToken) {
          clearInterval(checkInterval);
          resolve(isTokenValid());
        }
      }, 100);
    });
  }

  window.isRefreshingToken = true;

  try {
    console.log("正在刷新 token...");

    // 使用 fetch 直接調用，避免 apiRequest 的循環依賴
    const response = await fetch(`${API_BASE}/api/auth/refresh`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${authToken}`
      },
      body: JSON.stringify({ token: authToken })
    });

    const data = await response.json();

    if (response.ok && data.success && data.data) {
      console.log("Token 刷新成功");
      setAuthToken(data.data.token, data.data.expires_in);

      // 更新用戶資料
      if (data.data.user) {
        currentUser = data.data.user;
        if (data.data.permissions) {
          currentUser.permissions = data.data.permissions;
        }
        localStorage.setItem("user", JSON.stringify(currentUser));
      }

      return true;
    } else {
      console.log("Token 刷新失敗:", data.error || '未知錯誤');
      return false;
    }
  } catch (error) {
    console.log("Token 刷新網路錯誤:", error.message);
    return false;
  } finally {
    window.isRefreshingToken = false;
  }
}

// 取得當前使用者資訊
async function getCurrentUser() {
  try {
    const response = await apiRequest("/api/auth/me");
    if (response.success) {
      currentUser = response.data;
      await getUserPermissions();
      // 更新 localStorage 中的用戶資料
      localStorage.setItem("user", JSON.stringify(currentUser));
      updateUserInfo();
      updateUIBasedOnPermissions();
    } else {
      throw new Error(response.error || "獲取用戶資料失敗");
    }
  } catch (error) {
    console.error("getCurrentUser 錯誤:", error);
    // 重新拋出錯誤，讓調用者處理
    throw error;
  }
}

// 取得使用者權限資訊
async function getUserPermissions() {
  try {
    const response = await apiRequest("/api/admin/user-permissions");
    if (response.success) {
      currentUser.permissions = response.data;
      // 更新 localStorage 中的用戶資料
      localStorage.setItem("user", JSON.stringify(currentUser));
    }
  } catch (error) {
    console.error("獲取用戶權限失敗:", error);
    // 權限獲取失敗不應該影響登入狀態，使用預設權限
    if (!currentUser.permissions) {
      currentUser.permissions = { role_name: 'user', permissions: [] };
    }
  }
}

// 更新使用者資訊顯示
function updateUserInfo() {
  if (currentUser) {
    document.getElementById("user-display-name").textContent =
      currentUser.pharmacy_name || currentUser.username;
    userInfo.style.display = "flex";
  }
}

// 根據權限更新 UI 顯示
function updateUIBasedOnPermissions() {
  if (!currentUser || !currentUser.permissions) {
    return;
  }

  const permissions = currentUser.permissions;

  // 簡化權限檢查：只有管理員和一般用戶兩種，完全依賴role_name
  const isAdmin = permissions && (permissions.role_name === 'admin' || permissions.role_name === 'super_admin');
  const isUser = !isAdmin; // 所有非管理員都是一般用戶

  console.log('用戶角色:', permissions.role_name, isAdmin ? '(管理員)' : '(一般用戶)');

  // 根據角色顯示不同的導航標籤
  updateNavigationTabs(isAdmin, isUser);

  // 更新用戶信息顯示
  updateUserRoleDisplay(permissions, isAdmin);

  // 根據權限調整功能訪問
  updateFeatureAccess(permissions, isAdmin, isUser);

  // 更新管理員標籤內容
  if (isAdmin) {
    updateAdminTabContent(permissions);
  }
}

// 根據角色更新導航標籤顯示 - 簡化版：只有管理員和一般用戶
function updateNavigationTabs(isAdmin, isUser) {
  // 獲取所有導航標籤
  const productTab = document.querySelector('[data-tab="products"]');
  const cartTab = document.querySelector('[data-tab="cart"]');
  const ordersTab = document.querySelector('[data-tab="orders"]');
  const profileTab = document.querySelector('[data-tab="profile"]');
  const adminTab = document.querySelector('[data-tab="admin"]');

  if (isAdmin) {
    // 管理員界面
    if (productTab) {
      productTab.style.display = "block";
      productTab.textContent = "📦 產品管理";
    }
    if (cartTab) cartTab.style.display = "none";  // 管理員不需要購物車
    if (ordersTab) {
      ordersTab.style.display = "block";
      ordersTab.textContent = "📋 訂單管理";
    }
    if (profileTab) profileTab.style.display = "block";
    if (adminTab) {
      adminTab.style.display = "block";
      adminTab.textContent = "⚙️ 系統管理";
    }
  } else {
    // 一般用戶界面
    if (productTab) {
      productTab.style.display = "block";
      productTab.textContent = "🛒 產品採購";
    }
    if (cartTab) {
      cartTab.style.display = "block";
      cartTab.textContent = "🛒 購物車";
    }
    if (ordersTab) {
      ordersTab.style.display = "block";
      ordersTab.textContent = "📋 我的訂單";
    }
    if (profileTab) profileTab.style.display = "block";
    if (adminTab) adminTab.style.display = "none";  // 一般用戶無系統管理權限
  }
}

// 更新用戶角色顯示 - 簡化版
function updateUserRoleDisplay(permissions, isAdmin) {
  const userDisplayName = document.getElementById("user-display-name");
  if (userDisplayName && currentUser) {
    const roleText = isAdmin ? " (管理員)" : " (用戶)";
    userDisplayName.textContent = (currentUser.pharmacy_name || currentUser.username) + roleText;
  }
}

// 更新管理員標籤內容
function updateAdminTabContent(permissions) {
  const isSuperAdmin = permissions.role_name === 'super_admin';
  const isAdmin = permissions.role_name === 'admin' || isSuperAdmin;

  // 角色管理 - 只有超級管理員可以看到
  const rolesTab = document.querySelector('[data-admin-tab="roles"]');
  const rolesContent = document.getElementById("roles-admin");

  if (isSuperAdmin) {
    if (rolesTab) rolesTab.style.display = "block";
    if (rolesContent) rolesContent.style.display = "block";
  } else {
    if (rolesTab) rolesTab.style.display = "none";
    if (rolesContent) rolesContent.style.display = "none";
  }

  // 權限查看 - 管理員和超級管理員都可以看到
  const permissionsTab = document.querySelector('[data-admin-tab="permissions"]');
  if (permissionsTab) permissionsTab.style.display = "block";

  // 用戶權限 - 管理員和超級管理員都可以看到
  const usersTab = document.querySelector('[data-admin-tab="users"]');
  if (usersTab) usersTab.style.display = "block";

  // 訊息管理 - 管理員和超級管理員都可以看到
  const messagesTab = document.querySelector('[data-admin-tab="messages"]');
  if (messagesTab && isAdmin) messagesTab.style.display = "block";

  // 促銷管理 - 管理員和超級管理員都可以看到
  const promotionsTab = document.querySelector('[data-admin-tab="promotions"]');
  if (promotionsTab && isAdmin) promotionsTab.style.display = "block";
}

// 更新功能訪問權限 - 簡化版
function updateFeatureAccess(permissions, isAdmin, isUser) {
  // 更新產品表格標題和功能
  updateProductTableForRole(isAdmin, isUser);

  console.log('用戶權限:', {
    role: permissions.role_name,
    isAdmin: isAdmin,
    permissions: permissions.permissions || []
  });
}

// 根據角色更新產品表格 - 簡化版
function updateProductTableForRole(isAdmin, isUser) {
  const productHeader = document.querySelector('.product-header h3');

  if (productHeader) {
    if (isAdmin) {
      productHeader.textContent = '📦 產品庫存管理';
    } else {
      productHeader.textContent = '🛒 產品採購目錄';
    }
  }
}

// 登出
function logout() {
  clearAuthToken();
  currentUser = null;
  showLogin();
  showMessage("已登出");
}

// 切換標籤
function switchTab(tabName) {
  // 檢查是否已登入
  if (!authToken || isTokenExpired()) {
    showLogin();
    return;
  }

  // 更新導航標籤
  document.querySelectorAll(".nav-tab").forEach((tab) => {
    tab.classList.remove("active");
  });
  document.querySelector(`[data-tab="${tabName}"]`).classList.add("active");

  // 更新內容區域
  document.querySelectorAll(".tab-content").forEach((content) => {
    content.classList.remove("active");
  });
  document.getElementById(`${tabName}-tab`).classList.add("active");

  // 載入對應的資料
  switch (tabName) {
    case "products":
      loadProducts();
      break;
    case "cart":
      loadCart();
      break;
    case "orders":
      loadOrders({});
      break;
    case "profile":
      loadProfileData();
      break;
    case "admin":
      // 延遲載入管理員內容，確保 DOM 已經更新
      setTimeout(() => loadAdminContent(), 50);
      break;
  }
}

// 切換管理員子標籤
function switchAdminTab(tabName) {
  console.log('切換管理員標籤到:', tabName);

  // 更新管理員導航標籤
  document.querySelectorAll(".admin-nav-tab").forEach((tab) => {
    tab.classList.remove("active");
  });

  const targetTab = document.querySelector(`[data-admin-tab="${tabName}"]`);
  console.log('目標標籤:', targetTab);
  if (targetTab) {
    targetTab.classList.add("active");
  }

  // 更新管理員內容區域
  document.querySelectorAll(".admin-tab-content").forEach((content) => {
    content.classList.remove("active");
  });

  const targetContent = document.getElementById(`${tabName}-admin`);
  console.log('目標內容區域:', targetContent);
  if (targetContent) {
    targetContent.classList.add("active");
  }

  // 載入對應的管理員資料
  switch (tabName) {
    case "roles":
      loadRoles();
      break;
    case "permissions":
      loadPermissions();
      break;
    case "users":
      loadUserPermissions();
      break;
    case "messages":
      loadMessages();
      break;
  }
}

// 載入管理員內容
async function loadAdminContent() {
  console.log('載入管理員內容...');
  console.log('當前用戶:', currentUser);

  // 等待一小段時間確保 DOM 已經更新
  await new Promise(resolve => setTimeout(resolve, 100));

  // 確保管理員標籤都是可見的
  const adminTabs = document.querySelectorAll('.admin-nav-tab');
  console.log('找到的管理員標籤數量:', adminTabs.length);

  adminTabs.forEach(tab => {
    console.log('標籤:', tab.dataset.adminTab, '可見性:', tab.style.display);
  });

  // 如果找不到標籤，可能是因為 admin-tab 還沒有顯示
  if (adminTabs.length === 0) {
    console.log('找不到管理員標籤，檢查 admin-tab 是否可見');
    const adminTabContent = document.getElementById('admin-tab');
    console.log('admin-tab 元素:', adminTabContent);
    console.log('admin-tab 是否有 active 類別:', adminTabContent?.classList.contains('active'));
  }

  // 預設載入訊息管理
  if (currentUser && currentUser.permissions) {
    console.log('用戶角色:', currentUser.permissions.role_name);
    if (currentUser.permissions.role_name === 'super_admin') {
      switchAdminTab('roles');
    } else if (currentUser.permissions.role_name === 'admin') {
      switchAdminTab('messages');
    } else {
      switchAdminTab('permissions');
    }
  }
}

// 訊息管理相關函數
let currentMessages = [];

// 載入訊息列表
async function loadMessages(filters = {}) {
  try {
    showLoading();
    const response = await apiRequest("/api/messages");

    if (response.success) {
      currentMessages = response.data || [];
      renderMessagesList();
    } else {
      throw new Error(response.error || "載入訊息失敗");
    }
  } catch (error) {
    console.error("載入訊息錯誤:", error);
    showMessage("載入訊息失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 渲染訊息列表
function renderMessagesList() {
  const container = document.getElementById("messages-list");
  if (!container) return;

  if (currentMessages.length === 0) {
    container.innerHTML = '<div class="no-data">沒有找到訊息</div>';
    return;
  }

  container.innerHTML = currentMessages
    .map(message => `
      <div class="message-item ${message.is_read ? '' : 'unread'}">
        <div class="message-header">
          <h3 class="message-title">${message.title}</h3>
          <div class="message-meta">
            <span class="message-type ${message.message_type}">${getMessageTypeText(message.message_type)}</span>
            <span class="message-status ${message.status}">${message.status === 'active' ? '啟用' : '停用'}</span>
          </div>
        </div>
        <div class="message-content">${message.content}</div>
        <div class="message-footer">
          <div class="message-date">
            <span>發布時間: ${new Date(message.created_at).toLocaleDateString("zh-TW")}</span>
            ${message.is_read ? '<span class="read-indicator">✓ 已讀</span>' : ''}
          </div>
          <div class="message-actions">
            ${!message.is_read ? '<button class="btn btn-primary" onclick="markMessageRead(' + message.id + ')">標為已讀</button>' : ''}
            ${isAdmin() ? `
              <button class="btn btn-secondary" onclick="editMessage(${message.id})">編輯</button>
              <button class="btn btn-danger" onclick="deleteMessage(${message.id})">刪除</button>
            ` : ''}
          </div>
        </div>
      </div>
    `)
    .join("");
}

// 獲取訊息類型文字
function getMessageTypeText(type) {
  const typeMap = {
    'announcement': '一般公告',
    'system': '系統通知',
    'promotion': '促銷訊息'
  };
  return typeMap[type] || type;
}

// 檢查是否為管理員
function isAdmin() {
  return currentUser &&
    currentUser.permissions &&
    (currentUser.permissions.role_name === 'admin' ||
      currentUser.permissions.role_name === 'super_admin');
}

// 發送新訊息
async function createMessage(messageData) {
  try {
    const response = await apiRequest("/api/messages", {
      method: "POST",
      body: JSON.stringify(messageData)
    });

    if (response.success) {
      showMessage("訊息發送成功！");
      loadMessages();
      closeMessageModal();
    } else {
      throw new Error(response.error || "發送訊息失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  }
}

// 編輯訊息
async function editMessage(messageId) {
  const message = currentMessages.find(m => m.id === messageId);
  if (!message) return;

  // 填充表單
  document.getElementById("message-id").value = message.id;
  document.getElementById("message-title").value = message.title;
  document.getElementById("message-content").value = message.content;
  document.getElementById("message-type").value = message.message_type;
  document.getElementById("message-audience").value = message.target_audience;
  document.getElementById("message-priority").value = message.priority || 1;
  document.getElementById("message-active").checked = message.status === 'active';

  if (message.starts_at) {
    document.getElementById("message-start-date").value = message.starts_at.slice(0, 16);
  }
  if (message.ends_at) {
    document.getElementById("message-end-date").value = message.ends_at.slice(0, 16);
  }

  // 顯示模態框
  const modal = document.getElementById("message-modal");
  modal.style.display = "flex";
}

// 更新訊息
async function updateMessage(messageId, messageData) {
  try {
    const response = await apiRequest(`/api/messages/${messageId}`, {
      method: "PUT",
      body: JSON.stringify(messageData)
    });

    if (response.success) {
      showMessage("訊息更新成功！");
      loadMessages();
      closeMessageModal();
    } else {
      throw new Error(response.error || "更新訊息失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  }
}

// 刪除訊息
async function deleteMessage(messageId) {
  if (!confirm("確定要刪除這則訊息嗎？")) return;

  try {
    const response = await apiRequest(`/api/messages/${messageId}`, {
      method: "DELETE"
    });

    if (response.success) {
      showMessage("訊息已刪除");
      loadMessages();
    } else {
      throw new Error(response.error || "刪除訊息失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  }
}

// 標記訊息為已讀
async function markMessageRead(messageId) {
  try {
    const response = await apiRequest(`/api/messages/${messageId}/read`, {
      method: "POST"
    });

    if (response.success) {
      const message = currentMessages.find(m => m.id === messageId);
      if (message) {
        message.is_read = true;
        renderMessagesList();
      }
    } else {
      throw new Error(response.error || "標記已讀失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  }
}

// 關閉訊息模態框
function closeMessageModal() {
  const modal = document.getElementById("message-modal");
  modal.style.display = "none";

  // 清空表單
  document.getElementById("message-form").reset();
  document.getElementById("message-id").value = "";
}

// 設置訊息模態框事件
function setupMessageModalEvents() {
  const modal = document.getElementById("message-modal");
  const closeBtn = document.getElementById("close-message-modal");
  const cancelBtn = document.getElementById("cancel-message-edit");
  const form = document.getElementById("message-form");

  // 關閉按鈕事件
  closeBtn.onclick = closeMessageModal;
  cancelBtn.onclick = closeMessageModal;

  // 點擊模態框外部關閉
  modal.onclick = function (event) {
    if (event.target === modal) {
      closeMessageModal();
    }
  };

  // 表單提交事件
  form.onsubmit = async function (e) {
    e.preventDefault();

    const messageId = document.getElementById("message-id").value;
    const messageData = {
      title: document.getElementById("message-title").value,
      content: document.getElementById("message-content").value,
      message_type: document.getElementById("message-type").value,
      target_audience: document.getElementById("message-audience").value,
      priority: parseInt(document.getElementById("message-priority").value),
      status: document.getElementById("message-active").checked ? 'active' : 'inactive',
      starts_at: document.getElementById("message-start-date").value || null,
      ends_at: document.getElementById("message-end-date").value || null
    };

    if (messageId) {
      await updateMessage(messageId, messageData);
    } else {
      await createMessage(messageData);
    }
  };
}

// 初始化訊息管理功能
document.addEventListener("DOMContentLoaded", function () {
  // 綁定創建訊息按鈕事件
  const createMessageBtn = document.getElementById("create-message-btn");
  if (createMessageBtn) {
    createMessageBtn.addEventListener("click", function () {
      const modal = document.getElementById("message-modal");
      modal.style.display = "flex";
    });
  }

  // 設置模態框事件
  setupMessageModalEvents();
});



// 載入角色列表
async function loadRoles() {
  if (!currentUser || !currentUser.permissions || currentUser.permissions.role_name !== 'super_admin') {
    showMessage("您沒有權限查看角色管理", "error");
    return;
  }

  try {
    showLoading();
    const response = await apiRequest("/api/admin/roles");

    if (response.success) {
      const roles = response.data || [];
      renderRolesTable(roles);
    } else {
      throw new Error(response.error || "載入角色失敗");
    }
  } catch (error) {
    console.error("載入角色錯誤:", error);
    showMessage("載入角色失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 渲染角色表格
function renderRolesTable(roles) {
  const tableBody = document.getElementById("roles-table-body");
  if (!tableBody) return;

  if (roles.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="4" class="no-data">沒有找到角色</td></tr>';
    return;
  }

  tableBody.innerHTML = roles.map(role => `
    <tr>
      <td>${role.id}</td>
      <td>${role.name}</td>
      <td>${role.description || 'N/A'}</td>
      <td>
        <button class="btn btn-secondary" onclick="editRole(${role.id})">編輯</button>
        ${role.name !== 'super_admin' ? `<button class="btn btn-danger" onclick="deleteRole(${role.id})">刪除</button>` : ''}
      </td>
    </tr>
  `).join('');
}

// 載入權限列表
async function loadPermissions() {
  try {
    showLoading();
    const response = await apiRequest("/api/admin/permissions");

    if (response.success) {
      const permissions = response.data || [];
      renderPermissionsList(permissions);
    } else {
      throw new Error(response.error || "載入權限失敗");
    }
  } catch (error) {
    console.error("載入權限錯誤:", error);
    showMessage("載入權限失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 渲染權限列表
function renderPermissionsList(permissions) {
  const container = document.getElementById("permissions-list");
  if (!container) return;

  if (permissions.length === 0) {
    container.innerHTML = '<p class="no-data">沒有找到權限</p>';
    return;
  }

  // 按資源分組權限
  const groupedPermissions = permissions.reduce((groups, permission) => {
    const resource = permission.resource;
    if (!groups[resource]) {
      groups[resource] = [];
    }
    groups[resource].push(permission);
    return groups;
  }, {});

  container.innerHTML = Object.entries(groupedPermissions).map(([resource, perms]) => `
    <div class="permission-group">
      <h4>${resource} 權限</h4>
      <div class="permission-items">
        ${perms.map(perm => `
          <div class="permission-item">
            <strong>${perm.name}</strong>
            <span>${perm.description || 'N/A'}</span>
          </div>
        `).join('')}
      </div>
    </div>
  `).join('');
}

// 載入用戶權限
async function loadUserPermissions() {
  // 這裡可以實現用戶權限管理功能
  const container = document.getElementById("user-permissions-list");
  if (container) {
    container.innerHTML = '<p>用戶權限管理功能開發中...</p>';
  }
}

// 產品管理相關函數
let currentProducts = [];
let currentPage = 1;
let totalPages = 1;
const pageSize = 10;

// 從產品資料中獲取劑型
function getDosageForm(product) {
  // 如果已經有 dosage_form 值，直接返回
  if (product.dosage_form && product.dosage_form.trim() !== "") {
    return product.dosage_form;
  }

  // 根據產品名稱推測劑型
  const name = product.name || "";
  if (name.includes("錠") || name.toLowerCase().includes("tablet")) {
    return "錠劑";
  } else if (name.includes("膠囊") || name.toLowerCase().includes("capsule")) {
    return "膠囊";
  } else if (name.includes("糖漿") || name.toLowerCase().includes("syrup")) {
    return "糖漿";
  } else if (name.includes("注射") || name.toLowerCase().includes("injection")) {
    return "注射劑";
  } else if (name.includes("軟膏") || name.toLowerCase().includes("ointment")) {
    return "軟膏";
  } else if (name.includes("滴劑") || name.toLowerCase().includes("drop")) {
    return "滴劑";
  } else if (name.includes("粉") || name.toLowerCase().includes("powder")) {
    return "散劑";
  } else if (name.includes("液") || name.toLowerCase().includes("solution")) {
    return "溶液";
  }

  // 預設返回錠劑
  return "錠劑";
}

// 從產品資料中獲取成分含量
function getIngredients(product) {
  // 如果已經有 ingredients 值，直接返回
  if (product.ingredients && product.ingredients.trim() !== "") {
    return product.ingredients;
  }

  // 根據產品名稱推測成分含量
  const name = product.name || "";
  const lowerName = name.toLowerCase();

  // 常見藥品成分推測
  if (name.includes("普拿疼") || lowerName.includes("paracetamol")) {
    return "Paracetamol 500mg";
  } else if (name.includes("阿斯匹靈") || name.includes("阿司匹林") || lowerName.includes("aspirin")) {
    return "Acetylsalicylic Acid 100mg";
  } else if (name.includes("布洛芬") || lowerName.includes("ibuprofen")) {
    return "Ibuprofen 400mg";
  } else if (name.includes("安莫西林") || lowerName.includes("amoxicillin")) {
    return "Amoxicillin 250mg";
  } else if (name.includes("歐美拉唑") || lowerName.includes("omeprazole")) {
    return "Omeprazole 20mg";
  } else if (name.includes("二甲雙胍") || lowerName.includes("metformin")) {
    return "Metformin 500mg";
  } else if (name.includes("立普妥") || lowerName.includes("atorvastatin")) {
    return "Atorvastatin 10mg";
  } else if (name.includes("洛薩坦") || lowerName.includes("losartan")) {
    return "Losartan 50mg";
  } else if (name.includes("安普羅") || lowerName.includes("amlodipine")) {
    return "Amlodipine 5mg";
  }

  // 試著從產品名稱中提取劑量
  const dosageMatch = name.match(/(\d+)\s*mg/i);
  if (dosageMatch) {
    const dosage = dosageMatch[1];
    const nameWithoutDosage = name.replace(/\d+\s*mg/gi, "").trim();
    const firstWord = nameWithoutDosage.split(/\s+/)[0];
    return `${firstWord} ${dosage}mg`;
  }

  // 預設返回主要成分
  return "主要成分 100mg";
}

// 載入產品列表
async function loadProducts(page = 1) {
  try {
    showLoading();
    console.log(`載入第 ${page} 頁產品，每頁 ${pageSize} 筆`);

    const params = new URLSearchParams();
    params.append("page", page.toString());
    params.append("limit", pageSize.toString());

    const url = `/api/products?${params}`;
    console.log(`API請求URL: ${url}`);

    const response = await apiRequest(url);

    if (response.success) {
      currentProducts = response.data || [];
      currentPage = page;

      console.log(`載入了 ${currentProducts.length} 筆產品資料`);

      // 更新總頁數估算
      if (currentProducts.length < pageSize && page === 1) {
        totalPages = 1;
      } else if (currentProducts.length === pageSize) {
        // 如果當前頁滿了，估計還有下一頁
        totalPages = Math.max(page + 1, totalPages);
      } else if (currentProducts.length < pageSize) {
        // 如果當前頁未滿，則當前頁是最後一頁
        totalPages = page;
      }

      renderProductsTable();
      updatePaginationInfo();
      updatePaginationControls();
    } else {
      console.error("API響應錯誤:", response);
      throw new Error(response.error || "載入產品失敗");
    }
  } catch (error) {
    console.error("載入產品錯誤:", error);
    showMessage("載入產品失敗: " + error.message, "error");

    const gridBody = document.getElementById("products-grid-body");
    if (gridBody) {
      gridBody.innerHTML = '<div class="no-data" style="text-align: center; padding: 40px; color: #999;">載入產品失敗</div>';
    }
  } finally {
    hideLoading();
  }
}

// 渲染產品網格 - 新的卡片式佈局
function renderProductsTable() {
  const gridBody = document.getElementById("products-grid-body");
  if (!gridBody) return;

  if (currentProducts.length === 0) {
    gridBody.innerHTML = '<div class="no-data" style="text-align: center; padding: 40px; color: #999;">沒有找到產品</div>';
    return;
  }

  // 判斷是否為管理員
  const isAdmin = currentUser &&
    currentUser.permissions &&
    (currentUser.permissions.role_name === 'admin' ||
      currentUser.permissions.role_name === 'super_admin');

  gridBody.innerHTML = currentProducts
    .map((product) => createProductCard(product, isAdmin))
    .join("");
}

// 創建產品卡片
function createProductCard(product, isAdmin) {
  const status = getProductStatus(product);
  const nhiLevel = getNhiLevel(product);

  return `
    <div class="product-card">
      <!-- 健保資訊欄 -->
      <div class="nhi-info">
        <div class="nhi-details">
          <div class="nhi-code">${product.nhi_code || "N/A"}</div>
          <div class="nhi-level">健保價: ${product.nhi_price || "N/A"}</div>
          <div class="nhi-expiry">2027.04.30</div>
        </div>
      </div>
      
      <!-- 品名/成分欄 -->
      <div class="product-name">
        <div class="product-title">${product.name || "N/A"}</div>
        <div class="product-ingredients">${getIngredients(product)}</div>
      </div>
      
      <!-- 單價欄 -->
      <div class="product-price">
        <div class="price-value">${product.nhi_price || product.unit_price || "0"}</div>
        <div class="price-unit">/元</div>
      </div>
      
      <!-- 數量欄 -->
      <div class="product-quantity">
        ${getQuantityInput(product, isAdmin)}
        <div class="quantity-label">${isAdmin ? '庫存' : '訂量'}</div>
      </div>
      
      <!-- 功能欄 -->
      <div class="product-actions">
        <button class="action-btn cart" title="加入購物車" onclick="addToCart(${product.id}, this)">🛒</button>
        <button class="action-btn favorite" title="收藏" onclick="toggleFavorite(${product.id})">❤️</button>
      </div>
      
      <!-- 狀態欄 -->
      <div class="product-status">
        <span class="status-badge ${status.class}">${status.text}</span>
      </div>
      
      <!-- 保存欄 -->
      <div class="product-save">
        ${getSaveButton(product, isAdmin)}
      </div>
    </div>
  `;
}

// 獲取產品狀態
function getProductStatus(product) {
  const stock = product.stock_quantity || 0;
  if (stock > 10) {
    return { class: 'in-stock', text: '供貨中' };
  } else if (stock > 0) {
    return { class: 'supply-interrupted', text: '供貨中' };
  } else {
    return { class: 'out-of-stock', text: '缺貨' };
  }
}

// 獲取健保層級
function getNhiLevel(product) {
  // 根據健保代碼或其他條件判斷層級
  if (product.nhi_code) {
    return Math.floor(Math.random() * 3) + 1; // 暫時隨機生成 1-3
  }
  return "N/A";
}

// 獲取數量輸入框
function getQuantityInput(product, isAdmin) {
  if (isAdmin) {
    return `<input type="number" class="quantity-input" min="0" value="${product.stock_quantity || 0}" onchange="updateStock(${product.id}, this.value)">`;
  } else {
    return `<input type="number" class="quantity-input" min="1" max="${product.stock_quantity || 0}" value="1" ${(product.stock_quantity || 0) <= 0 ? "disabled" : ""}>`;
  }
}

// 獲取保存按鈕
function getSaveButton(product, isAdmin) {
  if (isAdmin) {
    return `<button class="save-btn" onclick="editProduct(${product.id})">編輯</button>`;
  } else {
    return `<button class="save-btn" onclick="toggleFavorite(${product.id})">常選</button>`;
  }
}

// 根據角色生成操作欄位
function getActionColumn(product, isAdmin) {
  if (isAdmin) {
    // 管理員看到庫存管理功能
    return `
      <td>
        <input type="number" 
               class="quantity-input"
               min="0" 
               value="${product.stock_quantity || 0}" 
               style="width: 60px;"
               onchange="updateStock(${product.id}, this.value)">
      </td>
      <td>
        <button class="btn btn-primary btn-sm" 
                onclick="editProduct(${product.id})"
                title="編輯產品">
            ✏️ 編輯
        </button>
      </td>
    `;
  } else {
    // 一般用戶看到購物車功能
    return `
      <td>
        <input type="number" 
               class="quantity-input"
               min="1" 
               max="${product.stock_quantity || 0}"
               value="1" 
               style="width: 60px;"
               ${(product.stock_quantity || 0) <= 0 ? "disabled" : ""}>
      </td>
      <td>
        <button class="btn-cart-icon" 
                onclick="addToCart(${product.id}, this)"
                ${(product.stock_quantity || 0) <= 0 ? "disabled" : ""}
                title="加入購物車">
            🛒 加入購物車
        </button>
      </td>
    `;
  }
}

// 管理員專用函數 - 更新庫存
async function updateStock(productId, newStock) {
  try {
    const response = await apiRequest(`/api/products/${productId}/stock`, {
      method: 'PUT',
      body: JSON.stringify({ stock_quantity: parseInt(newStock) })
    });

    if (response.success) {
      showMessage('庫存更新成功', 'success');
      // 更新本地數據
      const product = currentProducts.find(p => p.id === productId);
      if (product) {
        product.stock_quantity = parseInt(newStock);
      }
    } else {
      showMessage('庫存更新失敗: ' + response.error, 'error');
    }
  } catch (error) {
    showMessage('庫存更新失敗: ' + error.message, 'error');
  }
}

// 管理員專用函數 - 編輯產品
function editProduct(productId) {
  const product = currentProducts.find(p => p.id === productId);
  if (product) {
    showMessage('編輯產品功能開發中: ' + product.name, 'info');
    // TODO: 實現產品編輯功能
  }
}

// 新增功能函數
function downloadProduct(productId) {
  const product = currentProducts.find(p => p.id === productId);
  if (product) {
    showMessage('下載產品資訊: ' + product.name, 'info');
    // TODO: 實現產品資訊下載功能
  }
}

function toggleFavorite(productId) {
  const product = currentProducts.find(p => p.id === productId);
  if (product) {
    showMessage('收藏功能: ' + product.name, 'info');
    // TODO: 實現收藏功能
  }
}

function refreshProduct(productId) {
  const product = currentProducts.find(p => p.id === productId);
  if (product) {
    showMessage('重新整理產品: ' + product.name, 'info');
    // TODO: 實現單一產品重新整理功能
    loadProducts(); // 暫時重新載入所有產品
  }
}

// 更新分頁資訊顯示
function updatePaginationInfo() {
  const paginationInfo = document.getElementById("pagination-info");
  if (paginationInfo) {
    const start = (currentPage - 1) * pageSize + 1;
    const end = (currentPage - 1) * pageSize + currentProducts.length;
    const totalText = currentProducts.length < pageSize && currentPage === 1
      ? `${currentProducts.length} 筆`
      : `至少 ${end} 筆`;
    paginationInfo.textContent = `顯示第 ${start}-${end} 筆，共 ${totalText} 資料`;
  }
}

// 更新分頁控制按鈕
function updatePaginationControls() {
  const prevBtn = document.getElementById("prev-page");
  const nextBtn = document.getElementById("next-page");
  const pageInput = document.getElementById("page-input");

  if (prevBtn) {
    prevBtn.disabled = currentPage <= 1;
    prevBtn.style.opacity = currentPage <= 1 ? "0.5" : "1";
  }

  if (nextBtn) {
    const hasNextPage = currentProducts.length === pageSize;
    nextBtn.disabled = !hasNextPage;
    nextBtn.style.opacity = !hasNextPage ? "0.5" : "1";
  }

  if (pageInput) {
    pageInput.value = currentPage;
  }
}

// 切換頁面
async function changePage(newPage) {
  if (newPage < 1) return;
  if (newPage === currentPage) return;
  if (newPage > currentPage && currentProducts.length < pageSize) return; // 沒有下一頁

  await loadProducts(newPage);
}

// 前往指定頁面
async function gotoPage() {
  const pageInput = document.getElementById("page-input");
  const targetPage = parseInt(pageInput.value);

  if (isNaN(targetPage) || targetPage < 1) {
    showMessage("請輸入有效的頁數", "error");
    pageInput.value = currentPage;
    return;
  }

  await loadProducts(targetPage);
}

// 搜尋產品
async function searchProducts() {
  const searchTerm = document.getElementById("product-search").value.trim();

  try {
    showLoading();
    const params = new URLSearchParams();
    params.append("page", "1");
    params.append("limit", pageSize.toString());
    if (searchTerm) params.append("search", searchTerm);

    const response = await apiRequest(`/api/products?${params}`);

    if (response.success) {
      currentProducts = response.data || [];
      currentPage = 1;

      // 重設分頁狀態
      if (currentProducts.length === pageSize) {
        totalPages = 2; // 假設至少有下一頁
      } else {
        totalPages = 1;
      }

      renderProductsTable();
      updatePaginationInfo();
      updatePaginationControls();
    } else {
      throw new Error(response.error || "搜尋失敗");
    }
  } catch (error) {
    showMessage("搜尋失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 清除搜尋
async function clearSearch() {
  document.getElementById("product-search").value = "";
  currentPage = 1;
  await loadProducts(1);
}

// 加入購物車
async function addToCart(productId, button) {
  if (!authToken) {
    showMessage("請先登入才能加入購物車", "error");
    showLogin();
    return;
  }

  const container = button.closest('.product-card') || button.closest('tr');
  const quantityInput = container ? container.querySelector('.quantity-input') : null;

  if (!quantityInput) {
    showMessage("無法找到數量輸入框", "error");
    return;
  }

  const quantity = parseInt(quantityInput.value) || 1;

  try {
    showLoading();
    const response = await apiRequest("/api/cart", {
      method: "POST",
      body: JSON.stringify({
        product_id: productId,
        quantity: quantity,
      }),
    });

    if (response.success) {
      showMessage("✅ 已加入購物車！");
      quantityInput.value = 1; // 重置數量
    } else {
      throw new Error(response.error || "加入購物車失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  } finally {
    hideLoading();
  }
}

// 載入購物車
async function loadCart() {
  // 檢查是否已登入
  if (!authToken || isTokenExpired()) {
    showLogin();
    return;
  }

  try {
    const response = await apiRequest("/api/cart");

    if (response.success) {
      const cart = response.data.cart;
      const cartContainer = document.getElementById("cart-items");
      const totalElement = document.getElementById("cart-total");

      if (cart && cart.items && cart.items.length > 0) {
        cartContainer.innerHTML = cart.items
          .map(
            (item) => `
                    <div class="cart-item">
                        <div class="cart-item-info">
                            <div class="cart-item-name">${item.product_name
              }</div>
                            <div class="cart-item-price">NT$ ${parseFloat(
                item.item.unit_price
              ).toFixed(2)} x ${item.item.quantity
              } = NT$ ${Math.round(parseFloat(item.item.subtotal))}</div>
                        </div>
                        <div class="cart-item-actions">
                            <button class="btn btn-secondary" onclick="updateCartItem(${item.item.id
              }, ${item.item.quantity - 1})" ${item.item.quantity <= 1 ? "disabled" : ""
              }>-</button>
                            <input type="number" class="quantity-input-cart" value="${item.item.quantity
              }" min="1" max="${item.available_stock
              }" onchange="updateCartItemFromInput(${item.item.id}, this.value)">
                            <button class="btn btn-secondary" onclick="updateCartItem(${item.item.id
              }, ${item.item.quantity + 1})" ${item.available_stock > item.item.quantity ? "" : "disabled"
              }>+</button>
                            <button class="btn btn-primary" onclick="updateCartItemByButton(${item.item.id}, this)">更新</button>
                            <button class="btn btn-danger" onclick="removeCartItem(${item.item.id
              })">移除</button>
                        </div>
                    </div>
                `
          )
          .join("");

        totalElement.textContent = Math.round(
          parseFloat(cart.total_amount || 0)
        );
      } else {
        cartContainer.innerHTML = "<p>購物車是空的</p>";
        totalElement.textContent = "0";
      }
    } else {
      const cartContainer = document.getElementById("cart-items");
      const totalElement = document.getElementById("cart-total");
      cartContainer.innerHTML = "<p>購物車是空的</p>";
      totalElement.textContent = "0";
    }
  } catch (error) {
    console.error("載入購物車錯誤:", error);
    showMessage("載入購物車失敗: " + error.message, "error");
  }
}

// 更新購物車項目
async function updateCartItem(itemId, newQuantity) {
  if (newQuantity <= 0) {
    removeCartItem(itemId);
    return;
  }

  try {
    showLoading();
    const response = await apiRequest(`/api/cart/items/${itemId}`, {
      method: "PUT",
      body: JSON.stringify({ quantity: newQuantity }),
    });

    if (response.success) {
      showMessage("✅ 數量已更新！");
      loadCart();
    } else {
      throw new Error(response.error || "更新失敗");
    }
  } catch (error) {
    console.error("更新購物車錯誤:", error);
    showMessage("更新購物車失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 從輸入框更新購物車項目數量
async function updateCartItemFromInput(itemId, newQuantity) {
  const quantity = parseInt(newQuantity);
  if (isNaN(quantity) || quantity <= 0) {
    showMessage("請輸入有效的數量", "error");
    loadCart();
    return;
  }

  await updateCartItem(itemId, quantity);
}

// 從「更新」按鈕觸發，使用目前輸入框的數量
function updateCartItemByButton(itemId, button) {
  const container = button.closest('.cart-item');
  const input = container ? container.querySelector('.quantity-input-cart') : null;
  if (!input) {
    showMessage('找不到數量輸入框', 'error');
    return;
  }
  const quantity = parseInt(input.value);
  if (isNaN(quantity) || quantity <= 0) {
    showMessage('請輸入有效的數量', 'error');
    return;
  }
  updateCartItem(itemId, quantity);
}

// 移除購物車項目
async function removeCartItem(itemId) {
  try {
    await apiRequest(`/api/cart/items/${itemId}`, {
      method: "DELETE",
    });

    showMessage("已移除商品");
    loadCart();
  } catch (error) {
    showMessage("移除商品失敗: " + error.message, "error");
  }
}

// 結帳
async function checkout() {
  try {
    showLoading();

    const notes = document.getElementById("order-notes").value.trim();

    const response = await apiRequest("/api/orders/cart", {
      method: "POST",
      body: JSON.stringify({
        notes: notes || null,
      }),
    });

    if (response.success) {
      showMessage("訂單建立成功！");
      document.getElementById("order-notes").value = "";
      loadCart();
      loadOrders({});
      switchTab("orders");
    } else {
      throw new Error(response.error || "結帳失敗");
    }
  } catch (error) {
    showMessage(error.message, "error");
  } finally {
    hideLoading();
  }
}

// 清空購物車
async function clearCart() {
  if (!confirm("確定要清空購物車嗎？")) {
    return;
  }

  try {
    await apiRequest("/api/cart/clear", {
      method: "DELETE",
    });

    showMessage("購物車已清空");
    loadCart();
  } catch (error) {
    showMessage("清空購物車失敗: " + error.message, "error");
  }
}

// 載入訂單 - 根據用戶角色載入不同的訂單資料
async function loadOrders(filters = {}) {
  // 檢查是否已登入
  if (!authToken || isTokenExpired()) {
    showLogin();
    return;
  }

  // 判斷是否為管理員
  console.log('loadOrders - currentUser:', currentUser);
  const isAdmin = currentUser &&
    currentUser.permissions &&
    (currentUser.permissions.role_name === 'admin' ||
      currentUser.permissions.role_name === 'super_admin');
  console.log('loadOrders - isAdmin:', isAdmin);

  // 管理員預設篩選前5天的訂單
  if (isAdmin && Object.keys(filters).length === 0) {
    const today = new Date();
    const fiveDaysAgo = new Date(today);
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

    filters = {
      start_date: fiveDaysAgo.toISOString().split('T')[0] + 'T00:00:00Z',
      end_date: today.toISOString().split('T')[0] + 'T23:59:59Z'
    };

    console.log('管理員預設日期篩選:', filters);
  }

  // 更新界面標題和控制項
  updateOrdersInterface(isAdmin);

  try {
    showLoading();

    // 構建查詢參數
    const queryParams = new URLSearchParams();
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.start_date) queryParams.append('start_date', filters.start_date);
    if (filters.end_date) queryParams.append('end_date', filters.end_date);

    // 管理員查看所有訂單，一般用戶只查看自己的訂單
    const apiEndpoint = isAdmin ? "/api/orders/all" : "/api/orders";
    const url = queryParams.toString() ? `${apiEndpoint}?${queryParams}` : apiEndpoint;
    const response = await apiRequest(url);

    if (response.success) {
      const orders = response.data?.orders || [];
      const ordersContainer = document.getElementById("orders-list");

      console.log("載入訂單資料:", orders);
      console.log("訂單容器元素:", ordersContainer);

      // 如果是管理員，顯示統計信息
      if (isAdmin) {
        updateOrderStats(orders);
      }

      if (orders.length > 0) {
        ordersContainer.innerHTML = orders
          .map((order) => renderOrderItem(order, isAdmin))
          .join("");
      } else {
        ordersContainer.innerHTML = "<p>沒有訂單記錄</p>";
      }
    } else {
      throw new Error(response.error || "載入訂單失敗");
    }
  } catch (error) {
    console.error("載入訂單錯誤:", error);
    showMessage("載入訂單失敗: " + error.message, "error");

    const ordersContainer = document.getElementById("orders-list");
    if (ordersContainer) {
      ordersContainer.innerHTML = "<p>載入訂單失敗</p>";
    }
  } finally {
    hideLoading();
  }
}

// 更新訂單界面（管理員 vs 一般用戶）
function updateOrdersInterface(isAdmin) {
  const ordersTitle = document.getElementById("orders-title");
  const adminControls = document.getElementById("admin-order-controls");
  const batchOperations = document.getElementById("batch-operations");

  if (isAdmin) {
    ordersTitle.textContent = "訂單管理（管理員）";
    adminControls.style.display = "block";
    batchOperations.style.display = "block";

    // 綁定篩選事件（只綁定一次）
    if (!adminControls.dataset.bound) {
      setupAdminOrderFilters();
      setupBatchOperations();
      adminControls.dataset.bound = "true";
    }
  } else {
    ordersTitle.textContent = "訂單歷史";
    adminControls.style.display = "none";
    batchOperations.style.display = "none";
  }
}

// 設置管理員訂單篩選功能
function setupAdminOrderFilters() {
  const applyFiltersBtn = document.getElementById("apply-filters");
  const clearFiltersBtn = document.getElementById("clear-filters");

  applyFiltersBtn.addEventListener("click", () => {
    const filters = {
      status: document.getElementById("status-filter").value
    };

    // 移除空值
    Object.keys(filters).forEach(key => {
      if (!filters[key]) delete filters[key];
    });

    loadOrders(filters);
  });

  clearFiltersBtn.addEventListener("click", () => {
    document.getElementById("status-filter").value = "";
    loadOrders({}); // 重新載入預設篩選（今天和昨天）
  });
}

// 更新訂單統計信息
function updateOrderStats(orders) {
  const statsContainer = document.getElementById("order-stats");

  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === '待處理').length,
    processing: orders.filter(o => o.status === '揀貨中').length,
    completed: orders.filter(o => o.status === '已出貨').length
  };

  const totalAmount = orders.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0);

  statsContainer.innerHTML = `
    <div class="stat-item">
      <span class="stat-number">${stats.total}</span>
      <span class="stat-label">總訂單</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">${stats.pending}</span>
      <span class="stat-label">待處理</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">${stats.processing}</span>
      <span class="stat-label">揀貨中</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">${stats.completed}</span>
      <span class="stat-label">已出貨</span>
    </div>
    <div class="stat-item">
      <span class="stat-number">NT$ ${Math.round(totalAmount)}</span>
      <span class="stat-label">總金額</span>
    </div>
  `;
}

// 渲染單個訂單項目
function renderOrderItem(order, isAdmin) {
  // 狀態樣式映射 (直接使用後端返回的中文狀態)
  const statusClassMap = {
    "待處理": "status-pending",
    "揀貨中": "status-processing",
    "已出貨": "status-shipped"
  };

  const statusInfo = {
    text: order.status,
    class: statusClassMap[order.status] || "status-pending"
  };

  const checkbox = isAdmin ? `<input type="checkbox" class="order-checkbox" data-order-id="${order.id}">` : '';

  const adminActions = isAdmin ? `
    <div class="order-actions">
      <button class="btn btn-status-pending" onclick="changeOrderStatus(${order.id}, '待處理')">待處理</button>
      <button class="btn btn-status-processing" onclick="changeOrderStatus(${order.id}, '揀貨中')">揀貨中</button>
      <button class="btn btn-status-shipped" onclick="changeOrderStatus(${order.id}, '已出貨')">已出貨</button>
    </div>
  ` : '';

  return `
    <div class="order-item ${isAdmin ? 'admin-order-item' : ''}" data-order-id="${order.id}">
      <div class="order-header">
        ${checkbox}
        <div class="order-number">訂單編號: 
          <a href="#" class="order-number-link" onclick="showOrderDetail(${order.id}); return false;">
            ${order.order_number}
          </a>
        </div>
        <div class="order-status">
          <span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>
        </div>
        <div class="order-total">NT$ ${Math.round(parseFloat(order.total_amount))}</div>
      </div>
      ${isAdmin && (order.pharmacy_name || order.username) ?
      `<div class="order-user">
          <strong>客戶：</strong>
          ${order.pharmacy_name || order.username}
          ${order.username && order.pharmacy_name !== order.username ? ` (${order.username})` : ''}
        </div>` : ''
    }
      <div class="order-date">下單時間: ${new Date(order.created_at).toLocaleDateString("zh-TW")}</div>
      ${order.notes ? `<div class="order-notes">備註: ${order.notes}</div>` : ""}
      ${adminActions}
    </div>
  `;
}

// 管理員更改訂單狀態
async function changeOrderStatus(orderId, newStatus) {
  try {
    const response = await apiRequest(`/api/orders/${orderId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status: newStatus })
    });

    if (response.success) {
      const statusText = getStatusText(newStatus);
      showMessage(`訂單狀態已更新為：${statusText}`, "success");
      loadOrders({}); // 重新載入訂單列表
    } else {
      throw new Error(response.error || "更新訂單狀態失敗");
    }
  } catch (error) {
    console.error("更新訂單狀態錯誤:", error);
    showMessage("更新訂單狀態失敗: " + error.message, "error");
  }
}

// 顯示訂單詳情
async function showOrderDetail(orderId) {
  if (!authToken || isTokenExpired()) {
    showLogin();
    return;
  }

  try {
    showLoading();
    const response = await apiRequest(`/api/orders/${orderId}`);

    if (response.success && response.data.order) {
      const orderDetail = response.data.order;
      displayOrderDetailModal(orderDetail);
    } else {
      throw new Error(response.message || "載入訂單詳情失敗");
    }
  } catch (error) {
    console.error("載入訂單詳情錯誤:", error);
    showMessage("載入訂單詳情失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}

// 顯示訂單詳情模態框
function displayOrderDetailModal(orderDetail) {
  const modal = document.getElementById("order-detail-modal");
  const content = document.getElementById("order-detail-content");
  const title = document.getElementById("order-detail-title");

  if (!modal || !content || !title) {
    console.error("找不到訂單詳情模態框元素");
    return;
  }

  // 設置標題
  title.textContent = `訂單詳情 - ${orderDetail.order.order_number}`;

  // 直接使用後端返回的中文狀態
  const statusText = orderDetail.order.status;
  const statusClassMap = {
    "待處理": "pending",
    "揀貨中": "processing",
    "已出貨": "shipped"
  };
  const statusClass = statusClassMap[orderDetail.order.status] || "pending";

  // 生成訂單詳情HTML
  content.innerHTML = `
    <div class="order-detail-header">
      <div class="order-detail-info">
        <div>
          <label>訂單編號:</label>
          <span>${orderDetail.order.order_number}</span>
        </div>
        <div>
          <label>訂單狀態:</label>
          <span class="order-status-badge ${statusClass}">${statusText}</span>
        </div>
        <div>
          <label>下單時間:</label>
          <span>${new Date(orderDetail.order.created_at).toLocaleDateString("zh-TW")}</span>
        </div>
        <div>
          <label>更新時間:</label>
          <span>${new Date(orderDetail.order.updated_at).toLocaleDateString("zh-TW")}</span>
        </div>
        ${orderDetail.order.notes ? `
        <div style="grid-column: 1 / -1;">
          <label>訂單備註:</label>
          <span>${orderDetail.order.notes}</span>
        </div>
        ` : ''}
      </div>
    </div>

    <div class="order-items-section">
      <h4>購買明細</h4>
      <table class="order-items-table">
        <thead>
          <tr>
            <th>藥品代碼</th>
            <th>產品名稱</th>
            <th>單價</th>
            <th>數量</th>
            <th>小計</th>
          </tr>
        </thead>
        <tbody>
          ${orderDetail.items.map(itemDetail => `
            <tr>
              <td class="product-code">${itemDetail.product_nhi_code || 'N/A'}</td>
              <td>${itemDetail.product_name}</td>
              <td class="price">NT$ ${parseFloat(itemDetail.item.unit_price).toFixed(2)}</td>
              <td class="quantity">${itemDetail.item.quantity}</td>
              <td class="price">NT$ ${Math.round(parseFloat(itemDetail.item.subtotal))}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>

    <div class="order-total-summary">
      <div>總項目數: ${orderDetail.items.length} 項</div>
      <div class="order-total-amount">訂單總額: NT$ ${Math.round(parseFloat(orderDetail.order.total_amount))}</div>
    </div>
  `;

  // 顯示模態框
  modal.style.display = "flex";

  // 設置關閉事件
  setupOrderDetailModalEvents();
}

// 設置訂單詳情模態框事件
function setupOrderDetailModalEvents() {
  const modal = document.getElementById("order-detail-modal");
  const closeBtn = document.getElementById("close-order-detail-modal");

  if (!modal || !closeBtn) {
    return;
  }

  // 點擊關閉按鈕
  closeBtn.onclick = function () {
    modal.style.display = "none";
  };

  // 點擊模態框外部關閉
  modal.onclick = function (event) {
    if (event.target === modal) {
      modal.style.display = "none";
    }
  };

  // ESC 鍵關閉
  document.onkeydown = function (event) {
    if (event.key === "Escape" && modal.style.display === "flex") {
      modal.style.display = "none";
    }
  };
}
// 設置批次操作功能
function setupBatchOperations() {
  const selectAllBtn = document.getElementById("select-all-orders");
  const deselectAllBtn = document.getElementById("deselect-all-orders");
  const applyBatchBtn = document.getElementById("apply-batch-status");
  const selectedCountSpan = document.getElementById("selected-count");

  // 全選功能
  selectAllBtn.addEventListener("click", () => {
    const checkboxes = document.querySelectorAll(".order-checkbox");
    checkboxes.forEach(checkbox => {
      checkbox.checked = true;
      checkbox.closest('.order-item').classList.add('selected');
    });
    updateSelectedCount();
  });

  // 取消全選功能
  deselectAllBtn.addEventListener("click", () => {
    const checkboxes = document.querySelectorAll(".order-checkbox");
    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
      checkbox.closest('.order-item').classList.remove('selected');
    });
    updateSelectedCount();
  });

  // 批次更新狀態
  applyBatchBtn.addEventListener("click", async () => {
    const selectedCheckboxes = document.querySelectorAll(".order-checkbox:checked");
    const batchStatus = document.getElementById("batch-status-select").value;

    if (selectedCheckboxes.length === 0) {
      showMessage("請先選擇要更新的訂單", "warning");
      return;
    }

    if (!batchStatus) {
      showMessage("請選擇要更新的狀態", "warning");
      return;
    }

    const orderIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.orderId);

    if (confirm(`確定要將 ${orderIds.length} 個訂單的狀態更新為 ${getStatusText(batchStatus)} 嗎？`)) {
      await batchUpdateOrderStatus(orderIds, batchStatus);
    }
  });

  // 監聽訂單項目點擊事件
  document.addEventListener('change', (e) => {
    if (e.target.classList.contains('order-checkbox')) {
      const orderItem = e.target.closest('.order-item');
      if (e.target.checked) {
        orderItem.classList.add('selected');
      } else {
        orderItem.classList.remove('selected');
      }
      updateSelectedCount();
    }
  });
}

// 更新選中數量顯示
function updateSelectedCount() {
  const selectedCheckboxes = document.querySelectorAll(".order-checkbox:checked");
  const selectedCountSpan = document.getElementById("selected-count");
  selectedCountSpan.textContent = `已選擇 ${selectedCheckboxes.length} 個訂單`;
}

// 獲取狀態中文文本 (現在直接返回，因為後端已經使用中文)
function getStatusText(status) {
  return status;
}

// 批次更新訂單狀態
async function batchUpdateOrderStatus(orderIds, newStatus) {
  try {
    showLoading();
    let successCount = 0;
    let failCount = 0;

    // 逐個更新訂單狀態
    for (const orderId of orderIds) {
      try {
        const response = await apiRequest(`/api/orders/${orderId}/status`, {
          method: 'PUT',
          body: JSON.stringify({ status: newStatus })
        });

        if (response.success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        console.error(`更新訂單 ${orderId} 失敗:`, error);
        failCount++;
      }
    }

    // 顯示結果
    if (successCount > 0) {
      showMessage(`成功更新 ${successCount} 個訂單狀態`, "success");
    }
    if (failCount > 0) {
      showMessage(`${failCount} 個訂單更新失敗`, "error");
    }

    // 重新載入訂單列表
    loadOrders({});

  } catch (error) {
    console.error("批次更新訂單狀態錯誤:", error);
    showMessage("批次更新失敗: " + error.message, "error");
  } finally {
    hideLoading();
  }
}