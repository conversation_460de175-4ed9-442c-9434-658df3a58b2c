<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台南社區藥局藥品供應平台</title>
    <link rel="stylesheet" href="css/style.css?v=20250809-address">
</head>

<body>
    <div class="container">
        <header>
            <div class="header-content">
                <div class="logo-title">
                    <img src="images/logo.png" alt="公司標誌" class="header-logo">
                    <div class="title-section">
                        <h1>台南社區藥局藥品供應平台</h1>
                        <p class="subtitle">行政院衛生署補(捐)助科技發展計畫</p>
                    </div>
                </div>
                <div id="user-info" class="user-info" style="display: none;">
                    <span id="user-display-name"></span>
                    <button id="logout-btn" class="btn btn-secondary">登出</button>
                </div>
            </div>
        </header>

        <!-- 登入表單 -->
        <div id="login-section" class="section">
            <div class="card">
                <h2>使用者登入</h2>
                <form id="login-form">
                    <div class="form-group">
                        <label for="username">使用者名稱:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">密碼:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">登入</button>
                </form>

                <div class="register-link">
                    <p>還沒有帳號？ <a href="#" id="show-register">註冊新帳號</a></p>
                </div>
            </div>
        </div>

        <!-- 註冊表單 -->
        <div id="register-section" class="section" style="display: none;">
            <div class="card">
                <h2>加入會員</h2>
                <form id="register-form">
                    <div class="form-row">
                        <div class="form-group-inline">
                            <label for="reg-username"><span class="required">*</span>帳號:</label>
                            <input type="text" id="reg-username" name="username" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="reg-password"><span class="required">*</span>密碼:</label>
                            <input type="password" id="reg-password" name="password" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="reg-password-confirm"><span class="required">*</span>確認密碼:</label>
                            <input type="password" id="reg-password-confirm" name="password_confirm" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group-inline">
                            <label for="pharmacy-name"><span class="required">*</span>機構單位:</label>
                            <input type="text" id="pharmacy-name" name="pharmacy_name" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="contact-person"><span class="required">*</span>聯絡人:</label>
                            <input type="text" id="contact-person" name="contact_person" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="phone"><span class="required">*</span>聯絡電話:</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="mobile"><span class="required">*</span>手機號碼:</label>
                            <input type="tel" id="mobile" name="mobile" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group-inline">
                            <label for="institution-code"><span class="required">*</span>機構代號:</label>
                            <input type="text" id="institution-code" name="institution_code" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="reg-email"><span class="required">*</span>電子郵件:</label>
                            <input type="email" id="reg-email" name="email" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="address"><span class="required">*</span>聯絡地址:</label>
                            <input type="text" id="address" name="address" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">確定加入會員</button>
                    <button type="button" id="show-login" class="btn btn-secondary">返回登入</button>
                </form>
            </div>
        </div>

        <!-- 主要內容區域 -->
        <div id="main-content" class="section" style="display: none;">
            <!-- 促銷訊息橫幅 -->
            <div id="promotion-banner" class="promotion-banner" style="display: none;">
                <div class="promotion-content">
                    <div class="promotion-header">
                        <h4>📢 最新訊息</h4>
                        <button id="close-promotion-banner" class="close-banner">×</button>
                    </div>
                    <div id="promotion-messages">
                        <!-- 促銷訊息將在這裡顯示 -->
                    </div>
                    <div class="promotion-actions">
                        <button id="view-all-promotions" class="btn btn-secondary btn-sm">查看全部</button>
                        <span id="unread-count" class="unread-badge" style="display: none;">0</span>
                    </div>
                </div>
            </div>

            <nav class="nav-tabs">
                <button class="nav-tab active" data-tab="products">產品管理</button>
                <button class="nav-tab" data-tab="cart">🛒 購物車</button>
                <button class="nav-tab" data-tab="orders">訂單管理</button>
                <button class="nav-tab" data-tab="profile">個人資料</button>
                <button class="nav-tab" data-tab="promotions-view" id="promotions-tab">
                    📢 訊息
                    <span id="nav-unread-count" class="nav-unread-badge" style="display: none;">0</span>
                </button>
                <button class="nav-tab" data-tab="admin" id="admin-tab" style="display: none;">系統管理</button>
            </nav>

            <!-- 產品管理 -->
            <div id="products-tab" class="tab-content active">
                <div class="card">
                    <div class="product-header">
                        <h3>🏥 查詢中心產品目錄</h3>
                        <div class="search-controls">
                            <div class="search-bar">
                                <label>搜尋條件：</label>
                                <input type="text" id="product-search" placeholder="輸入產品名稱、健保代碼或製造商...">
                                <button id="search-btn" class="btn btn-primary">查詢</button>
                                <button id="clear-search-btn" class="btn btn-secondary">清除</button>
                            </div>
                        </div>
                    </div>

                    <div class="products-grid-container">
                        <div class="products-grid-header">
                            <div class="grid-header-item">健保資訊</div>
                            <div class="grid-header-item">品名 / 成分</div>
                            <div class="grid-header-item">單價</div>
                            <div class="grid-header-item">數量</div>
                            <div class="grid-header-item">功能</div>
                            <div class="grid-header-item">狀態</div>
                            <div class="grid-header-item">保存</div>
                        </div>
                        <div id="products-grid-body" class="products-grid-body">
                            <!-- 產品卡片將在這裡動態載入 -->
                        </div>
                    </div>

                    <div class="table-pagination">
                        <div class="pagination-info">
                            <span id="pagination-info">顯示第 1-10 筆，共 0 筆資料</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="prev-page" class="btn btn-secondary">上一頁</button>
                            <div class="page-input-group">
                                <span>第</span>
                                <input type="number" id="page-input" min="1" value="1" style="width: 60px; text-align: center;">
                                <span>頁</span>
                                <button id="goto-page" class="btn btn-primary">前往</button>
                            </div>
                            <button id="next-page" class="btn btn-secondary">下一頁</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 購物車 -->
            <div id="cart-tab" class="tab-content">
                <div class="card">
                    <h3>🛒 購物車</h3>
                    <div id="cart-items">
                        <!-- 購物車項目將在這裡動態載入 -->
                    </div>
                    <div class="cart-notes">
                        <div class="form-group">
                            <label for="order-notes">訂單備註:</label>
                            <textarea id="order-notes" placeholder="請輸入訂單備註（選填）" rows="3"
                                style="width: 100%; resize: vertical;"></textarea>
                        </div>
                    </div>
                    <div class="cart-summary">
                        <div class="total">
                            <strong>總計: NT$ <span id="cart-total">0</span></strong>
                        </div>
                        <button id="checkout-btn" class="btn btn-primary">💳 結帳</button>
                        <button id="clear-cart-btn" class="btn btn-secondary">🗑️ 清空購物車</button>
                    </div>
                </div>
            </div>

            <!-- 訂單管理 -->
            <div id="orders-tab" class="tab-content">
                <div class="card">
                    <div class="orders-header">
                        <h3 id="orders-title">訂單歷史</h3>
                        <div id="admin-order-controls" class="admin-controls" style="display: none;">
                            <div class="order-filters">
                                <select id="status-filter">
                                    <option value="">所有狀態</option>
                                    <option value="pending" selected>待處理</option>
                                    <option value="processing">檢貨中</option>
                                    <option value="shipped">已出貨</option>
                                </select>
                                <button id="apply-filters" class="btn btn-secondary">篩選</button>
                                <button id="clear-filters" class="btn btn-outline">清除</button>
                            </div>
                            <div class="batch-operations" id="batch-operations" style="display: none;">
                                <div class="batch-controls">
                                    <button id="select-all-orders" class="btn btn-outline">全選</button>
                                    <button id="deselect-all-orders" class="btn btn-outline">取消全選</button>
                                    <select id="batch-status-select">
                                        <option value="">選擇狀態</option>
                                        <option value="pending">待處理</option>
                                        <option value="processing">檢貨中</option>
                                        <option value="shipped">已出貨</option>
                                    </select>
                                    <button id="apply-batch-status" class="btn btn-primary">批次更新狀態</button>
                                    <span id="selected-count" class="selected-count">已選擇 0 個訂單</span>
                                </div>
                            </div>
                            <div class="order-stats" id="order-stats">
                                <!-- 統計信息將在這裡顯示 -->
                            </div>
                        </div>
                    </div>
                    <div id="orders-list">
                        <!-- 訂單列表將在這裡動態載入 -->
                    </div>
                </div>
            </div>

            <!-- 個人資料 -->
            <div id="profile-tab" class="tab-content">
                <div class="card">
                    <h3>個人資料</h3>
                    <form id="profile-form">
                        <div class="form-row">
                            <div class="form-group-inline">
                                <label for="profile-username">帳號:</label>
                                <input type="text" id="profile-username" name="username" disabled readonly>
                            </div>
                            <div class="form-group-inline">
                                <label for="profile-email-addr">電子郵件:</label>
                                <input type="email" id="profile-email-addr" name="email" disabled readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group-inline">
                                <label for="profile-pharmacy-name">機構單位:</label>
                                <input type="text" id="profile-pharmacy-name" name="pharmacy_name">
                            </div>
                            <div class="form-group-inline">
                                <label for="profile-contact-person">聯絡人:</label>
                                <input type="text" id="profile-contact-person" name="contact_person">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group-inline">
                                <label for="profile-phone">聯絡電話:</label>
                                <input type="tel" id="profile-phone" name="phone">
                            </div>
                            <div class="form-group-inline">
                                <label for="profile-mobile">手機號碼:</label>
                                <input type="tel" id="profile-mobile" name="mobile">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group-inline">
                                <label for="profile-institution-code">機構代號:</label>
                                <input type="text" id="profile-institution-code" name="institution_code">
                            </div>
                            <div class="form-group-inline">
                                <label for="profile-address">聯絡地址:</label>
                                <input type="text" id="profile-address" name="address">
                            </div>
                        </div>
                        <!-- 通知設定已移除 -->
                        <div class="form-row">
                            <button type="submit" class="btn btn-primary">更新資料</button>
                            <button type="button" id="change-password-btn" class="btn btn-secondary">修改密碼</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 促銷訊息查看 -->
            <div id="promotions-view-tab" class="tab-content">
                <div class="card">
                    <div class="promotions-view-header">
                        <h3>📢 促銷訊息</h3>
                        <div class="promotions-view-controls">
                            <button id="mark-all-read" class="btn btn-secondary">全部標為已讀</button>
                            <div class="view-filter">
                                <label for="view-filter-select">顯示:</label>
                                <select id="view-filter-select">
                                    <option value="all">全部訊息</option>
                                    <option value="unread">未讀訊息</option>
                                    <option value="active">活躍訊息</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div id="promotions-view-content" class="promotions-view-content">
                        <!-- 促銷訊息列表將在這裡顯示 -->
                    </div>
                    <div id="promotions-view-pagination" class="table-pagination">
                        <div class="pagination-info">
                            <span id="promotions-view-pagination-info">顯示第 1-10 筆，共 0 筆資料</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="promotions-view-prev-page" class="btn btn-secondary">上一頁</button>
                            <div class="page-input-group">
                                <span>第</span>
                                <input type="number" id="promotions-view-page-input" min="1" value="1" style="width: 60px; text-align: center;">
                                <span>頁</span>
                                <button id="promotions-view-goto-page" class="btn btn-primary">前往</button>
                            </div>
                            <button id="promotions-view-next-page" class="btn btn-secondary">下一頁</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系統管理 -->
            <div id="admin-tab" class="tab-content">
                <div class="card">
                    <h3>🔐 角色權限管理</h3>

                    <!-- 角色管理子標籤 -->
                    <div class="admin-nav">
                        <button class="admin-nav-tab active" data-admin-tab="roles">角色管理</button>
                        <button class="admin-nav-tab" data-admin-tab="permissions">權限查看</button>
                        <button class="admin-nav-tab" data-admin-tab="users">用戶權限</button>
                        <button class="admin-nav-tab" data-admin-tab="messages">訊息管理</button>
                        <button class="admin-nav-tab" data-admin-tab="promotions">促銷管理</button>
                    </div>

                    <!-- 角色管理 -->
                    <div id="roles-admin" class="admin-tab-content active">
                        <div class="admin-header">
                            <h4>角色列表</h4>
                            <button id="create-role-btn" class="btn btn-primary">新增角色</button>
                        </div>

                        <div class="roles-table-container">
                            <table id="roles-table" class="admin-table">
                                <thead>
                                    <tr>
                                        <th>角色名稱</th>
                                        <th>描述</th>
                                        <th>權限數量</th>
                                        <th>建立時間</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="roles-table-body">
                                    <!-- 角色資料將在這裡動態載入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 權限查看 -->
                    <div id="permissions-admin" class="admin-tab-content">
                        <h4>系統權限列表</h4>
                        <div id="permissions-list">
                            <!-- 權限列表將在這裡動態載入 -->
                        </div>
                    </div>

                    <!-- 用戶權限 -->
                    <div id="users-admin" class="admin-tab-content">
                        <h4>用戶權限查看</h4>
                        <div class="user-search">
                            <input type="text" id="user-search" placeholder="搜尋用戶...">
                            <button id="search-user-btn" class="btn btn-secondary">搜尋</button>
                        </div>
                        <div id="user-permissions-list">
                            <!-- 用戶權限列表將在這裡動態載入 -->
                        </div>
                    </div>

                    <!-- 訊息管理 -->
                    <div id="messages-admin" class="admin-tab-content">
                        <div class="admin-header">
                            <h4>📢 訊息管理</h4>
                            <button id="create-message-btn" class="btn btn-primary">發送新訊息</button>
                        </div>
                        <div class="messages-filters">
                            <div class="filter-group">
                                <label for="message-type-filter">類型:</label>
                                <select id="message-type-filter">
                                    <option value="">所有類型</option>
                                    <option value="promotion">促銷訊息</option>
                                    <option value="system">系統通知</option>
                                    <option value="announcement">一般公告</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="message-status-filter">狀態:</label>
                                <select id="message-status-filter">
                                    <option value="">所有狀態</option>
                                    <option value="active">啟用</option>
                                    <option value="inactive">停用</option>
                                </select>
                            </div>
                        </div>
                        <div id="messages-list" class="messages-list">
                            <!-- 訊息列表將由 JavaScript 動態填充 -->
                        </div>
                    </div>

                    <!-- 促銷管理 -->
                    <div id="promotions-admin" class="admin-tab-content">
                        <div class="admin-header">
                            <h4>🏷️ 促銷管理</h4>
                            <button id="create-promotion-btn" class="btn btn-primary">新增促銷</button>
                        </div>
                        <div class="promotions-filters">
                            <div class="filter-group">
                                <label for="promotion-type-filter">類型:</label>
                                <select id="promotion-type-filter">
                                    <option value="">所有類型</option>
                                    <option value="general">一般公告</option>
                                    <option value="discount">優惠促銷</option>
                                    <option value="welcome">歡迎訊息</option>
                                    <option value="system">系統通知</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="promotion-status-filter">狀態:</label>
                                <select id="promotion-status-filter">
                                    <option value="">所有狀態</option>
                                    <option value="active">啟用</option>
                                    <option value="inactive">停用</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="promotion-audience-filter">目標受眾:</label>
                                <select id="promotion-audience-filter">
                                    <option value="">所有用戶</option>
                                    <option value="all">所有用戶</option>
                                    <option value="pharmacy">藥局用戶</option>
                                    <option value="admin">管理員</option>
                                </select>
                            </div>
                            <button id="filter-promotions-btn" class="btn btn-secondary">篩選</button>
                            <button id="clear-promotion-filters-btn" class="btn btn-secondary">清除篩選</button>
                        </div>
                        <div class="promotions-table-container">
                            <table id="promotions-table" class="admin-table">
                                <thead>
                                    <tr>
                                        <th>標題</th>
                                        <th>類型</th>
                                        <th>優先級</th>
                                        <th>目標受眾</th>
                                        <th>狀態</th>
                                        <th>開始時間</th>
                                        <th>結束時間</th>
                                        <th>創建時間</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="promotions-table-body">
                                    <!-- 促銷資料將在這裡動態載入 -->
                                </tbody>
                            </table>
                        </div>
                        <div class="table-pagination">
                            <div class="pagination-info">
                                <span id="promotions-pagination-info">顯示第 1-10 筆，共 0 筆資料</span>
                            </div>
                            <div class="pagination-controls">
                                <button id="promotions-prev-page" class="btn btn-secondary">上一頁</button>
                                <div class="page-input-group">
                                    <span>第</span>
                                    <input type="number" id="promotions-page-input" min="1" value="1" style="width: 60px; text-align: center;">
                                    <span>頁</span>
                                    <button id="promotions-goto-page" class="btn btn-primary">前往</button>
                                </div>
                                <button id="promotions-next-page" class="btn btn-secondary">下一頁</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 載入指示器 -->
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p>載入中...</p>
        </div>

        <!-- 訊息提示 -->
        <div id="message" class="message" style="display: none;"></div>

        <!-- 角色編輯模態框 -->
        <div id="role-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="role-modal-title">編輯角色</h3>
                    <span class="close" id="close-role-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="role-form">
                        <input type="hidden" id="role-id">
                        <div class="form-group">
                            <label for="role-name">角色名稱:</label>
                            <input type="text" id="role-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="role-description">描述:</label>
                            <textarea id="role-description" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label>權限設定:</label>
                            <div id="permissions-checkboxes" class="permissions-grid">
                                <!-- 權限複選框將在這裡動態載入 -->
                            </div>
                        </div>
                        <div class="modal-actions">
                            <button type="submit" class="btn btn-primary">儲存</button>
                            <button type="button" id="cancel-role-edit" class="btn btn-secondary">取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 訂單詳情模態框 -->
        <div id="order-detail-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="order-detail-title">訂單詳情</h3>
                    <span class="close" id="close-order-detail-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="order-detail-content">
                        <!-- 訂單詳情將在這裡動態載入 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 訊息編輯模態框 -->
        <div id="message-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="message-modal-title">發送新訊息</h3>
                    <span class="close" id="close-message-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="message-form">
                        <input type="hidden" id="message-id">
                        <div class="form-group">
                            <label for="message-title">標題:</label>
                            <input type="text" id="message-title" name="title" required maxlength="200" placeholder="請輸入訊息標題">
                        </div>
                        <div class="form-group">
                            <label for="message-content">內容:</label>
                            <textarea id="message-content" name="content" rows="4" required placeholder="請輸入訊息內容"></textarea>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="message-type">類型:</label>
                                <select id="message-type" name="message_type" required>
                                    <option value="announcement">一般公告</option>
                                    <option value="system">系統通知</option>
                                    <option value="promotion">促銷訊息</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="message-priority">優先級:</label>
                                <input type="number" id="message-priority" name="priority" min="1" max="10" value="1" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="message-audience">目標受眾:</label>
                            <select id="message-audience" name="target_audience" required>
                                <option value="all">所有用戶</option>
                                <option value="pharmacy">藥局用戶</option>
                                <option value="admin">管理員</option>
                            </select>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="message-start-date">開始時間:</label>
                                <input type="datetime-local" id="message-start-date" name="starts_at">
                            </div>
                            <div class="form-group">
                                <label for="message-end-date">結束時間:</label>
                                <input type="datetime-local" id="message-end-date" name="ends_at">
                            </div>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="message-active" name="is_active" checked>
                                立即啟用此訊息
                            </label>
                        </div>
                        <div class="modal-actions">
                            <button type="submit" class="btn btn-primary">發送</button>
                            <button type="button" id="cancel-message-edit" class="btn btn-secondary">取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 促銷編輯模態框 -->
        <div id="promotion-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="promotion-modal-title">新增促銷訊息</h3>
                    <span class="close" id="close-promotion-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="promotion-form">
                        <input type="hidden" id="promotion-id">
                        <div class="form-group">
                            <label for="promotion-title">標題:</label>
                            <input type="text" id="promotion-title" name="title" required maxlength="200" placeholder="請輸入促銷標題">
                        </div>
                        <div class="form-group">
                            <label for="promotion-content">內容:</label>
                            <textarea id="promotion-content" name="content" rows="4" required placeholder="請輸入促銷內容"></textarea>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="promotion-type">類型:</label>
                                <select id="promotion-type" name="promotion_type" required>
                                    <option value="general">一般公告</option>
                                    <option value="discount">優惠促銷</option>
                                    <option value="welcome">歡迎訊息</option>
                                    <option value="system">系統通知</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="promotion-priority">優先級:</label>
                                <input type="number" id="promotion-priority" name="priority" min="1" max="10" value="1" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="promotion-audience">目標受眾:</label>
                            <select id="promotion-audience" name="target_audience" required>
                                <option value="all">所有用戶</option>
                                <option value="pharmacy">藥局用戶</option>
                                <option value="admin">管理員</option>
                            </select>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="promotion-start-date">開始時間:</label>
                                <input type="datetime-local" id="promotion-start-date" name="starts_at">
                            </div>
                            <div class="form-group">
                                <label for="promotion-end-date">結束時間:</label>
                                <input type="datetime-local" id="promotion-end-date" name="ends_at">
                            </div>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="promotion-active" name="is_active" checked>
                                啟用此促銷訊息
                            </label>
                        </div>
                        <div class="modal-actions">
                            <button type="submit" class="btn btn-primary">儲存</button>
                            <button type="button" id="cancel-promotion-edit" class="btn btn-secondary">取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="js/app.js?v=20250809-address"></script>
</body>

</html>