#!/usr/bin/env python3
import psycopg2

DATABASE_URL = "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require"

def fix_nhi_structure():
    print("🔄 修正資料庫結構：使用 nhi_code 作為主鍵...")
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # 1. 刪除現有的外鍵約束
        print("1. 移除現有外鍵約束...")
        try:
            cursor.execute("ALTER TABLE products DROP CONSTRAINT IF EXISTS fk_products_nhi_price")
            print("   ✅ 外鍵約束已移除")
        except Exception as e:
            print(f"   ⚠️  移除外鍵約束失敗: {e}")
        
        # 2. 刪除 products 表的 nhi_price 欄位
        print("2. 移除 products 表的 nhi_price 欄位...")
        try:
            cursor.execute("ALTER TABLE products DROP COLUMN IF EXISTS nhi_price")
            print("   ✅ nhi_price 欄位已移除")
        except Exception as e:
            print(f"   ⚠️  移除欄位失敗: {e}")
        
        # 3. 刪除舊的 nhi_prices 表
        print("3. 刪除舊的 nhi_prices 表...")
        try:
            cursor.execute("DROP TABLE IF EXISTS nhi_prices CASCADE")
            print("   ✅ 舊表已刪除")
        except Exception as e:
            print(f"   ⚠️  刪除舊表失敗: {e}")
        
        # 4. 創建新的 nhi_prices 表，使用 nhi_code 作為主鍵
        print("4. 創建新的 nhi_prices 表...")
        cursor.execute("""
            CREATE TABLE nhi_prices (
                nhi_code VARCHAR(20) PRIMARY KEY,
                nhi_price DECIMAL(10,2) NOT NULL,
                selling_price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("   ✅ 新的 nhi_prices 表已創建")
        
        # 5. 創建索引
        cursor.execute("CREATE INDEX idx_nhi_price ON nhi_prices (nhi_price)")
        cursor.execute("CREATE INDEX idx_selling_price ON nhi_prices (selling_price)")
        print("   ✅ 索引已創建")
        
        # 6. 插入測試數據
        print("5. 插入測試數據...")
        test_data = [
            ('A001', 280.40, 280.40),
            ('A002', 320.00, 320.00), 
            ('A003', 220.30, 220.30),
            ('A004', 420.60, 420.60),
            ('A005', 180.90, 180.90),
        ]
        
        for nhi_code, nhi_price, selling_price in test_data:
            cursor.execute("""
                INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price)
                VALUES (%s, %s, %s)
            """, (nhi_code, nhi_price, selling_price))
        print(f"   ✅ 已插入 {len(test_data)} 筆測試數據")
        
        # 7. 為 products 表添加 nhi_code 欄位
        print("6. 為 products 表添加 nhi_code 欄位...")
        cursor.execute("ALTER TABLE products ADD COLUMN nhi_code VARCHAR(20)")
        print("   ✅ nhi_code 欄位已添加")
        
        # 8. 更新 products 表，設定 nhi_code 值
        print("7. 更新產品的 nhi_code...")
        # 根據價格映射到對應的 nhi_code
        price_mapping = {
            280.40: 'A001',
            320.00: 'A002',
            220.30: 'A003', 
            420.60: 'A004',
            180.90: 'A005'
        }
        
        for price, code in price_mapping.items():
            cursor.execute("UPDATE products SET nhi_code = %s WHERE unit_price = %s", (code, price))
        print("   ✅ 產品 nhi_code 已更新")
        
        # 9. 添加外鍵約束
        print("8. 添加外鍵約束...")
        cursor.execute("""
            ALTER TABLE products 
            ADD CONSTRAINT fk_products_nhi_code 
            FOREIGN KEY (nhi_code) REFERENCES nhi_prices(nhi_code)
        """)
        print("   ✅ 外鍵約束已添加")
        
        # 10. 檢查結果
        print("\n📊 檢查修正結果...")
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        nhi_count = cursor.fetchone()[0]
        print(f"   nhi_prices 表: {nhi_count} 筆記錄")
        
        cursor.execute("SELECT COUNT(*) FROM products WHERE nhi_code IS NOT NULL")
        products_count = cursor.fetchone()[0]
        print(f"   products 表有 nhi_code: {products_count} 筆記錄")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 資料庫結構修正完成！現在使用 nhi_code 作為主鍵")
        return True
        
    except Exception as e:
        print(f"❌ 修正失敗: {e}")
        return False

if __name__ == "__main__":
    success = fix_nhi_structure()
    print(f"\n{'🎊 修正成功!' if success else '❌ 修正失敗'}")