#!/bin/bash

echo "🔧 完整修復結帳問題..."

# 1. 設定環境變數
export JWT_SECRET="my-super-secret-jwt-key-for-testing-2024"
export DATABASE_URL="postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require"

echo "✅ 環境變數已設定"

# 2. 停止現有伺服器
echo "🛑 停止現有伺服器..."
pkill -f pharmacy-system
sleep 2

# 3. 重新啟動伺服器
echo "🚀 重新啟動伺服器..."
cargo run --bin pharmacy-system &
SERVER_PID=$!
sleep 5

# 4. 測試伺服器狀態
echo "🏥 測試伺服器狀態..."
if curl -s http://localhost:8080/health | grep -q '"status":"healthy"'; then
    echo "✅ 伺服器啟動成功"
else
    echo "❌ 伺服器啟動失敗"
    exit 1
fi

# 5. 創建測試用戶
echo "👤 創建測試用戶..."
REGISTER_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "checkoutuser",
    "email": "<EMAIL>", 
    "password": "TestPass123!",
    "pharmacy_name": "結帳測試藥局",
    "phone": "**********"
  }')

echo "註冊回應: $REGISTER_RESPONSE"

# 6. 登入測試用戶
echo "🔐 登入測試用戶..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "checkoutuser",
    "password": "TestPass123!"
  }')

echo "登入回應: $LOGIN_RESPONSE"

# 7. 提取 token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
    echo "✅ 成功取得 token"
    
    # 8. 添加商品到購物車
    echo "🛒 添加商品到購物車..."
    ADD_CART_RESPONSE=$(curl -s -X POST http://localhost:8080/api/cart \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d '{
        "product_id": 32,
        "quantity": 2
      }')
    
    echo "添加購物車回應: $ADD_CART_RESPONSE"
    
    # 9. 測試結帳
    echo "💳 測試結帳..."
    CHECKOUT_RESPONSE=$(curl -s -X POST http://localhost:8080/api/orders/cart \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d '{
        "notes": "測試訂單"
      }')
    
    echo "結帳回應: $CHECKOUT_RESPONSE"
    
    # 10. 分析結果
    echo ""
    echo "📊 測試結果："
    echo "- 伺服器狀態: ✅ 正常"
    echo "- 用戶註冊: $(if echo $REGISTER_RESPONSE | grep -q '"success":true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
    echo "- 用戶登入: $(if echo $LOGIN_RESPONSE | grep -q '"success":true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
    echo "- 添加購物車: $(if echo $ADD_CART_RESPONSE | grep -q '"success":true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
    echo "- 結帳功能: $(if echo $CHECKOUT_RESPONSE | grep -q '"success":true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
    
    if echo $CHECKOUT_RESPONSE | grep -q '"success":true'; then
        echo ""
        echo "🎉 結帳功能修復成功！"
        echo ""
        echo "📋 使用說明："
        echo "1. 訪問 http://localhost:8080"
        echo "2. 使用以下帳號登入："
        echo "   - 用戶名: checkoutuser"
        echo "   - 密碼: TestPass123!"
        echo "3. 瀏覽產品並添加到購物車"
        echo "4. 點擊結帳按鈕完成訂單"
        echo ""
        echo "🔧 如果仍有問題，請檢查："
        echo "1. 資料庫連接是否正常"
        echo "2. 環境變數是否正確設定"
        echo "3. 伺服器日誌中的錯誤信息"
    else
        echo ""
        echo "❌ 結帳功能仍有問題"
        echo ""
        echo "🔍 錯誤分析："
        echo "註冊: $REGISTER_RESPONSE"
        echo "登入: $LOGIN_RESPONSE"
        echo "購物車: $ADD_CART_RESPONSE"
        echo "結帳: $CHECKOUT_RESPONSE"
        echo ""
        echo "💡 建議解決方案："
        echo "1. 檢查資料庫表結構"
        echo "2. 確認環境變數設定"
        echo "3. 查看伺服器日誌"
        echo "4. 重新執行資料庫遷移"
    fi
    
else
    echo "❌ 無法取得有效的 token"
    echo ""
    echo "🔍 問題分析："
    echo "註冊回應: $REGISTER_RESPONSE"
    echo "登入回應: $LOGIN_RESPONSE"
    echo ""
    echo "💡 可能的解決方案："
    echo "1. 檢查資料庫連接"
    echo "2. 確認 JWT_SECRET 設定"
    echo "3. 檢查資料庫表結構"
    echo "4. 重新執行資料庫遷移"
fi

echo ""
echo "🎯 結帳問題修復完成！" 