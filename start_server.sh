#!/bin/bash

echo "🚀 啟動藥品採購系統..."

# 設置環境變數
export RUST_LOG=info
export DATABASE_URL=${DATABASE_URL:-postgresql://postgres:password@localhost:5432/pharmacy_db}
export JWT_SECRET=your-super-secret-jwt-key-here-for-development
export PORT=8080

# 編譯和啟動服務器
echo "📦 編譯應用程式..."
cargo build --release --bin pharmacy-system

if [ $? -eq 0 ]; then
    echo "✅ 編譯成功！"
    echo "🌐 啟動服務器在 http://localhost:8080"
    echo "📚 可用的 API 端點："
    echo "   - GET  / (API 資訊)"
    echo "   - GET  /health (健康檢查)"
    echo "   - POST /api/auth/register (註冊)"
    echo "   - POST /api/auth/login (登入)"
    echo "   - GET  /api/products (產品列表)"
    echo "   - GET  /api/orders (訂單列表)"
    echo "   - GET  /api/cart (購物車)"
    echo ""
    echo "按 Ctrl+C 停止服務器"
    echo ""
    
    # 啟動服務器
./target/release/pharmacy-system
else
    echo "❌ 編譯失敗！"
    exit 1
fi