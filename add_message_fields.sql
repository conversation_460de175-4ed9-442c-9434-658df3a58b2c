-- 檢查並添加新欄位到 messages 表格
DO $$
BEGIN
    -- 添加 target_audience 欄位
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'messages' AND column_name = 'target_audience') THEN
        ALTER TABLE messages ADD COLUMN target_audience VARCHAR(50) DEFAULT 'all';
    END IF;
    
    -- 添加 priority 欄位
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'messages' AND column_name = 'priority') THEN
        ALTER TABLE messages ADD COLUMN priority INTEGER DEFAULT 1;
    END IF;
    
    -- 添加 starts_at 欄位
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'messages' AND column_name = 'starts_at') THEN
        ALTER TABLE messages ADD COLUMN starts_at TIMESTAMP WITH TIME ZONE;
    END IF;
    
    -- 添加 ends_at 欄位
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'messages' AND column_name = 'ends_at') THEN
        ALTER TABLE messages ADD COLUMN ends_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;
