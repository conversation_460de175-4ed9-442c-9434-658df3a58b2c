#!/usr/bin/env python3
import psycopg2

DATABASE_URL = "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require"

def fix_nhi_structure():
    print("🔄 修正資料庫結構：使用 nhi_code 作為主鍵...")
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # 1. 完全重置 - 移除所有約束和欄位
        print("1. 完全重置現有結構...")
        try:
            cursor.execute("ALTER TABLE products DROP CONSTRAINT IF EXISTS fk_products_nhi_code")
            cursor.execute("ALTER TABLE products DROP CONSTRAINT IF EXISTS fk_products_nhi_price")
            cursor.execute("DROP INDEX IF EXISTS products_nhi_code_key")
            cursor.execute("ALTER TABLE products DROP COLUMN IF EXISTS nhi_code CASCADE")
            cursor.execute("ALTER TABLE products DROP COLUMN IF EXISTS nhi_price CASCADE")
            cursor.execute("DROP TABLE IF EXISTS nhi_prices CASCADE")
            print("   ✅ 舊結構已完全清理")
        except Exception as e:
            print(f"   ⚠️  清理過程中的警告: {e}")
        
        # 2. 創建新的 nhi_prices 表
        print("2. 創建新的 nhi_prices 表...")
        cursor.execute("""
            CREATE TABLE nhi_prices (
                nhi_code VARCHAR(20) PRIMARY KEY,
                nhi_price DECIMAL(10,2) NOT NULL,
                selling_price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        cursor.execute("CREATE INDEX idx_nhi_price ON nhi_prices (nhi_price)")
        cursor.execute("CREATE INDEX idx_selling_price ON nhi_prices (selling_price)")
        print("   ✅ nhi_prices 表已創建")
        
        # 3. 插入測試數據
        print("3. 插入健保價格數據...")
        
        # 先獲取所有不同的價格
        cursor.execute("SELECT DISTINCT unit_price FROM products WHERE unit_price IS NOT NULL ORDER BY unit_price")
        unique_prices = [row[0] for row in cursor.fetchall()]
        print(f"   找到 {len(unique_prices)} 種不同價格")
        
        # 為每種價格生成 nhi_code
        test_data = []
        for i, price in enumerate(unique_prices, 1):
            nhi_code = f"A{i:03d}"  # A001, A002, A003...
            test_data.append((nhi_code, float(price), float(price)))
        
        for nhi_code, nhi_price, selling_price in test_data:
            cursor.execute("""
                INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price)
                VALUES (%s, %s, %s)
            """, (nhi_code, nhi_price, selling_price))
        print(f"   ✅ 已插入 {len(test_data)} 筆價格數據")
        
        # 4. 為 products 表添加 nhi_code 欄位
        print("4. 為 products 表添加 nhi_code 欄位...")
        cursor.execute("ALTER TABLE products ADD COLUMN nhi_code VARCHAR(20)")
        print("   ✅ nhi_code 欄位已添加")
        
        # 5. 更新 products 表的 nhi_code
        print("5. 更新產品的 nhi_code...")
        for i, price in enumerate(unique_prices, 1):
            nhi_code = f"A{i:03d}"
            cursor.execute("UPDATE products SET nhi_code = %s WHERE unit_price = %s", (nhi_code, price))
        print("   ✅ 產品 nhi_code 已更新")
        
        # 6. 添加外鍵約束
        print("6. 添加外鍵約束...")
        cursor.execute("""
            ALTER TABLE products 
            ADD CONSTRAINT fk_products_nhi_code 
            FOREIGN KEY (nhi_code) REFERENCES nhi_prices(nhi_code)
        """)
        print("   ✅ 外鍵約束已添加")
        
        # 7. 檢查結果
        print("\n📊 檢查修正結果...")
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        nhi_count = cursor.fetchone()[0]
        print(f"   nhi_prices 表: {nhi_count} 筆記錄")
        
        cursor.execute("SELECT COUNT(*) FROM products WHERE nhi_code IS NOT NULL")
        products_count = cursor.fetchone()[0]
        print(f"   products 表有 nhi_code: {products_count} 筆記錄")
        
        # 顯示樣本數據
        cursor.execute("SELECT nhi_code, nhi_price, selling_price FROM nhi_prices LIMIT 5")
        print("   nhi_prices 表樣本:")
        for code, nhi_price, selling_price in cursor.fetchall():
            print(f"     {code}: 健保價={nhi_price}, 賣價={selling_price}")
        
        cursor.execute("""
            SELECT p.name, p.unit_price, p.nhi_code, np.selling_price
            FROM products p
            LEFT JOIN nhi_prices np ON p.nhi_code = np.nhi_code
            WHERE p.nhi_code IS NOT NULL
            LIMIT 5
        """)
        print("   products 表關聯樣本:")
        for name, unit_price, nhi_code, selling_price in cursor.fetchall():
            print(f"     {name}: 單價={unit_price}, nhi_code={nhi_code}, 賣價={selling_price}")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 資料庫結構修正完成！現在使用 nhi_code 作為主鍵")
        return True
        
    except Exception as e:
        print(f"❌ 修正失敗: {e}")
        return False

if __name__ == "__main__":
    success = fix_nhi_structure()
    print(f"\n{'🎊 修正成功!' if success else '❌ 修正失敗'}")