#!/usr/bin/env python3
import pandas as pd
import psycopg2
import sys
from decimal import Decimal
import os
from datetime import datetime

def continue_import():
    print("=== 繼續匯入 4.xlsx 剩餘資料到 nhi_prices 資料表 ===")
    
    # 讀取 Excel 檔案
    print("正在讀取 4.xlsx...")
    df = pd.read_excel('4.xlsx', header=None)
    
    # 提取和過濾資料
    nhi_data = df.iloc[:, [1, 2]].copy()
    nhi_data.columns = ['nhi_code', 'nhi_price']
    
    # 套用相同的過濾條件
    nhi_data = nhi_data.dropna()
    nhi_data = nhi_data[nhi_data['nhi_code'] != '']
    nhi_data = nhi_data[nhi_data['nhi_price'] != 0]
    nhi_data = nhi_data.drop_duplicates(subset=['nhi_code'], keep='first')
    
    total_should_import = len(nhi_data)
    print(f"Excel檔案中總共有 {total_should_import} 筆有效記錄")
    
    # 連接資料庫
    database_url = os.getenv('DATABASE_URL')
    print("正在連接資料庫...")
    
    try:
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        # 檢查已匯入的健保代碼
        cursor.execute("SELECT nhi_code FROM nhi_prices")
        existing_codes = set(row[0] for row in cursor.fetchall())
        existing_count = len(existing_codes)
        print(f"資料庫中已有 {existing_count} 筆記錄")
        
        # 找出尚未匯入的記錄
        remaining_data = nhi_data[~nhi_data['nhi_code'].isin(existing_codes)]
        remaining_count = len(remaining_data)
        
        print(f"需要繼續匯入: {remaining_count} 筆")
        
        if remaining_count == 0:
            print("✅ 所有資料都已匯入完成！")
            cursor.close()
            conn.close()
            return
        
        print(f"匯入進度: {existing_count}/{total_should_import} ({existing_count/total_should_import*100:.1f}%)")
        
        # 顯示前幾筆將要匯入的資料
        print("\n前5筆將要匯入的資料:")
        print(remaining_data.head())
        
        # 批次匯入剩餘資料
        insert_sql = """
        INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price, effective_date)
        VALUES (%s, %s, %s, %s)
        """
        
        current_date = datetime.now().date()
        batch_size = 500  # 較小的批次大小以避免超時
        success_count = 0
        error_count = 0
        
        batch_data = []
        total_processed = 0
        
        for index, row in remaining_data.iterrows():
            try:
                nhi_code = str(row['nhi_code']).strip()
                nhi_price = Decimal(str(row['nhi_price']))
                
                batch_data.append((
                    nhi_code,
                    nhi_price,
                    nhi_price,  # selling_price = nhi_price
                    current_date
                ))
                
                total_processed += 1
                
                # 當批次資料達到指定大小時，執行批次插入
                if len(batch_data) >= batch_size:
                    try:
                        cursor.executemany(insert_sql, batch_data)
                        conn.commit()
                        success_count += len(batch_data)
                        progress = (existing_count + success_count) / total_should_import * 100
                        print(f"已匯入 {success_count} 筆 (總進度: {existing_count + success_count}/{total_should_import} = {progress:.1f}%)")
                        batch_data = []
                    except Exception as e:
                        print(f"批次匯入失敗: {e}")
                        conn.rollback()
                        error_count += len(batch_data)
                        batch_data = []
                        
            except Exception as e:
                error_count += 1
                print(f"第 {total_processed} 筆資料處理失敗: {e}")
                continue
        
        # 處理最後一批資料
        if batch_data:
            try:
                cursor.executemany(insert_sql, batch_data)
                conn.commit()
                success_count += len(batch_data)
                print(f"匯入最後 {len(batch_data)} 筆，總共新增 {success_count} 筆")
            except Exception as e:
                print(f"最後批次匯入失敗: {e}")
                conn.rollback()
                error_count += len(batch_data)
        
        # 最終統計
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        final_count = cursor.fetchone()[0]
        
        print(f"\n=== 繼續匯入完成 ===")
        print(f"本次成功匯入: {success_count} 筆")
        print(f"本次匯入失敗: {error_count} 筆")
        print(f"資料表最終記錄數: {final_count}")
        print(f"整體完成度: {final_count}/{total_should_import} ({final_count/total_should_import*100:.1f}%)")
        
        if final_count >= total_should_import:
            print("🎉 全部資料匯入完成！")
        else:
            remaining = total_should_import - final_count
            print(f"⚠️  仍有 {remaining} 筆資料尚未匯入")
        
        # 顯示一些統計資訊
        cursor.execute("""
            SELECT MIN(nhi_price), MAX(nhi_price), 
                   ROUND(AVG(nhi_price), 2) as avg_price,
                   COUNT(DISTINCT nhi_code) as unique_codes
            FROM nhi_prices
        """)
        stats = cursor.fetchone()
        min_price, max_price, avg_price, unique_codes = stats
        
        print(f"\n=== 最終統計 ===")
        print(f"不重複健保代碼: {unique_codes}")
        print(f"價格範圍: ${min_price} ~ ${max_price}")
        print(f"平均價格: ${avg_price}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"資料庫操作失敗: {e}")
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        sys.exit(1)

if __name__ == "__main__":
    try:
        continue_import()
    except Exception as e:
        print(f"執行失敗: {e}")
        sys.exit(1)