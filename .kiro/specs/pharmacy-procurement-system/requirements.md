# 需求文件

## 介紹

藥品中盤商採購系統是一個專為藥品批發商設計的訂購管理平台，讓一般藥局能夠方便地向中盤商訂購藥品。系統支援健保藥品資料管理、訂單處理、通知服務以及資料備份功能，使用 Rust 開發並部署到 GCP 平台。

## 需求

### 需求 1 - 藥品資料管理

**使用者故事：** 作為中盤商管理員，我希望能夠上傳健保藥品資料，以便維護最新的藥品目錄供藥局訂購。

#### 驗收標準

1. WHEN 管理員上傳 Excel 檔案 THEN 系統 SHALL 解析並匯入藥品資料到資料庫
2. WHEN 管理員上傳 CSV 檔案 THEN 系統 SHALL 解析並匯入藥品資料到資料庫
3. WHEN 上傳的檔案格式不正確 THEN 系統 SHALL 顯示錯誤訊息並拒絕匯入
4. WHEN 藥品資料匯入成功 THEN 系統 SHALL 顯示匯入結果統計

### 需求 2 - 訂購功能

**使用者故事：** 作為藥局使用者，我希望能夠瀏覽藥品目錄並建立訂單，以便向中盤商採購所需藥品。

#### 驗收標準

1. WHEN 藥局使用者登入系統 THEN 系統 SHALL 顯示可訂購的藥品清單
2. WHEN 使用者選擇藥品並指定數量 THEN 系統 SHALL 將商品加入購物車
3. WHEN 使用者確認訂單 THEN 系統 SHALL 建立訂購記錄並儲存到資料庫
4. WHEN 訂單建立成功 THEN 系統 SHALL 產生唯一的訂單編號
5. IF 藥品庫存不足 THEN 系統 SHALL 顯示庫存不足警告

### 需求 3 - 通知服務

**使用者故事：** 作為藥局使用者，我希望在訂購完成後能收到確認通知，以便追蹤我的訂單狀態。

#### 驗收標準

1. WHEN 訂單建立成功 THEN 系統 SHALL 透過 Email 發送訂單確認通知
2. WHEN 訂單建立成功 THEN 系統 SHALL 透過 Line 發送訂單確認通知
3. WHEN Email 發送失敗 THEN 系統 SHALL 記錄錯誤並嘗試重新發送
4. WHEN Line 通知發送失敗 THEN 系統 SHALL 記錄錯誤並嘗試重新發送
5. IF 使用者未設定通知偏好 THEN 系統 SHALL 使用預設的 Email 通知

### 需求 4 - 使用者管理

**使用者故事：** 作為系統管理員，我希望能夠管理藥局使用者帳號，以便控制系統存取權限。

#### 驗收標準

1. WHEN 管理員建立新的藥局帳號 THEN 系統 SHALL 儲存使用者資訊並產生登入憑證
2. WHEN 藥局使用者登入 THEN 系統 SHALL 驗證憑證並建立使用者會話
3. WHEN 使用者登入失敗 THEN 系統 SHALL 記錄失敗嘗試並顯示錯誤訊息
4. WHEN 使用者會話過期 THEN 系統 SHALL 要求重新登入

### 需求 5 - 資料備份系統

**使用者故事：** 作為系統管理員，我希望系統能夠自動備份重要資料，以便在系統故障時能夠快速恢復。

#### 驗收標準

1. WHEN 系統運行 THEN 系統 SHALL 每日自動備份 SQLite 資料庫
2. WHEN 備份完成 THEN 系統 SHALL 將備份檔案上傳到 GCP Cloud Storage
3. WHEN 備份失敗 THEN 系統 SHALL 記錄錯誤並通知管理員
4. WHEN 需要恢復資料 THEN 系統 SHALL 能夠從最新的備份檔案恢復資料庫
5. IF 備份檔案超過 30 天 THEN 系統 SHALL 自動刪除舊的備份檔案

### 需求 6 - 系統部署與維運

**使用者故事：** 作為開發團隊，我希望系統能夠穩定部署到 GCP 平台，以便提供可靠的服務。

#### 驗收標準

1. WHEN 系統部署到 GCP THEN 系統 SHALL 能夠正常啟動並提供服務
2. WHEN 系統接收 HTTP 請求 THEN 系統 SHALL 在合理時間內回應
3. WHEN 系統發生錯誤 THEN 系統 SHALL 記錄詳細的錯誤日誌
4. WHEN 系統負載過高 THEN 系統 SHALL 能夠自動擴展資源