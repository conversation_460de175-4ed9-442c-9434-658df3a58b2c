# 設計文件

## 概述

藥品中盤商採購系統採用現代化的微服務架構，使用 Rust 語言開發，提供高效能和記憶體安全的解決方案。系統設計為單體應用程式，但具備良好的模組化結構，便於未來擴展為微服務架構。

## 架構

### 整體架構

```mermaid
graph TB
    A[Web Frontend] --> B[API Gateway]
    B --> C[Authentication Service]
    B --> D[Product Service]
    B --> E[Order Service]
    B --> F[Notification Service]
    B --> G[File Upload Service]
    
    C --> H[SQLite Database]
    D --> H
    E --> H
    F --> I[Email Provider]
    F --> J[Line Bot API]
    G --> K[GCP Cloud Storage]
    
    L[Backup Service] --> H
    L --> K
```

### 技術棧

- **後端框架**: Axum (Rust 非同步 web 框架)
- **資料庫**: SQLite3 with SQLx (非同步 SQL 工具包)
- **檔案處理**: calamine (Excel), csv (CSV 處理)
- **認證**: JWT tokens with jsonwebtoken crate
- **通知服務**: 
  - Email: lettre crate
  - Line: reqwest for HTTP API calls
- **部署**: Docker containers on GCP Cloud Run
- **儲存**: GCP Cloud Storage for backups and file uploads
- **日誌**: tracing and tracing-subscriber

## 元件和介面

### 1. 認證服務 (Authentication Service)

**職責**: 處理使用者登入、註冊和會話管理

**主要介面**:
```rust
pub trait AuthService {
    async fn login(&self, credentials: LoginRequest) -> Result<AuthResponse>;
    async fn register(&self, user_data: RegisterRequest) -> Result<User>;
    async fn validate_token(&self, token: &str) -> Result<Claims>;
    async fn refresh_token(&self, refresh_token: &str) -> Result<AuthResponse>;
}
```

### 2. 產品服務 (Product Service)

**職責**: 管理藥品資料、庫存和檔案上傳

**主要介面**:
```rust
pub trait ProductService {
    async fn import_from_excel(&self, file_data: Vec<u8>) -> Result<ImportResult>;
    async fn import_from_csv(&self, file_data: Vec<u8>) -> Result<ImportResult>;
    async fn get_products(&self, filter: ProductFilter) -> Result<Vec<Product>>;
    async fn update_stock(&self, product_id: i64, quantity: i32) -> Result<()>;
}
```

### 3. 訂單服務 (Order Service)

**職責**: 處理訂單建立、管理和狀態追蹤

**主要介面**:
```rust
pub trait OrderService {
    async fn create_order(&self, order_data: CreateOrderRequest) -> Result<Order>;
    async fn get_orders(&self, user_id: i64, filter: OrderFilter) -> Result<Vec<Order>>;
    async fn update_order_status(&self, order_id: i64, status: OrderStatus) -> Result<()>;
    async fn get_order_details(&self, order_id: i64) -> Result<OrderDetails>;
}
```

### 4. 通知服務 (Notification Service)

**職責**: 處理 Email 和 Line 通知

**主要介面**:
```rust
pub trait NotificationService {
    async fn send_email(&self, notification: EmailNotification) -> Result<()>;
    async fn send_line_message(&self, notification: LineNotification) -> Result<()>;
    async fn send_order_confirmation(&self, order: &Order, user: &User) -> Result<()>;
}
```

### 5. 備份服務 (Backup Service)

**職責**: 自動備份資料庫到 GCP Cloud Storage

**主要介面**:
```rust
pub trait BackupService {
    async fn create_backup(&self) -> Result<BackupInfo>;
    async fn upload_to_cloud(&self, backup_path: &str) -> Result<String>;
    async fn restore_from_backup(&self, backup_url: &str) -> Result<()>;
    async fn cleanup_old_backups(&self) -> Result<()>;
}
```

## 資料模型

### 核心實體

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: i64,
    pub username: String,
    pub email: String,
    pub pharmacy_name: String,
    pub phone: Option<String>,
    pub line_user_id: Option<String>,
    pub notification_preferences: NotificationPreferences,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Product {
    pub id: i64,
    pub nhi_code: String,  // 健保代碼
    pub name: String,
    pub manufacturer: String,
    pub unit: String,
    pub unit_price: Decimal,
    pub stock_quantity: i32,
    pub description: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Order {
    pub id: i64,
    pub order_number: String,
    pub user_id: i64,
    pub status: OrderStatus,
    pub total_amount: Decimal,
    pub items: Vec<OrderItem>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderItem {
    pub id: i64,
    pub order_id: i64,
    pub product_id: i64,
    pub quantity: i32,
    pub unit_price: Decimal,
    pub subtotal: Decimal,
}
```

### 資料庫架構

```sql
-- 使用者表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    pharmacy_name TEXT NOT NULL,
    phone TEXT,
    line_user_id TEXT,
    notification_email BOOLEAN DEFAULT TRUE,
    notification_line BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 產品表
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nhi_code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    manufacturer TEXT NOT NULL,
    unit TEXT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    stock_quantity INTEGER DEFAULT 0,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 訂單表
CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number TEXT UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    total_amount DECIMAL(10,2) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 訂單項目表
CREATE TABLE order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

## 錯誤處理

### 錯誤類型定義

```rust
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("Authentication error: {0}")]
    Authentication(String),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("File processing error: {0}")]
    FileProcessing(String),
    
    #[error("Notification error: {0}")]
    Notification(String),
    
    #[error("External service error: {0}")]
    ExternalService(String),
}
```

### 錯誤處理策略

1. **資料庫錯誤**: 記錄詳細錯誤，回傳通用錯誤訊息給使用者
2. **驗證錯誤**: 提供具體的驗證失敗原因
3. **檔案處理錯誤**: 詳細說明檔案格式或內容問題
4. **通知錯誤**: 實作重試機制，記錄失敗原因
5. **外部服務錯誤**: 實作斷路器模式，提供降級服務

## 測試策略

### 測試層級

1. **單元測試**
   - 每個服務的核心邏輯
   - 資料模型驗證
   - 工具函數測試

2. **整合測試**
   - 資料庫操作測試
   - API 端點測試
   - 外部服務整合測試

3. **端到端測試**
   - 完整的使用者流程測試
   - 檔案上傳和處理流程
   - 通知發送流程

### 測試工具

- **單元測試**: Rust 內建 test framework
- **HTTP 測試**: axum-test
- **資料庫測試**: sqlx-test with test database
- **模擬服務**: mockall crate for mocking external services

### 測試資料管理

- 使用 SQLite in-memory database 進行測試
- 提供測試資料 fixtures
- 每個測試案例獨立的資料庫狀態

## 部署架構

### GCP 服務使用

1. **Cloud Run**: 容器化應用程式部署
2. **Cloud Storage**: 檔案儲存和備份
3. **Cloud SQL**: 生產環境資料庫（可選升級）
4. **Cloud Logging**: 集中化日誌管理
5. **Cloud Monitoring**: 系統監控和告警

### Docker 配置

```dockerfile
FROM rust:1.75 as builder
WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/pharmacy-system /usr/local/bin/pharmacy-system
EXPOSE 8080
CMD ["pharmacy-system"]
```

### 環境配置

- 使用環境變數管理配置
- 敏感資訊使用 GCP Secret Manager
- 支援多環境部署（開發、測試、生產）