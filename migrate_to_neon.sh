#!/bin/bash

echo "=== SQLite to Neon DB 遷移腳本 ==="
echo

# 檢查必要的工具
check_requirements() {
    echo "檢查必要工具..."
    
    if ! command -v python3 &> /dev/null; then
        echo "錯誤：需要安裝 python3"
        exit 1
    fi
    
    if ! command -v psql &> /dev/null; then
        echo "警告：未找到 psql，將嘗試使用 Python 腳本"
        USE_PYTHON=true
    else
        USE_PYTHON=false
    fi
    
    echo "工具檢查完成"
}

# 檢查環境變量
check_env() {
    echo "檢查環境變量..."
    
    if [ ! -f ".env" ]; then
        echo "錯誤：找不到 .env 文件"
        exit 1
    fi
    
    source .env
    
    if [ -z "$DATABASE_URL" ]; then
        echo "錯誤：在 .env 文件中找不到 DATABASE_URL"
        exit 1
    fi
    
    echo "環境變量檢查完成"
}

# 備份當前數據
backup_data() {
    echo "備份當前數據..."
    
    if [ -f "pharmacy.db" ]; then
        timestamp=$(date +"%Y%m%d_%H%M%S")
        cp pharmacy.db "pharmacy_backup_${timestamp}.db"
        echo "SQLite數據庫已備份到: pharmacy_backup_${timestamp}.db"
    else
        echo "警告：找不到 pharmacy.db 文件"
    fi
}

# 停止服務
stop_services() {
    echo "停止相關服務..."
    
    # 查找並停止可能運行的服務
    pkill -f "pharmacy-system" 2>/dev/null || true
    pkill -f "cargo run" 2>/dev/null || true
    
    echo "服務已停止"
}

# 運行數據庫遷移
run_migrations() {
    echo "運行數據庫遷移..."
    
    # 檢查是否安裝了 sqlx-cli
    if command -v sqlx &> /dev/null; then
        echo "使用 sqlx 運行遷移..."
        sqlx migrate run --database-url "$DATABASE_URL"
    else
        echo "未找到 sqlx，嘗試手動運行遷移..."
        
        # 手動運行遷移文件
        for migration_file in migrations/*.sql; do
            if [ -f "$migration_file" ]; then
                echo "執行遷移: $migration_file"
                if [ "$USE_PYTHON" = true ]; then
                    echo "請手動執行遷移文件: $migration_file"
                else
                    psql "$DATABASE_URL" -f "$migration_file"
                fi
            fi
        done
    fi
    
    echo "數據庫遷移完成"
}

# 遷移數據
migrate_data() {
    echo "遷移數據..."
    
    if [ "$USE_PYTHON" = true ]; then
        echo "使用 Python 腳本遷移數據..."
        
        # 檢查是否安裝了 psycopg2
        if python3 -c "import psycopg2" 2>/dev/null; then
            python3 migrate_to_neon.py
        else
            echo "未安裝 psycopg2，使用簡化腳本..."
            python3 simple_neon_migrate.py
            
            echo "請手動執行生成的 SQL 文件："
            echo "psql \$DATABASE_URL -f neon_migration.sql"
            read -p "按 Enter 繼續，或 Ctrl+C 取消..."
        fi
    else
        echo "使用完整的 Python 遷移腳本..."
        python3 migrate_to_neon.py
    fi
    
    echo "數據遷移完成"
}

# 驗證遷移
verify_migration() {
    echo "驗證遷移結果..."
    
    if [ "$USE_PYTHON" = false ]; then
        echo "檢查 PostgreSQL 數據..."
        psql "$DATABASE_URL" -c "
            SELECT 'users' as table_name, COUNT(*) as count FROM users
            UNION ALL
            SELECT 'products', COUNT(*) FROM products
            UNION ALL
            SELECT 'orders', COUNT(*) FROM orders
            UNION ALL
            SELECT 'order_items', COUNT(*) FROM order_items;
        "
    fi
    
    echo "驗證完成"
}

# 更新配置
update_config() {
    echo "更新配置..."
    
    # 確保 Cargo.toml 使用 PostgreSQL
    if grep -q "sqlite" Cargo.toml; then
        echo "警告：Cargo.toml 中仍包含 SQLite 依賴，請手動更新"
    fi
    
    echo "配置更新完成"
}

# 主函數
main() {
    echo "開始遷移過程..."
    echo
    
    check_requirements
    check_env
    backup_data
    stop_services
    run_migrations
    migrate_data
    verify_migration
    update_config
    
    echo
    echo "=== 遷移完成 ==="
    echo "你的數據已從 SQLite 遷移到 Neon DB！"
    echo
    echo "接下來的步驟："
    echo "1. 測試你的應用程序"
    echo "2. 如果一切正常，可以刪除舊的 SQLite 文件"
    echo "3. 更新你的部署配置以使用新的數據庫"
    echo
    echo "如果遇到問題，可以使用備份文件恢復："
    echo "ls pharmacy_backup_*.db"
}

# 執行主函數
main "$@"