#!/usr/bin/env python3
import psycopg2

DATABASE_URL = "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require"

def fix_nhi_structure():
    print("🔄 修正資料庫結構：使用 nhi_code 作為主鍵...")
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # 檢查現有狀態
        print("0. 檢查現有狀態...")
        cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'products' AND column_name IN ('nhi_code', 'nhi_price')")
        existing_columns = [row[0] for row in cursor.fetchall()]
        print(f"   products 表現有欄位: {existing_columns}")
        
        # 如果已經有 nhi_code 欄位，先清理
        if 'nhi_code' in existing_columns:
            print("1. 清理現有 nhi_code 欄位...")
            try:
                cursor.execute("ALTER TABLE products DROP CONSTRAINT IF EXISTS fk_products_nhi_code")
                cursor.execute("UPDATE products SET nhi_code = NULL")
                print("   ✅ nhi_code 欄位已清理")
            except Exception as e:
                print(f"   ⚠️  清理失敗: {e}")
        
        # 如果還有 nhi_price 欄位，也清理
        if 'nhi_price' in existing_columns:
            print("2. 清理 nhi_price 欄位...")
            try:
                cursor.execute("ALTER TABLE products DROP COLUMN nhi_price CASCADE")
                print("   ✅ nhi_price 欄位已移除")
            except Exception as e:
                print(f"   ⚠️  清理失敗: {e}")
        
        # 如果沒有 nhi_code 欄位，添加它
        if 'nhi_code' not in existing_columns:
            print("3. 添加 nhi_code 欄位...")
            cursor.execute("ALTER TABLE products ADD COLUMN nhi_code VARCHAR(20)")
            print("   ✅ nhi_code 欄位已添加")
        
        # 檢查 nhi_prices 表是否存在且結構正確
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_name = 'nhi_prices'")
        if not cursor.fetchone():
            print("4. 創建新的 nhi_prices 表...")
            cursor.execute("""
                CREATE TABLE nhi_prices (
                    nhi_code VARCHAR(20) PRIMARY KEY,
                    nhi_price DECIMAL(10,2) NOT NULL,
                    selling_price DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            cursor.execute("CREATE INDEX idx_nhi_price ON nhi_prices (nhi_price)")
            cursor.execute("CREATE INDEX idx_selling_price ON nhi_prices (selling_price)")
            print("   ✅ 新的 nhi_prices 表已創建")
        else:
            print("4. nhi_prices 表已存在")
        
        # 清空並重新插入測試數據
        print("5. 插入測試數據...")
        cursor.execute("DELETE FROM nhi_prices")
        test_data = [
            ('A001', 280.40, 280.40),
            ('A002', 320.00, 320.00), 
            ('A003', 220.30, 220.30),
            ('A004', 420.60, 420.60),
            ('A005', 180.90, 180.90),
        ]
        
        for nhi_code, nhi_price, selling_price in test_data:
            cursor.execute("""
                INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price)
                VALUES (%s, %s, %s)
            """, (nhi_code, nhi_price, selling_price))
        print(f"   ✅ 已插入 {len(test_data)} 筆測試數據")
        
        # 更新 products 表，設定 nhi_code 值
        print("6. 更新產品的 nhi_code...")
        price_mapping = {
            280.40: 'A001',
            320.00: 'A002',
            220.30: 'A003', 
            420.60: 'A004',
            180.90: 'A005'
        }
        
        for price, code in price_mapping.items():
            cursor.execute("UPDATE products SET nhi_code = %s WHERE unit_price = %s", (code, price))
        print("   ✅ 產品 nhi_code 已更新")
        
        # 添加外鍵約束
        print("7. 添加外鍵約束...")
        cursor.execute("""
            ALTER TABLE products 
            ADD CONSTRAINT fk_products_nhi_code 
            FOREIGN KEY (nhi_code) REFERENCES nhi_prices(nhi_code)
        """)
        print("   ✅ 外鍵約束已添加")
        
        # 檢查結果
        print("\n📊 檢查修正結果...")
        cursor.execute("SELECT nhi_code, nhi_price, selling_price FROM nhi_prices LIMIT 3")
        nhi_samples = cursor.fetchall()
        print("   nhi_prices 表樣本:")
        for code, nhi_price, selling_price in nhi_samples:
            print(f"     {code}: 健保價={nhi_price}, 賣價={selling_price}")
        
        cursor.execute("""
            SELECT p.name, p.unit_price, p.nhi_code, np.selling_price
            FROM products p
            LEFT JOIN nhi_prices np ON p.nhi_code = np.nhi_code
            WHERE p.nhi_code IS NOT NULL
            LIMIT 3
        """)
        products_samples = cursor.fetchall()
        print("   products 表關聯樣本:")
        for name, unit_price, nhi_code, selling_price in products_samples:
            print(f"     {name}: 單價={unit_price}, nhi_code={nhi_code}, 賣價={selling_price}")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 資料庫結構修正完成！現在使用 nhi_code 作為主鍵")
        return True
        
    except Exception as e:
        print(f"❌ 修正失敗: {e}")
        return False

if __name__ == "__main__":
    success = fix_nhi_structure()
    print(f"\n{'🎊 修正成功!' if success else '❌ 修正失敗'}")