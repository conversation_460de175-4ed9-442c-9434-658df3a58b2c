#!/usr/bin/env python3
"""
驗證遷移結果
"""

import sqlite3
import psycopg2
import os

def get_database_url():
    """從環境變量讀取數據庫連接信息"""
    env_vars = {}
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    return env_vars.get('DATABASE_URL')

def verify_migration():
    """驗證遷移結果"""
    print("=== 驗證遷移結果 ===")
    print()
    
    # 連接數據庫
    sqlite_conn = sqlite3.connect('pharmacy.db')
    database_url = get_database_url()
    pg_conn = psycopg2.connect(database_url)
    
    try:
        sqlite_cursor = sqlite_conn.cursor()
        pg_cursor = pg_conn.cursor()
        
        tables_to_verify = ['users', 'products', 'orders', 'order_items', 'carts', 'cart_items']
        
        print("表格數據對比：")
        print("-" * 50)
        
        total_sqlite = 0
        total_pg = 0
        
        for table in tables_to_verify:
            try:
                # SQLite計數
                sqlite_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                sqlite_count = sqlite_cursor.fetchone()[0]
                total_sqlite += sqlite_count
                
                # PostgreSQL計數
                pg_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                pg_count = pg_cursor.fetchone()[0]
                total_pg += pg_count
                
                status = "✓" if sqlite_count == pg_count else "✗"
                print(f"{table:15} | SQLite: {sqlite_count:3d} | PostgreSQL: {pg_count:3d} | {status}")
                
            except Exception as e:
                print(f"{table:15} | 錯誤: {e}")
        
        print("-" * 50)
        print(f"{'總計':15} | SQLite: {total_sqlite:3d} | PostgreSQL: {total_pg:3d} | {'✓' if total_sqlite == total_pg else '✗'}")
        
        print()
        print("PostgreSQL數據庫結構檢查：")
        print("-" * 30)
        
        # 檢查表格是否存在
        pg_cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        """)
        
        pg_tables = [row[0] for row in pg_cursor.fetchall()]
        print(f"PostgreSQL表格: {', '.join(pg_tables)}")
        
        print()
        print("樣本數據檢查：")
        print("-" * 20)
        
        # 檢查用戶數據
        pg_cursor.execute("SELECT username, pharmacy_name FROM users LIMIT 3")
        users = pg_cursor.fetchall()
        print("用戶樣本:")
        for user in users:
            print(f"  - {user[0]} ({user[1]})")
        
        # 檢查產品數據
        pg_cursor.execute("SELECT name, manufacturer FROM products LIMIT 3")
        products = pg_cursor.fetchall()
        print("產品樣本:")
        for product in products:
            print(f"  - {product[0]} ({product[1]})")
        
        # 檢查訂單數據
        pg_cursor.execute("SELECT order_number, status, total_amount FROM orders LIMIT 3")
        orders = pg_cursor.fetchall()
        print("訂單樣本:")
        for order in orders:
            print(f"  - {order[0]} ({order[1]}, ${order[2]})")
        
        print()
        if total_sqlite == total_pg:
            print("🎉 遷移成功！所有數據已正確遷移到Neon DB")
        else:
            print("⚠️  遷移可能有問題，請檢查上述對比結果")
        
    except Exception as e:
        print(f"驗證過程中發生錯誤: {e}")
    finally:
        sqlite_conn.close()
        pg_conn.close()

if __name__ == "__main__":
    verify_migration()