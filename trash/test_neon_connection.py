#!/usr/bin/env python3
"""
測試Neon DB連接和基本功能
"""

import psycopg2
import os
from datetime import datetime

def get_database_url():
    """從環境變量讀取數據庫連接信息"""
    env_vars = {}
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    return env_vars.get('DATABASE_URL')

def test_connection():
    """測試數據庫連接"""
    print("=== Neon DB 連接測試 ===")
    print()
    
    database_url = get_database_url()
    
    try:
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        print("✓ 數據庫連接成功")
        
        # 測試基本查詢
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✓ PostgreSQL版本: {version}")
        
        # 測試表格存在性
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('users', 'products', 'orders', 'order_items', 'carts', 'cart_items')
            ORDER BY table_name;
        """)
        
        tables = [row[0] for row in cursor.fetchall()]
        print(f"✓ 核心表格存在: {', '.join(tables)}")
        
        # 測試數據完整性
        test_queries = [
            ("用戶數量", "SELECT COUNT(*) FROM users"),
            ("產品數量", "SELECT COUNT(*) FROM products"),
            ("訂單數量", "SELECT COUNT(*) FROM orders"),
            ("訂單項目數量", "SELECT COUNT(*) FROM order_items"),
            ("購物車數量", "SELECT COUNT(*) FROM carts"),
            ("購物車項目數量", "SELECT COUNT(*) FROM cart_items")
        ]
        
        print("\n數據統計:")
        for name, query in test_queries:
            cursor.execute(query)
            count = cursor.fetchone()[0]
            print(f"  {name}: {count}")
        
        # 測試關聯查詢
        print("\n關聯查詢測試:")
        
        # 測試用戶-訂單關聯
        cursor.execute("""
            SELECT u.username, COUNT(o.id) as order_count
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id
            GROUP BY u.id, u.username
            HAVING COUNT(o.id) > 0
            ORDER BY order_count DESC
            LIMIT 3;
        """)
        
        user_orders = cursor.fetchall()
        print("  用戶訂單統計:")
        for username, order_count in user_orders:
            print(f"    {username}: {order_count} 個訂單")
        
        # 測試產品-訂單項目關聯
        cursor.execute("""
            SELECT p.name, SUM(oi.quantity) as total_sold
            FROM products p
            JOIN order_items oi ON p.id = oi.product_id
            GROUP BY p.id, p.name
            ORDER BY total_sold DESC
            LIMIT 3;
        """)
        
        product_sales = cursor.fetchall()
        print("  熱銷產品:")
        for product_name, total_sold in product_sales:
            print(f"    {product_name}: 售出 {total_sold} 個")
        
        # 測試寫入操作
        print("\n寫入測試:")
        
        # 創建測試用戶
        test_username = f"migration_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute("""
            INSERT INTO users (username, email, password_hash, pharmacy_name)
            VALUES (%s, %s, %s, %s)
            RETURNING id;
        """, (test_username, f"{test_username}@test.com", "test_hash", "遷移測試藥局"))
        
        test_user_id = cursor.fetchone()[0]
        print(f"  ✓ 創建測試用戶: ID {test_user_id}")
        
        # 刪除測試用戶
        cursor.execute("DELETE FROM users WHERE id = %s", (test_user_id,))
        print(f"  ✓ 刪除測試用戶: ID {test_user_id}")
        
        conn.commit()
        
        print("\n🎉 所有測試通過！Neon DB已準備就緒")
        print("\n接下來的步驟:")
        print("1. 停止使用SQLite的應用程序")
        print("2. 確保Cargo.toml中使用postgres特性")
        print("3. 重新編譯並啟動你的Rust應用程序")
        print("4. 測試所有功能是否正常工作")
        print("5. 如果一切正常，可以備份並刪除舊的SQLite文件")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    test_connection()