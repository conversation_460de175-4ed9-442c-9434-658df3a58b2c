-- 完整資料庫遷移腳本
-- 執行此腳本來建立所有缺少的資料表和索引

-- ============================================
-- 第一步：建立缺少的基礎表格
-- ============================================

-- 建立通知偏好設定表格
CREATE TABLE IF NOT EXISTS notification_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    email_enabled BOOLEAN NOT NULL DEFAULT true,
    line_enabled BOOLEAN NOT NULL DEFAULT false,
    line_user_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 建立通知記錄表格
CREATE TABLE IF NOT EXISTS notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    type VARCHAR(20) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 建立備份記錄表格
CREATE TABLE IF NOT EXISTS backup_logs (
    id SERIAL PRIMARY KEY,
    backup_file VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    status VARCHAR(20) NOT NULL,
    uploaded_to_gcp BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================
-- 第二步：建立購物車相關表格
-- ============================================

-- 建立購物車表格
CREATE TABLE IF NOT EXISTS carts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE(user_id) -- 每個使用者只能有一個購物車
);

-- 建立購物車項目表格
CREATE TABLE IF NOT EXISTS cart_items (
    id SERIAL PRIMARY KEY,
    cart_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cart_id) REFERENCES carts(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    UNIQUE(cart_id, product_id) -- 每個購物車中每個產品只能有一個項目
);

-- ============================================
-- 第三步：建立角色權限系統
-- ============================================

-- 創建角色表格
CREATE TABLE IF NOT EXISTS roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 創建權限表格
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(50) NOT NULL, -- 資源類型，如 'products', 'orders', 'users' 等
    action VARCHAR(50) NOT NULL,   -- 操作類型，如 'create', 'read', 'update', 'delete'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 創建角色權限關聯表格
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE(role_id, permission_id)
);

-- 為 users 表格添加 role_id 欄位（如果不存在）
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='role_id') THEN
        ALTER TABLE users ADD COLUMN role_id INTEGER;
        ALTER TABLE users ADD CONSTRAINT fk_users_role FOREIGN KEY (role_id) REFERENCES roles(id);
    END IF;
END $$;

-- ============================================
-- 第四步：插入預設角色和權限
-- ============================================

-- 插入預設角色
INSERT INTO roles (name, description) VALUES 
    ('super_admin', '超級管理員 - 擁有所有權限'),
    ('admin', '管理員 - 可以管理產品和訂單'),
    ('pharmacy', '藥局用戶 - 可以下訂單和查看自己的資料'),
    ('viewer', '檢視者 - 只能查看資料')
ON CONFLICT (name) DO NOTHING;

-- 插入基本權限
INSERT INTO permissions (name, description, resource, action) VALUES 
    -- 產品管理權限
    ('products.create', '創建產品', 'products', 'create'),
    ('products.read', '查看產品', 'products', 'read'),
    ('products.update', '更新產品', 'products', 'update'),
    ('products.delete', '刪除產品', 'products', 'delete'),
    
    -- 訂單管理權限
    ('orders.create', '創建訂單', 'orders', 'create'),
    ('orders.read', '查看訂單', 'orders', 'read'),
    ('orders.update', '更新訂單', 'orders', 'update'),
    ('orders.delete', '刪除訂單', 'orders', 'delete'),
    ('orders.read_all', '查看所有用戶訂單', 'orders', 'read_all'),
    
    -- 用戶管理權限
    ('users.create', '創建用戶', 'users', 'create'),
    ('users.read', '查看用戶', 'users', 'read'),
    ('users.update', '更新用戶', 'users', 'update'),
    ('users.delete', '刪除用戶', 'users', 'delete'),
    ('users.read_all', '查看所有用戶', 'users', 'read_all'),
    
    -- 系統管理權限
    ('system.backup', '系統備份', 'system', 'backup'),
    ('system.settings', '系統設定', 'system', 'settings'),
    ('system.logs', '查看系統日誌', 'system', 'logs'),
    
    -- 角色和權限管理
    ('roles.create', '創建角色', 'roles', 'create'),
    ('roles.read', '查看角色', 'roles', 'read'),
    ('roles.update', '更新角色', 'roles', 'update'),
    ('roles.delete', '刪除角色', 'roles', 'delete'),
    
    -- 通知權限
    ('notifications.read', '查看通知', 'notifications', 'read'),
    ('notifications.send', '發送通知', 'notifications', 'send')
ON CONFLICT (name) DO NOTHING;

-- 設置角色權限關聯

-- 超級管理員 - 擁有所有權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'super_admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 管理員 - 產品、訂單、部分用戶管理權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'admin' AND p.name IN (
    'products.create', 'products.read', 'products.update', 'products.delete',
    'orders.create', 'orders.read', 'orders.update', 'orders.delete', 'orders.read_all',
    'users.read', 'users.read_all',
    'notifications.read', 'notifications.send'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 藥局用戶 - 基本訂單和產品查看權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'pharmacy' AND p.name IN (
    'products.read',
    'orders.create', 'orders.read', 'orders.update',
    'notifications.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 檢視者 - 只能查看
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'viewer' AND p.name IN (
    'products.read',
    'orders.read',
    'notifications.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 為現有用戶設置預設角色（pharmacy）
UPDATE users SET role_id = (SELECT id FROM roles WHERE name = 'pharmacy') WHERE role_id IS NULL;

-- 將 role_id 設為 NOT NULL（在設置預設值後）
DO $$ 
BEGIN 
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='role_id' AND is_nullable='YES') THEN
        ALTER TABLE users ALTER COLUMN role_id SET NOT NULL;
    END IF;
END $$;

-- ============================================
-- 第五步：建立所有必要的索引
-- ============================================

-- 基礎索引
CREATE INDEX IF NOT EXISTS idx_products_nhi_code ON products(nhi_code);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_stock_quantity ON products(stock_quantity);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);

CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_user_status ON orders(user_id, status);

CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);

-- 購物車索引
CREATE INDEX IF NOT EXISTS idx_carts_user_id ON carts(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_id ON cart_items(cart_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_product_id ON cart_items(product_id);

-- 通知索引
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);

-- 角色權限索引
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);

-- 複合索引，用於優化常見的多條件查詢
CREATE INDEX IF NOT EXISTS idx_products_stock_price ON products(stock_quantity, unit_price) WHERE stock_quantity > 0;
CREATE INDEX IF NOT EXISTS idx_orders_user_date ON orders(user_id, created_at);

-- ============================================
-- 完成訊息
-- ============================================

-- 顯示完成訊息（這在某些資料庫中可能不支援，可以忽略錯誤）
DO $$ 
BEGIN 
    RAISE NOTICE '資料庫遷移完成！';
    RAISE NOTICE '已建立的表格：';
    RAISE NOTICE '- users (使用者)';
    RAISE NOTICE '- products (產品)';
    RAISE NOTICE '- orders (訂單)';
    RAISE NOTICE '- order_items (訂單項目)';
    RAISE NOTICE '- carts (購物車)';
    RAISE NOTICE '- cart_items (購物車項目)';
    RAISE NOTICE '- roles (角色)';
    RAISE NOTICE '- permissions (權限)';
    RAISE NOTICE '- role_permissions (角色權限關聯)';
    RAISE NOTICE '- notifications (通知)';
    RAISE NOTICE '- notification_preferences (通知偏好)';
    RAISE NOTICE '- backup_logs (備份記錄)';
END $$;