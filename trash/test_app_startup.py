#!/usr/bin/env python3
"""
測試應用程序啟動和基本功能
"""

import subprocess
import time
import requests
import signal
import os

def test_app_startup():
    """測試應用程序啟動"""
    print("=== 測試應用程序啟動 ===")
    print()
    
    # 啟動應用程序
    print("正在啟動應用程序...")
    env = os.environ.copy()
    env['RUST_LOG'] = 'info'
    
    process = subprocess.Popen(
        ['cargo', 'run', '--bin', 'pharmacy-system'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        env=env,
        text=True
    )
    
    # 等待應用程序啟動
    startup_timeout = 30
    for i in range(startup_timeout):
        try:
            # 嘗試連接健康檢查端點
            response = requests.get('http://localhost:8080/health', timeout=2)
            if response.status_code == 200:
                print(f"✓ 應用程序在 {i+1} 秒後成功啟動")
                print(f"✓ 健康檢查響應: {response.text}")
                break
        except requests.exceptions.RequestException:
            pass
        
        # 檢查進程是否還在運行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ 應用程序啟動失敗")
            print("STDOUT:")
            print(stdout[-1000:])  # 最後1000字符
            print("STDERR:")
            print(stderr[-1000:])  # 最後1000字符
            return False
        
        time.sleep(1)
        if i % 5 == 4:
            print(f"  等待中... ({i+1}/{startup_timeout})")
    else:
        print("❌ 應用程序啟動超時")
        process.terminate()
        return False
    
    # 測試基本API端點
    print("\n測試基本API端點:")
    
    test_endpoints = [
        ('GET', '/health', '健康檢查'),
        ('GET', '/api/products', '產品列表'),
        ('GET', '/api/users/me', '用戶信息（需要認證）')
    ]
    
    for method, endpoint, description in test_endpoints:
        try:
            url = f'http://localhost:8080{endpoint}'
            if method == 'GET':
                response = requests.get(url, timeout=5)
            
            print(f"  {description}: {response.status_code}")
            if response.status_code < 500:
                print(f"    ✓ 端點響應正常")
            else:
                print(f"    ❌ 服務器錯誤")
                
        except requests.exceptions.RequestException as e:
            print(f"  {description}: 連接失敗 ({e})")
    
    # 停止應用程序
    print("\n正在停止應用程序...")
    process.terminate()
    try:
        process.wait(timeout=10)
        print("✓ 應用程序已正常停止")
    except subprocess.TimeoutExpired:
        process.kill()
        print("✓ 應用程序已強制停止")
    
    return True

def main():
    success = test_app_startup()
    
    print("\n=== 測試結果 ===")
    if success:
        print("🎉 應用程序可以正常啟動並響應請求！")
        print("\n你的Neon DB遷移已完成，應用程序運行正常。")
        print("\n建議的後續步驟:")
        print("1. 進行完整的功能測試")
        print("2. 檢查所有API端點是否正常工作")
        print("3. 測試用戶註冊、登錄、下訂單等核心功能")
        print("4. 備份舊的SQLite文件到安全位置")
        print("5. 更新生產環境配置")
    else:
        print("❌ 應用程序啟動測試失敗")
        print("\n請檢查:")
        print("1. Neon DB連接配置是否正確")
        print("2. 數據庫遷移是否完成")
        print("3. 應用程序日誌中的錯誤信息")

if __name__ == "__main__":
    main()