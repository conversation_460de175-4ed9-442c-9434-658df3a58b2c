#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def check_roles_table():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔍 檢查 roles 表...")
    
    try:
        # 檢查 roles 表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'roles'
            );
        """)
        roles_exists = cursor.fetchone()[0]
        print(f"📋 roles 表存在: {roles_exists}")
        
        if roles_exists:
            # 檢查 roles 表結構
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'roles' 
                ORDER BY ordinal_position
            """)
            columns = cursor.fetchall()
            
            print("\n📋 roles 表欄位結構:")
            for col in columns:
                print(f"  - {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
            
            # 檢查 roles 資料
            cursor.execute("SELECT * FROM roles")
            roles_data = cursor.fetchall()
            print(f"\n📊 roles 表資料筆數: {len(roles_data)}")
            for role in roles_data:
                print(f"  - {role}")
        else:
            print("❌ roles 表不存在，需要建立")
            
            # 建立 roles 表
            cursor.execute("""
                CREATE TABLE roles (
                    id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                    name VARCHAR(50) UNIQUE NOT NULL,
                    description TEXT,
                    created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                    updated_at TIMESTAMPTZ DEFAULT current_timestamp()
                )
            """)
            print("✅ 建立 roles 表")
            
            # 插入預設角色
            default_roles = [
                ('super_admin', '超級管理員'),
                ('admin', '管理員'),
                ('pharmacy', '藥局'),
                ('staff', '員工')
            ]
            
            for role_name, description in default_roles:
                cursor.execute("""
                    INSERT INTO roles (name, description)
                    VALUES (%s, %s)
                """, (role_name, description))
            
            print("✅ 插入預設角色")
        
        conn.commit()
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_roles_table() 