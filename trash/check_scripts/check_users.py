#!/usr/bin/env python3
"""
檢查用戶數據差異
"""

import sqlite3
import psycopg2
import os

def get_database_url():
    """從環境變量讀取數據庫連接信息"""
    env_vars = {}
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    return env_vars.get('DATABASE_URL')

def check_users():
    """檢查用戶數據差異"""
    print("檢查用戶數據差異...")
    
    # 連接數據庫
    sqlite_conn = sqlite3.connect('pharmacy.db')
    database_url = get_database_url()
    pg_conn = psycopg2.connect(database_url)
    
    try:
        sqlite_cursor = sqlite_conn.cursor()
        pg_cursor = pg_conn.cursor()
        
        print("SQLite用戶:")
        sqlite_cursor.execute("SELECT id, username, pharmacy_name FROM users ORDER BY id")
        sqlite_users = sqlite_cursor.fetchall()
        for user in sqlite_users:
            print(f"  ID: {user[0]}, 用戶名: {user[1]}, 藥局: {user[2]}")
        
        print("\nPostgreSQL用戶:")
        pg_cursor.execute("SELECT id, username, pharmacy_name FROM users ORDER BY id")
        pg_users = pg_cursor.fetchall()
        for user in pg_users:
            print(f"  ID: {user[0]}, 用戶名: {user[1]}, 藥局: {user[2]}")
        
        # 找出差異
        sqlite_ids = {user[0] for user in sqlite_users}
        pg_ids = {user[0] for user in pg_users}
        
        extra_in_pg = pg_ids - sqlite_ids
        missing_in_pg = sqlite_ids - pg_ids
        
        if extra_in_pg:
            print(f"\nPostgreSQL中多出的用戶ID: {extra_in_pg}")
        if missing_in_pg:
            print(f"\nPostgreSQL中缺少的用戶ID: {missing_in_pg}")
        
        if not extra_in_pg and not missing_in_pg:
            print("\n用戶ID完全匹配，可能是重複插入的問題")
        
    except Exception as e:
        print(f"檢查過程中發生錯誤: {e}")
    finally:
        sqlite_conn.close()
        pg_conn.close()

if __name__ == "__main__":
    check_users()