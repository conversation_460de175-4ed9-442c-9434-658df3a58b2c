#!/usr/bin/env python3
import psycopg2
import os

def check_products_table():
    # 從環境變量讀取數據庫URL
    database_url = "postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require"
    
    try:
        # 連接數據庫
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        # 檢查 products 表格結構
        print("🔍 檢查 products 表格結構...")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'products'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        if columns:
            print("📋 Products 表格欄位:")
            for col in columns:
                print(f"  - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")
        else:
            print("❌ Products 表格不存在!")
            return
            
        # 檢查是否有數據
        cursor.execute("SELECT COUNT(*) FROM products;")
        count = cursor.fetchone()[0]
        print(f"📊 Products 表格有 {count} 筆記錄")
        
        # 查看前幾筆數據
        if count > 0:
            cursor.execute("SELECT id, nhi_code, name, manufacturer, is_active FROM products LIMIT 3;")
            rows = cursor.fetchall()
            print("🔍 前3筆產品資料:")
            for row in rows:
                print(f"  ID: {row[0]}, NHI: {row[1]}, 名稱: {row[2]}, 廠商: {row[3]}, 狀態: {row[4]}")
        
        # 測試我們的查詢
        print("\n🧪 測試產品查詢...")
        cursor.execute("""
            SELECT id, nhi_code, name, manufacturer, unit, unit_price, stock_quantity, description, is_active, created_at, updated_at
            FROM products
            WHERE is_active = %s
            ORDER BY created_at DESC 
            LIMIT %s OFFSET %s
        """, (True, 50, 0))
        
        products = cursor.fetchall()
        print(f"✅ 查詢成功！找到 {len(products)} 筆活躍產品")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")

if __name__ == "__main__":
    check_products_table()