#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def final_check_all_tables():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔍 最終檢查所有表...")
    
    try:
        # 獲取所有表
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        tables = cursor.fetchall()
        
        print(f"📋 找到 {len(tables)} 個表:")
        
        for table in tables:
            table_name = table[0]
            print(f"\n📊 表: {table_name}")
            
            # 檢查欄位
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = %s 
                ORDER BY ordinal_position
            """, (table_name,))
            columns = cursor.fetchall()
            
            print("  欄位:")
            for col in columns:
                print(f"    - {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
            
            # 檢查主鍵
            cursor.execute("""
                SELECT kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name
                WHERE tc.table_name = %s 
                    AND tc.constraint_type = 'PRIMARY KEY'
                ORDER BY kcu.ordinal_position
            """, (table_name,))
            primary_keys = cursor.fetchall()
            print(f"  主鍵: {[pk[0] for pk in primary_keys]}")
            
            # 檢查是否有 id 欄位且沒有 unique_rowid() 預設值
            id_column = None
            for col in columns:
                if col[0] == 'id':
                    id_column = col
                    break
            
            if id_column and not id_column[3] or 'unique_rowid' not in str(id_column[3]):
                print(f"  ⚠️  {table_name} 表的 id 欄位沒有 unique_rowid() 預設值")
                print(f"      當前預設值: {id_column[3]}")
                
                # 修復這個表
                print(f"  🔧 修復 {table_name} 表...")
                
                # 備份資料
                cursor.execute(f"SELECT * FROM {table_name}")
                data = cursor.fetchall()
                print(f"    📦 備份 {len(data)} 筆資料")
                
                # 獲取欄位名稱（除了 id）
                cursor.execute(f"""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = '{table_name}' 
                    AND column_name != 'id'
                    ORDER BY ordinal_position
                """)
                column_names = [row[0] for row in cursor.fetchall()]
                
                # 刪除舊表
                cursor.execute(f"DROP TABLE IF EXISTS {table_name} CASCADE")
                
                # 重新建立表（使用 CockroachDB 語法）
                if table_name == 'users':
                    cursor.execute("""
                        CREATE TABLE users (
                            id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                            username VARCHAR(50) UNIQUE NOT NULL,
                            email VARCHAR(100) UNIQUE NOT NULL,
                            password_hash VARCHAR(255) NOT NULL,
                            pharmacy_name VARCHAR(200) NOT NULL,
                            phone VARCHAR(20),
                            line_user_id VARCHAR(100),
                            notification_email BOOLEAN NOT NULL DEFAULT true,
                            notification_line BOOLEAN NOT NULL DEFAULT false,
                            role_id INT8 NOT NULL DEFAULT 3,
                            created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                            updated_at TIMESTAMPTZ DEFAULT current_timestamp()
                        )
                    """)
                elif table_name == 'products':
                    cursor.execute("""
                        CREATE TABLE products (
                            id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                            nhi_code VARCHAR(20) UNIQUE NOT NULL,
                            name VARCHAR(200) NOT NULL,
                            manufacturer VARCHAR(100) NOT NULL,
                            unit VARCHAR(20) NOT NULL,
                            unit_price DECIMAL(10,2) NOT NULL,
                            stock_quantity INT8 NOT NULL DEFAULT 0,
                            description TEXT,
                            is_active BOOLEAN NOT NULL DEFAULT true,
                            created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                            updated_at TIMESTAMPTZ DEFAULT current_timestamp()
                        )
                    """)
                elif table_name == 'orders':
                    cursor.execute("""
                        CREATE TABLE orders (
                            id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                            order_number VARCHAR(50) UNIQUE NOT NULL,
                            user_id INT8 NOT NULL,
                            status VARCHAR(20) NOT NULL DEFAULT 'pending',
                            total_amount DECIMAL(10,2) NOT NULL,
                            notes TEXT,
                            created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                            updated_at TIMESTAMPTZ DEFAULT current_timestamp()
                        )
                    """)
                elif table_name == 'order_items':
                    cursor.execute("""
                        CREATE TABLE order_items (
                            id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                            order_id INT8 NOT NULL,
                            product_id INT8 NOT NULL,
                            quantity INT8 NOT NULL,
                            unit_price DECIMAL(10,2) NOT NULL,
                            subtotal DECIMAL(10,2) NOT NULL,
                            created_at TIMESTAMPTZ DEFAULT current_timestamp()
                        )
                    """)
                elif table_name == 'carts':
                    cursor.execute("""
                        CREATE TABLE carts (
                            id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                            user_id INT8 NOT NULL,
                            created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                            updated_at TIMESTAMPTZ DEFAULT current_timestamp()
                        )
                    """)
                elif table_name == 'cart_items':
                    cursor.execute("""
                        CREATE TABLE cart_items (
                            id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                            cart_id INT8 NOT NULL,
                            product_id INT8 NOT NULL,
                            quantity INT8 NOT NULL,
                            unit_price DECIMAL(10,2) NOT NULL,
                            subtotal DECIMAL(10,2) NOT NULL,
                            created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                            updated_at TIMESTAMPTZ DEFAULT current_timestamp()
                        )
                    """)
                else:
                    print(f"    ⚠️  跳過 {table_name} 表的修復（未定義結構）")
                    continue
                
                print(f"    ✅ 重新建立 {table_name} 表")
                
                # 恢復資料（跳過 id 欄位）
                if data:
                    placeholders = ', '.join(['%s'] * len(column_names))
                    column_list = ', '.join(column_names)
                    
                    for row in data:
                        values = row[1:]  # 跳過 id
                        cursor.execute(f"""
                            INSERT INTO {table_name} ({column_list})
                            VALUES ({placeholders})
                        """, values)
                    
                    print(f"    ✅ 恢復 {len(data)} 筆資料")
                
                # 測試插入
                try:
                    if table_name == 'users':
                        cursor.execute("""
                            INSERT INTO users (username, email, password_hash, pharmacy_name)
                            VALUES ('test_final', '<EMAIL>', 'hash', 'test')
                            RETURNING id
                        """)
                    elif table_name == 'products':
                        cursor.execute("""
                            INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, stock_quantity)
                            VALUES ('FINAL123', 'Final Test Product', 'Final Manufacturer', '顆', 100.00, 100)
                            RETURNING id
                        """)
                    elif table_name == 'orders':
                        cursor.execute("""
                            INSERT INTO orders (order_number, user_id, total_amount)
                            VALUES ('FINAL-001', 1, 100.00)
                            RETURNING id
                        """)
                    elif table_name == 'carts':
                        cursor.execute("""
                            INSERT INTO carts (user_id)
                            VALUES (1)
                            RETURNING id
                        """)
                    
                    result = cursor.fetchone()
                    print(f"    ✅ 測試插入成功: {result}")
                    
                    # 清理測試資料
                    if table_name == 'users':
                        cursor.execute("DELETE FROM users WHERE username = 'test_final'")
                    elif table_name == 'products':
                        cursor.execute("DELETE FROM products WHERE nhi_code = 'FINAL123'")
                    elif table_name == 'orders':
                        cursor.execute("DELETE FROM orders WHERE order_number = 'FINAL-001'")
                    elif table_name == 'carts':
                        cursor.execute("DELETE FROM carts WHERE user_id = 1 AND id = %s", (result[0],))
                    
                except Exception as e:
                    print(f"    ❌ 測試插入失敗: {e}")
            else:
                print(f"  ✅ {table_name} 表正常")
        
        conn.commit()
        print("\n🎉 所有表檢查完成！")
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    final_check_all_tables() 