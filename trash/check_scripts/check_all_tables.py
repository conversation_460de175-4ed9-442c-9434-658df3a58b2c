#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def check_all_tables():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔍 檢查所有表的結構...")
    
    try:
        # 獲取所有表
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        tables = cursor.fetchall()
        
        print(f"📋 找到 {len(tables)} 個表:")
        for table in tables:
            table_name = table[0]
            print(f"\n📊 表: {table_name}")
            
            # 檢查欄位
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = %s 
                ORDER BY ordinal_position
            """, (table_name,))
            columns = cursor.fetchall()
            
            print("  欄位:")
            for col in columns:
                print(f"    - {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
            
            # 檢查主鍵
            cursor.execute("""
                SELECT kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name
                WHERE tc.table_name = %s 
                    AND tc.constraint_type = 'PRIMARY KEY'
                ORDER BY kcu.ordinal_position
            """, (table_name,))
            primary_keys = cursor.fetchall()
            print(f"  主鍵: {[pk[0] for pk in primary_keys]}")
            
            # 測試插入（如果表有 id 欄位）
            if any(col[0] == 'id' for col in columns):
                try:
                    if table_name == 'users':
                        cursor.execute("""
                            INSERT INTO users (username, email, password_hash, pharmacy_name)
                            VALUES ('test_check', '<EMAIL>', 'hash', 'test')
                            RETURNING id
                        """)
                    elif table_name == 'products':
                        cursor.execute("""
                            INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, stock_quantity)
                            VALUES ('CHECK123', 'Check Product', 'Check Manufacturer', '顆', 100.00, 100)
                            RETURNING id
                        """)
                    else:
                        print(f"    ⚠️  跳過 {table_name} 的插入測試")
                        continue
                    
                    result = cursor.fetchone()
                    print(f"    ✅ 插入測試成功: {result}")
                    
                    # 清理測試資料
                    if table_name == 'users':
                        cursor.execute("DELETE FROM users WHERE username = 'test_check'")
                    elif table_name == 'products':
                        cursor.execute("DELETE FROM products WHERE nhi_code = 'CHECK123'")
                    
                except Exception as e:
                    print(f"    ❌ 插入測試失敗: {e}")
        
        conn.commit()
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_all_tables() 