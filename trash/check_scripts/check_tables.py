#!/usr/bin/env python3
import psycopg2

def check_all_tables():
    database_url = "postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require"
    
    try:
        print("連接到 CockroachDB...")
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        # 檢查所有表
        print("\n=== 檢查所有表 ===")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        print(f"找到 {len(tables)} 個表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 檢查是否有 roles, permissions, role_permissions 表
        role_tables = ['roles', 'permissions', 'role_permissions']
        existing_role_tables = [t[0] for t in tables if t[0] in role_tables]
        
        print(f"\n角色相關表狀態:")
        for table in role_tables:
            if table in existing_role_tables:
                print(f"  ✅ {table} - 存在")
            else:
                print(f"  ❌ {table} - 不存在")
        
        # 檢查 users 表結構
        if any(t[0] == 'users' for t in tables):
            print(f"\n=== Users 表結構 ===")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'users' AND table_schema = 'public'
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            has_role_id = False
            for col in columns:
                print(f"  - {col[0]} ({col[1]}) {'NULL' if col[2]=='YES' else 'NOT NULL'}")
                if col[0] == 'role_id':
                    has_role_id = True
            
            if not has_role_id:
                print("  ❌ role_id 欄位不存在！")
            else:
                print("  ✅ role_id 欄位存在")
        
        # 檢查資料庫版本
        print(f"\n=== 資料庫資訊 ===")
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        print(f"資料庫版本: {version}")
        
    except psycopg2.Error as e:
        print(f"❌ 資料庫連接或查詢錯誤: {e}")
    except Exception as e:
        print(f"❌ 其他錯誤: {e}")
    finally:
        if 'conn' in locals():
            conn.close()
            print("\n資料庫連接已關閉")

if __name__ == "__main__":
    check_all_tables()