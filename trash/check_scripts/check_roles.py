#!/usr/bin/env python3

import psycopg2
import os
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

def check_database():
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("錯誤：找不到 DATABASE_URL")
        return
    
    try:
        print(f"連接到資料庫: {database_url}")
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        # 檢查是否有role相關資料表
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('roles', 'permissions', 'role_permissions')
            ORDER BY table_name;
        """)
        
        role_tables = cur.fetchall()
        print(f"找到的role相關資料表: {[table[0] for table in role_tables]}")
        
        # 檢查users表是否有role_id欄位
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'role_id';
        """)
        
        role_id_column = cur.fetchall()
        print(f"users表中是否有role_id欄位: {'是' if role_id_column else '否'}")
        
        # 如果有roles表，檢查資料
        if any('roles' in table for table in role_tables):
            cur.execute("SELECT id, name, description FROM roles ORDER BY id;")
            roles = cur.fetchall()
            print(f"roles表中的資料:")
            for role in roles:
                print(f"  - ID: {role[0]}, Name: {role[1]}, Description: {role[2]}")
        
        # 如果有permissions表，檢查幾個關鍵權限
        if any('permissions' in table for table in role_tables):
            cur.execute("SELECT name FROM permissions WHERE name LIKE 'orders.%' ORDER BY name;")
            order_permissions = cur.fetchall()
            print(f"orders相關權限: {[p[0] for p in order_permissions]}")
        
        # 檢查現有用戶的role_id
        if role_id_column:
            cur.execute("SELECT username, role_id FROM users WHERE username IN ('admin', 'demo', 'demo123', 'cats8727') ORDER BY username;")
            users = cur.fetchall()
            print(f"主要用戶的role_id:")
            for user in users:
                print(f"  - {user[0]}: role_id={user[1]}")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"資料庫連接錯誤: {e}")

if __name__ == "__main__":
    check_database()