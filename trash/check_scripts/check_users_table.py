#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def check_users_table():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔍 檢查 users 表結構...")
    
    # 檢查表是否存在
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'users'
        );
    """)
    table_exists = cursor.fetchone()[0]
    print(f"✅ users 表存在: {table_exists}")
    
    if table_exists:
        # 檢查欄位結構
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'users' 
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print("\n📋 users 表欄位結構:")
        column_names = []
        for col in columns:
            column_names.append(col[0])
            print(f"  - {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
        
        # 檢查是否有 role_id 欄位
        if 'role_id' in column_names:
            print("✅ 有 role_id 欄位")
        else:
            print("❌ 缺少 role_id 欄位")
        
        # 檢查主鍵
        cursor.execute("""
            SELECT kcu.column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            WHERE tc.table_name = 'users' 
                AND tc.constraint_type = 'PRIMARY KEY';
        """)
        primary_keys = cursor.fetchall()
        print(f"\n🔑 主鍵欄位: {[pk[0] for pk in primary_keys]}")
        
        # 檢查是否有資料
        cursor.execute("SELECT COUNT(*) FROM users")
        count = cursor.fetchone()[0]
        print(f"📊 users 表資料筆數: {count}")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    check_users_table() 