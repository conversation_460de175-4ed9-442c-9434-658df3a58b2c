#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def add_role_id_to_users():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔧 為 users 表添加 role_id 欄位...")
    
    try:
        # 檢查是否已經有 role_id 欄位
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_name = 'users' 
                AND column_name = 'role_id'
            );
        """)
        has_role_id = cursor.fetchone()[0]
        
        if has_role_id:
            print("✅ role_id 欄位已存在")
        else:
            # 添加 role_id 欄位
            cursor.execute("""
                ALTER TABLE users 
                ADD COLUMN role_id BIGINT NOT NULL DEFAULT 3
            """)
            print("✅ 成功添加 role_id 欄位")
            
            # 為現有用戶設置預設角色 (id=3 是 pharmacy 角色)
            cursor.execute("""
                UPDATE users 
                SET role_id = 3 
                WHERE role_id IS NULL OR role_id = 0
            """)
            print("✅ 已為現有用戶設置預設角色")
        
        conn.commit()
        print("🎉 完成！")
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    add_role_id_to_users() 