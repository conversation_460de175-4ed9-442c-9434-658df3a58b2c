#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def check_permissions_tables():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔍 檢查權限相關表...")
    
    try:
        # 檢查 permissions 表
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'permissions'
            );
        """)
        permissions_exists = cursor.fetchone()[0]
        print(f"📋 permissions 表存在: {permissions_exists}")
        
        if not permissions_exists:
            print("❌ permissions 表不存在，需要建立")
            
            # 建立 permissions 表
            cursor.execute("""
                CREATE TABLE permissions (
                    id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                    name VARCHAR(100) UNIQUE NOT NULL,
                    description TEXT,
                    resource VARCHAR(50) NOT NULL,
                    action VARCHAR(50) NOT NULL,
                    created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                    updated_at TIMESTAMPTZ DEFAULT current_timestamp()
                )
            """)
            print("✅ 建立 permissions 表")
            
            # 插入預設權限
            default_permissions = [
                ('user_management', '用戶管理', 'users', 'manage'),
                ('product_management', '產品管理', 'products', 'manage'),
                ('order_management', '訂單管理', 'orders', 'manage'),
                ('view_all_orders', '查看所有訂單', 'orders', 'view_all'),
                ('system_admin', '系統管理', 'system', 'admin')
            ]
            
            for perm_name, description, resource, action in default_permissions:
                cursor.execute("""
                    INSERT INTO permissions (name, description, resource, action)
                    VALUES (%s, %s, %s, %s)
                """, (perm_name, description, resource, action))
            
            print("✅ 插入預設權限")
        
        # 檢查 role_permissions 表
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'role_permissions'
            );
        """)
        role_permissions_exists = cursor.fetchone()[0]
        print(f"📋 role_permissions 表存在: {role_permissions_exists}")
        
        if not role_permissions_exists:
            print("❌ role_permissions 表不存在，需要建立")
            
            # 建立 role_permissions 表
            cursor.execute("""
                CREATE TABLE role_permissions (
                    id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                    role_id INT8 NOT NULL,
                    permission_id INT8 NOT NULL,
                    created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                    UNIQUE(role_id, permission_id)
                )
            """)
            print("✅ 建立 role_permissions 表")
            
            # 為超級管理員分配所有權限
            cursor.execute("SELECT id FROM roles WHERE name = 'super_admin'")
            super_admin_id = cursor.fetchone()[0]
            
            cursor.execute("SELECT id FROM permissions")
            permission_ids = [row[0] for row in cursor.fetchall()]
            
            for perm_id in permission_ids:
                cursor.execute("""
                    INSERT INTO role_permissions (role_id, permission_id)
                    VALUES (%s, %s)
                """, (super_admin_id, perm_id))
            
            print("✅ 為超級管理員分配權限")
        
        conn.commit()
        print("🎉 權限相關表檢查完成！")
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_permissions_tables() 