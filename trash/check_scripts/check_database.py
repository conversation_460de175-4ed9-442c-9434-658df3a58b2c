#!/usr/bin/env python3
import psycopg2

def check_database():
    database_url = "postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require"
    
    try:
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        print("=== 檢查數據庫結構 ===\n")
        
        # 檢查users表結構
        cursor.execute("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'users' 
            ORDER BY ordinal_position
        """)
        
        users_columns = cursor.fetchall()
        print("Users表結構:")
        for col in users_columns:
            print(f"  - {col[0]} ({col[1]}) {'NULL' if col[2]=='YES' else 'NOT NULL'}")
        
        # 檢查角色表數據
        print("\n=== 角色數據 ===")
        cursor.execute("SELECT id, name, description FROM roles ORDER BY id")
        roles = cursor.fetchall()
        for role in roles:
            print(f"  {role[0]}: {role[1]} - {role[2]}")
        
        # 檢查權限數據
        print("\n=== 權限數據 (前10個) ===")
        cursor.execute("SELECT name, resource, action FROM permissions ORDER BY resource, action LIMIT 10")
        permissions = cursor.fetchall()
        for perm in permissions:
            print(f"  {perm[0]} ({perm[1]}.{perm[2]})")
        
        # 檢查用戶數據和角色關聯
        print("\n=== 用戶數據 ===")
        cursor.execute("""
            SELECT u.id, u.username, u.email, u.role_id, r.name as role_name
            FROM users u 
            LEFT JOIN roles r ON u.role_id = r.id 
            ORDER BY u.id
        """)
        users = cursor.fetchall()
        if users:
            for user in users:
                print(f"  ID {user[0]}: {user[1]} ({user[2]}) - 角色: {user[4] or '未設定'}")
        else:
            print("  目前沒有用戶")
            
    except psycopg2.Error as e:
        print(f"數據庫錯誤: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_database()