#!/usr/bin/env python3
import os
import re
from pathlib import Path

def check_rust_syntax():
    """檢查 Rust 代碼的基本語法問題"""
    
    print("🔍 檢查 Rust 語法和常見錯誤...\n")
    
    errors = []
    warnings = []
    
    # 檢查的文件列表
    rust_files = [
        "src/models/role.rs",
        "src/repositories/role.rs", 
        "src/services/admin.rs",
        "src/api/middleware.rs",
        "src/models/mod.rs",
        "src/repositories/mod.rs",
        "src/services/mod.rs",
        "src/main.rs"
    ]
    
    for file_path in rust_files:
        if not os.path.exists(file_path):
            errors.append(f"❌ 文件不存在: {file_path}")
            continue
            
        print(f"📄 檢查 {file_path}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 檢查基本語法問題
            check_basic_syntax(file_path, content, errors, warnings)
            
        except Exception as e:
            errors.append(f"❌ 讀取文件失敗 {file_path}: {e}")
    
    # 檢查模組依賴
    check_module_dependencies(errors, warnings)
    
    # 輸出結果
    print("\n" + "="*50)
    print("📋 檢查結果:")
    print("="*50)
    
    if errors:
        print(f"\n❌ 發現 {len(errors)} 個錯誤:")
        for error in errors:
            print(f"  {error}")
    
    if warnings:
        print(f"\n⚠️  發現 {len(warnings)} 個警告:")
        for warning in warnings:
            print(f"  {warning}")
    
    if not errors and not warnings:
        print("\n✅ 未發現明顯的語法錯誤!")
        
    return len(errors) == 0

def check_basic_syntax(file_path, content, errors, warnings):
    """檢查基本語法問題"""
    
    lines = content.split('\n')
    
    for i, line in enumerate(lines, 1):
        line_stripped = line.strip()
        
        # 檢查未封閉的括號 (簡單檢查)
        if line_stripped.count('(') != line_stripped.count(')'):
            if not line_stripped.endswith(',') and not line_stripped.startswith('//'):
                warnings.append(f"⚠️  {file_path}:{i} - 可能存在未配對的括號")
        
        # 檢查未使用的 use 語句
        if line_stripped.startswith('use ') and '//' in line and 'unused' in line:
            warnings.append(f"⚠️  {file_path}:{i} - 未使用的導入")
        
        # 檢查常見的語法錯誤
        if 'pub struct' in line_stripped and not line_stripped.endswith('{'):
            if i < len(lines) - 1 and not lines[i].strip().startswith('{'):
                errors.append(f"❌ {file_path}:{i} - struct 定義可能有語法錯誤")

def check_module_dependencies(errors, warnings):
    """檢查模組依賴關係"""
    
    # 檢查 models/mod.rs
    if os.path.exists("src/models/mod.rs"):
        with open("src/models/mod.rs", 'r') as f:
            models_mod = f.read()
        
        if "pub mod role;" in models_mod and "pub use role::*;" in models_mod:
            if not os.path.exists("src/models/role.rs"):
                errors.append("❌ models/mod.rs 引用了 role 模組但文件不存在")
    
    # 檢查 repositories/mod.rs  
    if os.path.exists("src/repositories/mod.rs"):
        with open("src/repositories/mod.rs", 'r') as f:
            repos_mod = f.read()
        
        if "pub mod role;" in repos_mod:
            if not os.path.exists("src/repositories/role.rs"):
                errors.append("❌ repositories/mod.rs 引用了 role 模組但文件不存在")
    
    # 檢查 Cargo.toml 依賴
    if os.path.exists("Cargo.toml"):
        with open("Cargo.toml", 'r') as f:
            cargo_content = f.read()
        
        required_deps = ['sqlx', 'tokio', 'serde', 'bcrypt', 'jsonwebtoken', 'axum']
        for dep in required_deps:
            if dep not in cargo_content:
                warnings.append(f"⚠️  Cargo.toml 可能缺少依賴: {dep}")

if __name__ == "__main__":
    success = check_rust_syntax()
    
    if success:
        print("\n🎉 語法檢查通過! 代碼應該能夠編譯。")
        print("\n💡 建議:")
        print("  - 在有 Rust 環境的機器上運行 'cargo check' 進行完整檢查")
        print("  - 運行 'cargo build' 進行完整編譯")
        print("  - 運行 'cargo test' 執行測試")
    else:
        print("\n❌ 發現語法錯誤，請修復後再編譯。")
    
    exit(0 if success else 1)