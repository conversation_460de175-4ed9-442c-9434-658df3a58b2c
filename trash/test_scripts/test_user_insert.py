#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def test_user_insert():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🧪 測試用戶插入 SQL...")
    
    try:
        # 測試插入用戶
        cursor.execute("""
            INSERT INTO users (username, email, password_hash, pharmacy_name, phone, line_user_id, role_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        """, (
            'testuser3',
            '<EMAIL>',
            '$2b$12$test.hash.here',
            '測試藥局3',
            '0912345678',
            None,
            3
        ))
        
        user_id = cursor.fetchone()[0]
        print(f"✅ 成功插入用戶，ID: {user_id}")
        
        # 檢查插入的用戶
        cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
        user = cursor.fetchone()
        print(f"📋 插入的用戶資料: {user}")
        
        conn.commit()
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    test_user_insert() 