#!/usr/bin/env python3
import os
import psycopg2
from decimal import Decimal

# 數據庫連接字符串
DATABASE_URL = "postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require"

def insert_test_products():
    try:
        # 連接到數據庫
        conn = psycopg2.connect(DATABASE_URL)
        cur = conn.cursor()
        
        # 先檢查表結構
        cur.execute("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'products'
        ORDER BY ordinal_position
        """)
        columns = cur.fetchall()
        print("Products 表的欄位:")
        for col in columns:
            print(f"  - {col[0]}: {col[1]}")
        
        # 測試產品數據（只使用存在的欄位）
        products = [
            ('A001234567', '普拿疼 500', '台灣製藥股份有限公司', '盒', 85.50, 150, '止痛退燒，每盒10錠', True),
            ('A001234568', '阿司匹靈腸溶', '永信藥品工業股份有限公司', '瓶', 200.00, 200, '預防心血管疾病，每瓶100錠', True),
        ]
        
        # 插入產品數據
        insert_query = """
        INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, stock_quantity, description, is_active)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (nhi_code) DO NOTHING
        RETURNING id
        """
        
        for product in products:
            try:
                cur.execute(insert_query, product)
                result = cur.fetchone()
                if result:
                    print(f"插入產品 {product[0]} 成功，ID: {result[0]}")
                else:
                    print(f"產品 {product[0]} 已存在，跳過")
            except Exception as e:
                print(f"插入產品 {product[0]} 失敗: {e}")
        
        # 提交事務
        conn.commit()
        
        # 檢查插入的數據
        cur.execute("SELECT COUNT(*) FROM products")
        count = cur.fetchone()[0]
        print(f"總共有 {count} 個產品在數據庫中")
        
        # 查看插入的產品
        cur.execute("SELECT nhi_code, name, stock_quantity FROM products ORDER BY created_at DESC LIMIT 5")
        products = cur.fetchall()
        print("最近的產品:")
        for product in products:
            print(f"  - {product[0]}: {product[1]} (庫存: {product[2]})")
            
    except Exception as e:
        print(f"錯誤: {e}")
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    insert_test_products()