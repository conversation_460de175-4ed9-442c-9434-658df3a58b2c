#!/usr/bin/env python3
"""
測試CockroachDB連接和資料讀取
"""
import psycopg2
import os

# 從環境變數或直接使用連接字串
DATABASE_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def test_database_connection():
    try:
        print("🔗 測試資料庫連接...")
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        print("✅ 連接成功！")
        
        # 測試基本查詢
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        print(f"📋 資料庫版本: {version}")
        
        # 測試資料表存在性
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('users', 'products', 'orders', 'order_items')
            ORDER BY table_name
        """)
        tables = cursor.fetchall()
        print(f"📊 主要資料表: {[t[0] for t in tables]}")
        
        # 測試資料讀取
        for table in ['users', 'products', 'orders']:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   {table}: {count} 筆資料")
            
        # 測試具體資料
        print("\n🧪 測試資料讀取:")
        cursor.execute("SELECT username, pharmacy_name FROM users WHERE id = 1")
        user = cursor.fetchone()
        if user:
            print(f"   第一個使用者: {user[0]} ({user[1]})")
            
        cursor.execute("SELECT name, unit_price FROM products WHERE id = 31")
        product = cursor.fetchone()
        if product:
            print(f"   第一個產品: {product[0]} - ${product[1]}")
            
        conn.close()
        print("\n🎉 所有測試通過！資料庫連接正常，資料完整存在。")
        
        return True
        
    except Exception as e:
        print(f"❌ 連接失敗: {e}")
        return False

if __name__ == "__main__":
    test_database_connection()