#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def test_cart_insert():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🧪 測試購物車相關表插入...")
    
    try:
        # 測試 carts 插入
        print("📦 測試 carts 表...")
        cursor.execute("""
            INSERT INTO carts (user_id)
            VALUES (1)
            RETURNING id
        """)
        cart_result = cursor.fetchone()
        print(f"✅ carts 插入成功: {cart_result}")
        
        # 測試 cart_items 插入
        print("🛒 測試 cart_items 表...")
        cursor.execute("""
            INSERT INTO cart_items (cart_id, product_id, quantity, unit_price, subtotal)
            VALUES (%s, 1, 2, 100.00, 200.00)
            RETURNING id
        """, (cart_result[0],))
        item_result = cursor.fetchone()
        print(f"✅ cart_items 插入成功: {item_result}")
        
        # 清理測試資料
        cursor.execute("DELETE FROM cart_items WHERE cart_id = %s", (cart_result[0],))
        cursor.execute("DELETE FROM carts WHERE id = %s", (cart_result[0],))
        print("🧹 清理測試資料")
        
        conn.commit()
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    test_cart_insert() 