#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def test_products_insert():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🧪 測試 products 表插入...")
    
    try:
        # 測試插入產品
        cursor.execute("""
            INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, stock_quantity, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        """, (
            'A123456789',
            '測試藥品',
            '測試藥廠',
            '顆',
            100.00,
            100,
            '測試用藥品'
        ))
        
        product_id = cursor.fetchone()[0]
        print(f"✅ 成功插入產品，ID: {product_id}")
        
        # 清理測試資料
        cursor.execute("DELETE FROM products WHERE nhi_code = 'A123456789'")
        print("🧹 清理測試資料")
        
        conn.commit()
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    test_products_insert() 