#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def fix_all_tables():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔧 修復所有表的序列問題...")
    
    try:
        # 修復 users 表
        print("\n👥 修復 users 表...")
        
        # 備份 users 資料
        cursor.execute("SELECT username, email, password_hash, pharmacy_name, phone, line_user_id, notification_email, notification_line, role_id, created_at, updated_at FROM users")
        users_data = cursor.fetchall()
        print(f"📦 備份 {len(users_data)} 筆用戶資料")
        
        # 刪除舊表
        cursor.execute("DROP TABLE IF EXISTS users CASCADE")
        
        # 重新建立 users 表（使用 CockroachDB 語法）
        cursor.execute("""
            CREATE TABLE users (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                pharmacy_name VARCHAR(200) NOT NULL,
                phone VARCHAR(20),
                line_user_id VARCHAR(100),
                notification_email BOOLEAN NOT NULL DEFAULT true,
                notification_line BOOLEAN NOT NULL DEFAULT false,
                role_id INT8 NOT NULL DEFAULT 3,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("✅ 重新建立 users 表")
        
        # 恢復 users 資料
        for user in users_data:
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, pharmacy_name, phone, 
                                 line_user_id, notification_email, notification_line, 
                                 role_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, user)
        
        print(f"✅ 恢復 {len(users_data)} 筆用戶資料")
        
        # 修復 products 表
        print("\n💊 修復 products 表...")
        
        # 備份 products 資料
        cursor.execute("SELECT nhi_code, name, manufacturer, unit, unit_price, stock_quantity, description, is_active, created_at, updated_at FROM products")
        products_data = cursor.fetchall()
        print(f"📦 備份 {len(products_data)} 筆產品資料")
        
        # 刪除舊表
        cursor.execute("DROP TABLE IF EXISTS products CASCADE")
        
        # 重新建立 products 表
        cursor.execute("""
            CREATE TABLE products (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                nhi_code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(200) NOT NULL,
                manufacturer VARCHAR(100) NOT NULL,
                unit VARCHAR(20) NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                stock_quantity INT8 NOT NULL DEFAULT 0,
                description TEXT,
                is_active BOOLEAN NOT NULL DEFAULT true,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("✅ 重新建立 products 表")
        
        # 恢復 products 資料
        for product in products_data:
            cursor.execute("""
                INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, 
                                   stock_quantity, description, is_active, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, product)
        
        print(f"✅ 恢復 {len(products_data)} 筆產品資料")
        
        # 測試插入
        print("\n🧪 測試插入...")
        
        # 測試 users 插入
        cursor.execute("""
            INSERT INTO users (username, email, password_hash, pharmacy_name)
            VALUES ('test', '<EMAIL>', 'hash', 'test')
            RETURNING id
        """)
        user_result = cursor.fetchone()
        print(f"✅ users 測試插入成功: {user_result}")
        
        # 測試 products 插入
        cursor.execute("""
            INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, stock_quantity)
            VALUES ('TEST123', 'Test Product', 'Test Manufacturer', '顆', 100.00, 100)
            RETURNING id
        """)
        product_result = cursor.fetchone()
        print(f"✅ products 測試插入成功: {product_result}")
        
        # 清理測試資料
        cursor.execute("DELETE FROM users WHERE username = 'test'")
        cursor.execute("DELETE FROM products WHERE nhi_code = 'TEST123'")
        print("🧹 清理測試資料")
        
        conn.commit()
        print("🎉 所有表修復完成！")
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    fix_all_tables() 