#!/usr/bin/env python3
"""
簡化的資料遷移腳本
"""

import sqlite3
import psycopg2
import json
from datetime import datetime

# 連接配置
SQLITE_DB = "pharmacy.db"
POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def export_sqlite_to_json():
    """將SQLite資料導出為JSON"""
    conn = sqlite3.connect(SQLITE_DB)
    conn.row_factory = sqlite3.Row
    
    data = {}
    
    # 導出各表格資料
    tables = ['users', 'products', 'orders', 'order_items', 'carts', 'cart_items', 
              'notifications', 'notification_preferences', 'backup_logs']
    
    for table in tables:
        try:
            cursor = conn.cursor()
            cursor.execute(f"SELECT * FROM {table}")
            rows = cursor.fetchall()
            data[table] = [dict(row) for row in rows]
            print(f"✅ 導出 {table}: {len(data[table])} 筆記錄")
        except sqlite3.OperationalError as e:
            print(f"⚠️  表格 {table} 不存在或無法讀取: {e}")
            data[table] = []
    
    conn.close()
    
    # 儲存到JSON檔案
    with open('migration_data.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"✅ 資料已導出到 migration_data.json")
    return data

def create_postgres_schema():
    """在PostgreSQL建立表格結構"""
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    # 建立表格的SQL
    schema_sql = """
    -- 建立使用者表格
    CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        pharmacy_name VARCHAR(200) NOT NULL,
        phone VARCHAR(20),
        line_user_id VARCHAR(100),
        notification_email BOOLEAN NOT NULL DEFAULT true,
        notification_line BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- 建立產品表格
    CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        nhi_code VARCHAR(20) UNIQUE NOT NULL,
        name VARCHAR(200) NOT NULL,
        manufacturer VARCHAR(100) NOT NULL,
        unit VARCHAR(20) NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        stock_quantity INTEGER NOT NULL DEFAULT 0,
        description TEXT,
        is_active BOOLEAN NOT NULL DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- 建立訂單表格
    CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        user_id INTEGER NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        total_amount DECIMAL(10,2) NOT NULL,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    );

    -- 建立訂單項目表格
    CREATE TABLE IF NOT EXISTS order_items (
        id SERIAL PRIMARY KEY,
        order_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        subtotal DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders(id),
        FOREIGN KEY (product_id) REFERENCES products(id)
    );

    -- 建立購物車表格
    CREATE TABLE IF NOT EXISTS carts (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id),
        UNIQUE(user_id)
    );

    -- 建立購物車項目表格
    CREATE TABLE IF NOT EXISTS cart_items (
        id SERIAL PRIMARY KEY,
        cart_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        subtotal DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (cart_id) REFERENCES carts(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id),
        UNIQUE(cart_id, product_id)
    );

    -- 建立通知偏好設定表格
    CREATE TABLE IF NOT EXISTS notification_preferences (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        email_enabled BOOLEAN NOT NULL DEFAULT true,
        line_enabled BOOLEAN NOT NULL DEFAULT false,
        line_user_id VARCHAR(100),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    );

    -- 建立通知記錄表格
    CREATE TABLE IF NOT EXISTS notifications (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        type VARCHAR(20) NOT NULL,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        sent_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    );

    -- 建立備份記錄表格
    CREATE TABLE IF NOT EXISTS backup_logs (
        id SERIAL PRIMARY KEY,
        backup_file VARCHAR(255) NOT NULL,
        file_size BIGINT NOT NULL,
        status VARCHAR(20) NOT NULL,
        uploaded_to_gcp BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- 建立索引
    CREATE INDEX IF NOT EXISTS idx_products_nhi_code ON products(nhi_code);
    CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
    CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
    CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
    CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
    CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
    CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);
    CREATE INDEX IF NOT EXISTS idx_carts_user_id ON carts(user_id);
    CREATE INDEX IF NOT EXISTS idx_cart_items_cart_id ON cart_items(cart_id);
    CREATE INDEX IF NOT EXISTS idx_cart_items_product_id ON cart_items(product_id);
    """
    
    cursor.execute(schema_sql)
    conn.commit()
    cursor.close()
    conn.close()
    
    print("✅ PostgreSQL schema 建立完成")

def import_data_to_postgres(data):
    """將資料導入PostgreSQL"""
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    try:
        # 先清空所有表格
        cursor.execute("TRUNCATE TABLE notifications, notification_preferences, cart_items, carts, order_items, orders, products, users, backup_logs RESTART IDENTITY CASCADE")
        
        # 導入使用者
        if data.get('users'):
            for user in data['users']:
                cursor.execute("""
                    INSERT INTO users (username, email, password_hash, pharmacy_name, phone, 
                                     line_user_id, notification_email, notification_line, 
                                     created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    user['username'], user['email'], user['password_hash'], 
                    user['pharmacy_name'], user['phone'], user['line_user_id'],
                    user['notification_email'], user['notification_line'],
                    user['created_at'], user['updated_at']
                ))
            print(f"✅ 導入 {len(data['users'])} 筆使用者資料")
        
        # 導入產品
        if data.get('products'):
            for product in data['products']:
                cursor.execute("""
                    INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, 
                                        stock_quantity, description, is_active, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    product['nhi_code'], product['name'], product['manufacturer'], product['unit'],
                    product['unit_price'], product['stock_quantity'], product['description'],
                    product['is_active'], product['created_at'], product['updated_at']
                ))
            print(f"✅ 導入 {len(data['products'])} 筆產品資料")
        
        # 導入訂單
        if data.get('orders'):
            for order in data['orders']:
                cursor.execute("""
                    INSERT INTO orders (order_number, user_id, status, total_amount, 
                                      notes, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    order['order_number'], order['user_id'], order['status'], 
                    order['total_amount'], order['notes'], order['created_at'], order['updated_at']
                ))
            print(f"✅ 導入 {len(data['orders'])} 筆訂單資料")
        
        # 導入訂單項目
        if data.get('order_items'):
            for item in data['order_items']:
                cursor.execute("""
                    INSERT INTO order_items (order_id, product_id, quantity, unit_price, 
                                           subtotal, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    item['order_id'], item['product_id'], item['quantity'], 
                    item['unit_price'], item['subtotal'], item['created_at']
                ))
            print(f"✅ 導入 {len(data['order_items'])} 筆訂單項目資料")
        
        conn.commit()
        print("✅ 所有資料導入完成")
        
    except Exception as e:
        print(f"❌ 導入資料時發生錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def main():
    print("🚀 開始資料遷移...")
    
    # 1. 導出SQLite資料
    print("\n📤 步驟 1: 導出SQLite資料")
    data = export_sqlite_to_json()
    
    # 2. 建立PostgreSQL schema
    print("\n🏗️  步驟 2: 建立PostgreSQL schema")
    create_postgres_schema()
    
    # 3. 導入資料
    print("\n📥 步驟 3: 導入資料到PostgreSQL")
    import_data_to_postgres(data)
    
    print("\n🎉 資料遷移完成！")

if __name__ == "__main__":
    main()