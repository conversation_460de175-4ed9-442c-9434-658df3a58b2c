#!/usr/bin/env python3
"""
修復遷移文件，使其支持重複運行
"""

import os
import re

def fix_migration_file(file_path):
    """修復單個遷移文件"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 備份原文件
    backup_path = file_path + '.backup'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 替換CREATE TABLE為CREATE TABLE IF NOT EXISTS
    content = re.sub(
        r'CREATE TABLE\s+(\w+)',
        r'CREATE TABLE IF NOT EXISTS \1',
        content,
        flags=re.IGNORECASE
    )
    
    # 替換CREATE INDEX為CREATE INDEX IF NOT EXISTS
    content = re.sub(
        r'CREATE INDEX\s+(\w+)',
        r'CREATE INDEX IF NOT EXISTS \1',
        content,
        flags=re.IGNORECASE
    )
    
    # 寫回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✓ 修復完成: {file_path}")
    return True

def fix_all_migrations():
    """修復所有遷移文件"""
    print("正在修復遷移文件...")
    
    migration_files = [
        'migrations/20240101000001_initial_schema.sql',
        'migrations/20240102000001_add_cart_tables.sql',
        'migrations/20240103000001_add_user_roles.sql'
    ]
    
    for file_path in migration_files:
        fix_migration_file(file_path)
    
    print("\n所有遷移文件已修復為支持重複運行")

if __name__ == "__main__":
    fix_all_migrations()