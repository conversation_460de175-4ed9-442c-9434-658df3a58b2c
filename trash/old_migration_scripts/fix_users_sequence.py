#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def fix_users_sequence():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔧 修復 users 表序列問題...")
    
    try:
        # 備份現有資料
        cursor.execute("SELECT username, email, password_hash, pharmacy_name, phone, line_user_id, notification_email, notification_line, role_id, created_at, updated_at FROM users")
        users_data = cursor.fetchall()
        print(f"📦 備份 {len(users_data)} 筆用戶資料")
        
        # 刪除舊表
        cursor.execute("DROP TABLE IF EXISTS users CASCADE")
        print("🗑️ 刪除舊表")
        
        # 重新建立表
        cursor.execute("""
            CREATE TABLE users (
                id BIGSERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                pharmacy_name VARCHAR(200) NOT NULL,
                phone VARCHAR(20),
                line_user_id VARCHAR(100),
                notification_email BOOLEAN NOT NULL DEFAULT true,
                notification_line BOOLEAN NOT NULL DEFAULT false,
                role_id BIGINT NOT NULL DEFAULT 3,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ 重新建立表")
        
        # 恢復資料
        for user in users_data:
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, pharmacy_name, phone, 
                                 line_user_id, notification_email, notification_line, 
                                 role_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, user)
        
        print(f"✅ 恢復 {len(users_data)} 筆用戶資料")
        
        # 測試插入
        print("\n🧪 測試插入...")
        cursor.execute("""
            INSERT INTO users (username, email, password_hash, pharmacy_name)
            VALUES ('test', '<EMAIL>', 'hash', 'test')
            RETURNING id
        """)
        result = cursor.fetchone()
        print(f"✅ 測試插入成功: {result}")
        
        # 清理測試資料
        cursor.execute("DELETE FROM users WHERE username = 'test'")
        
        conn.commit()
        print("🎉 修復完成！")
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    fix_users_sequence() 