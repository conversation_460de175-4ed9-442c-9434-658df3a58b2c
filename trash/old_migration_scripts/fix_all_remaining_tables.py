#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def fix_all_remaining_tables():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔧 修復所有剩餘的表...")
    
    try:
        # 修復 backup_logs 表
        print("\n📦 修復 backup_logs 表...")
        
        # 備份資料
        cursor.execute("SELECT backup_file, file_size, status, uploaded_to_gcp, created_at FROM backup_logs")
        backup_logs_data = cursor.fetchall()
        print(f"📦 備份 {len(backup_logs_data)} 筆 backup_logs 資料")
        
        # 刪除舊表
        cursor.execute("DROP TABLE IF EXISTS backup_logs CASCADE")
        
        # 重新建立表
        cursor.execute("""
            CREATE TABLE backup_logs (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                backup_file VARCHAR(255) NOT NULL,
                file_size INT8 NOT NULL,
                status VARCHAR(20) NOT NULL,
                uploaded_to_gcp BOOLEAN NOT NULL DEFAULT false,
                created_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("✅ 重新建立 backup_logs 表")
        
        # 恢復資料
        for log in backup_logs_data:
            cursor.execute("""
                INSERT INTO backup_logs (backup_file, file_size, status, uploaded_to_gcp, created_at)
                VALUES (%s, %s, %s, %s, %s)
            """, log)
        
        print(f"✅ 恢復 {len(backup_logs_data)} 筆 backup_logs 資料")
        
        # 修復 notification_preferences 表
        print("\n🔔 修復 notification_preferences 表...")
        
        # 備份資料
        cursor.execute("SELECT user_id, email_enabled, line_enabled, line_user_id, created_at, updated_at FROM notification_preferences")
        notification_prefs_data = cursor.fetchall()
        print(f"📦 備份 {len(notification_prefs_data)} 筆 notification_preferences 資料")
        
        # 刪除舊表
        cursor.execute("DROP TABLE IF EXISTS notification_preferences CASCADE")
        
        # 重新建立表
        cursor.execute("""
            CREATE TABLE notification_preferences (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                user_id INT8 NOT NULL,
                email_enabled BOOLEAN NOT NULL DEFAULT true,
                line_enabled BOOLEAN NOT NULL DEFAULT false,
                line_user_id VARCHAR(100),
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("✅ 重新建立 notification_preferences 表")
        
        # 恢復資料
        for pref in notification_prefs_data:
            cursor.execute("""
                INSERT INTO notification_preferences (user_id, email_enabled, line_enabled, line_user_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, pref)
        
        print(f"✅ 恢復 {len(notification_prefs_data)} 筆 notification_preferences 資料")
        
        # 修復 notifications 表
        print("\n📢 修復 notifications 表...")
        
        # 備份資料
        cursor.execute("SELECT user_id, type, title, message, status, sent_at, created_at FROM notifications")
        notifications_data = cursor.fetchall()
        print(f"📦 備份 {len(notifications_data)} 筆 notifications 資料")
        
        # 刪除舊表
        cursor.execute("DROP TABLE IF EXISTS notifications CASCADE")
        
        # 重新建立表
        cursor.execute("""
            CREATE TABLE notifications (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                user_id INT8 NOT NULL,
                type VARCHAR(20) NOT NULL,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                sent_at TIMESTAMPTZ,
                created_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("✅ 重新建立 notifications 表")
        
        # 恢復資料
        for notif in notifications_data:
            cursor.execute("""
                INSERT INTO notifications (user_id, type, title, message, status, sent_at, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, notif)
        
        print(f"✅ 恢復 {len(notifications_data)} 筆 notifications 資料")
        
        # 測試插入
        print("\n🧪 測試插入...")
        
        # 測試 backup_logs 插入
        cursor.execute("""
            INSERT INTO backup_logs (backup_file, file_size, status)
            VALUES ('test_backup.db', 1024, 'completed')
            RETURNING id
        """)
        backup_result = cursor.fetchone()
        print(f"✅ backup_logs 測試插入成功: {backup_result}")
        
        # 測試 notification_preferences 插入
        cursor.execute("""
            INSERT INTO notification_preferences (user_id, email_enabled, line_enabled)
            VALUES (1, true, false)
            RETURNING id
        """)
        pref_result = cursor.fetchone()
        print(f"✅ notification_preferences 測試插入成功: {pref_result}")
        
        # 測試 notifications 插入
        cursor.execute("""
            INSERT INTO notifications (user_id, type, title, message)
            VALUES (1, 'test', 'Test Notification', 'This is a test notification')
            RETURNING id
        """)
        notif_result = cursor.fetchone()
        print(f"✅ notifications 測試插入成功: {notif_result}")
        
        # 清理測試資料
        cursor.execute("DELETE FROM backup_logs WHERE backup_file = 'test_backup.db'")
        cursor.execute("DELETE FROM notification_preferences WHERE user_id = 1 AND id = %s", (pref_result[0],))
        cursor.execute("DELETE FROM notifications WHERE id = %s", (notif_result[0],))
        print("🧹 清理測試資料")
        
        conn.commit()
        print("🎉 所有表修復完成！")
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    fix_all_remaining_tables() 