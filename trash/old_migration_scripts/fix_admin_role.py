#!/usr/bin/env python3

import psycopg2
import os
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

def fix_admin_role():
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("錯誤：找不到 DATABASE_URL")
        return
    
    try:
        print(f"修正admin用戶的role_id...")
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        # 將admin用戶的role_id設為2（admin角色）
        cur.execute("UPDATE users SET role_id = 2 WHERE username = 'admin';")
        
        # 確認修改
        cur.execute("SELECT username, role_id FROM users WHERE username = 'admin';")
        admin_user = cur.fetchone()
        print(f"admin用戶的新role_id: {admin_user[1]}")
        
        # 驗證admin角色是否有orders.read_all權限
        cur.execute("""
            SELECT 1 FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN roles r ON rp.role_id = r.id
            WHERE r.id = 2 AND p.name = 'orders.read_all';
        """)
        has_permission = cur.fetchone()
        print(f"admin角色是否有orders.read_all權限: {'是' if has_permission else '否'}")
        
        conn.commit()
        cur.close()
        conn.close()
        
        print("修正完成！")
        
    except Exception as e:
        print(f"修正時發生錯誤: {e}")

if __name__ == "__main__":
    fix_admin_role()