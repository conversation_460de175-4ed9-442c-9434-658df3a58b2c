#!/usr/bin/env python3
import psycopg2
import os
from pathlib import Path

def run_migration():
    # 從 .env 文件讀取連接字符串
    database_url = "postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require"
    
    print("連接到數據庫...")
    
    try:
        # 連接到數據庫
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        # 讀取並執行遷移文件
        migration_file = Path("migrations/20240103000001_add_user_roles.sql")
        
        if migration_file.exists():
            print(f"執行遷移: {migration_file}")
            
            with open(migration_file, 'r', encoding='utf-8') as f:
                migration_sql = f.read()
            
            # 執行整個遷移文件作為一個事務
            try:
                cursor.execute(migration_sql)
                print("✓ 遷移SQL執行成功")
            except psycopg2.Error as e:
                print(f"✗ 遷移執行失敗: {e}")
                # 回滾事務
                conn.rollback()
                return
            
            # 提交更改
            conn.commit()
            print("✅ 遷移完成！")
            
            # 檢查創建的表
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('roles', 'permissions', 'role_permissions')
                ORDER BY table_name
            """)
            
            tables = cursor.fetchall()
            print("\n創建的表:")
            for table in tables:
                print(f"  - {table[0]}")
                
            # 檢查角色數據
            cursor.execute("SELECT name, description FROM roles ORDER BY name")
            roles = cursor.fetchall()
            print("\n創建的角色:")
            for role in roles:
                print(f"  - {role[0]}: {role[1]}")
                
        else:
            print(f"遷移文件不存在: {migration_file}")
            
    except psycopg2.Error as e:
        print(f"數據庫錯誤: {e}")
    except Exception as e:
        print(f"其他錯誤: {e}")
    finally:
        if 'conn' in locals():
            conn.close()
            print("數據庫連接已關閉")

if __name__ == "__main__":
    run_migration()