#!/usr/bin/env python3

import psycopg2
import os
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

def run_migration():
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("錯誤：找不到 DATABASE_URL")
        return
    
    try:
        print(f"連接到資料庫並執行role migration...")
        conn = psycopg2.connect(database_url)
        cur = conn.cursor()
        
        # 讀取修復後的migration檔案
        with open('migrations/20240103000001_add_user_roles.sql', 'r', encoding='utf-8') as f:
            migration_sql = f.read()
        
        # 執行migration
        cur.execute(migration_sql)
        conn.commit()
        
        print("Migration執行完成！")
        
        # 驗證結果
        cur.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('roles', 'permissions', 'role_permissions') ORDER BY table_name;")
        tables = cur.fetchall()
        print(f"建立的資料表: {[table[0] for table in tables]}")
        
        # 檢查角色資料
        if tables:
            cur.execute("SELECT name FROM roles ORDER BY id;")
            roles = cur.fetchall()
            print(f"建立的角色: {[role[0] for role in roles]}")
            
            # 檢查admin角色的權限
            cur.execute("""
                SELECT p.name FROM permissions p
                JOIN role_permissions rp ON p.id = rp.permission_id
                JOIN roles r ON rp.role_id = r.id
                WHERE r.name = 'admin'
                ORDER BY p.name;
            """)
            admin_permissions = cur.fetchall()
            print(f"admin角色的權限: {[perm[0] for perm in admin_permissions]}")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"執行migration時發生錯誤: {e}")

if __name__ == "__main__":
    run_migration()