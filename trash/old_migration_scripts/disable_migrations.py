#!/usr/bin/env python3
"""
禁用Rust應用程序中的自動遷移
"""

import os
import re

def find_database_files():
    """找到數據庫相關的Rust文件"""
    database_files = []
    
    # 搜索可能包含遷移代碼的文件
    for root, dirs, files in os.walk('src'):
        for file in files:
            if file.endswith('.rs'):
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'migrate' in content.lower() or 'migration' in content.lower():
                        database_files.append(file_path)
    
    return database_files

def disable_migrations_in_file(file_path):
    """在指定文件中禁用遷移"""
    print(f"檢查文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 註釋掉遷移相關的代碼
    patterns = [
        (r'(\s*)(sqlx::migrate::run\([^)]+\)\.await\??[^;]*;)', r'\1// \2'),
        (r'(\s*)(migrate!\([^)]+\))', r'\1// \2'),
        (r'(\s*)(\.migrate\([^)]+\))', r'\1// .migrate(/* disabled */)'),
        (r'(\s*)(run_migrations\([^)]+\)\.await\??[^;]*;)', r'\1// \2'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    if content != original_content:
        # 備份原文件
        backup_path = file_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(original_content)
        
        # 寫入修改後的內容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  ✓ 已禁用遷移並備份到: {backup_path}")
        return True
    else:
        print(f"  - 未找到需要修改的遷移代碼")
        return False

def main():
    print("=== 禁用Rust應用程序中的自動遷移 ===")
    print()
    
    database_files = find_database_files()
    
    if not database_files:
        print("未找到包含遷移代碼的文件")
        return
    
    print(f"找到 {len(database_files)} 個可能包含遷移代碼的文件:")
    for file_path in database_files:
        print(f"  - {file_path}")
    
    print()
    
    modified_files = []
    for file_path in database_files:
        if disable_migrations_in_file(file_path):
            modified_files.append(file_path)
    
    print()
    if modified_files:
        print("✅ 已成功禁用以下文件中的遷移:")
        for file_path in modified_files:
            print(f"  ✓ {file_path}")
        
        print()
        print("現在嘗試重新編譯和啟動應用程序:")
        print("  cargo build --bin pharmacy-system")
        print("  cargo run --bin pharmacy-system")
    else:
        print("❌ 未找到需要修改的遷移代碼")
        print("可能需要手動檢查數據庫初始化代碼")

if __name__ == "__main__":
    main()