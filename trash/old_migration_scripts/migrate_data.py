#!/usr/bin/env python3
"""
PostgreSQL Data Migration Script
PostgreSQL 資料遷移腳本（已不再使用 SQLite）
"""

# import sqlite3  # 不再需要 SQLite
import psycopg2
import os
from datetime import datetime
import sys

# 資料庫配置（已不再使用 SQLite）
# SQLITE_DB_PATH = "pharmacy.db"  # 已棄用
POSTGRES_CONNECTION = {
    'host': 'messy-molerat-79.jxf.cockroachlabs.cloud',
    'port': 26257,
    'database': 'happy',
    'user': 'arguskao',
    'password': 'MIYlINYSePJ-eoC0lnPqsQ',
    'sslmode': 'require'
}

def connect_sqlite():
    """連接SQLite資料庫"""
    try:
        conn = sqlite3.connect(SQLITE_DB_PATH)
        conn.row_factory = sqlite3.Row  # 使結果可以按欄名存取
        return conn
    except Exception as e:
        print(f"SQLite連接錯誤: {e}")
        return None

def connect_postgres():
    """連接PostgreSQL/CockroachDB"""
    try:
        conn = psycopg2.connect(
            host=POSTGRES_CONNECTION['host'],
            port=POSTGRES_CONNECTION['port'],
            database=POSTGRES_CONNECTION['database'],
            user=POSTGRES_CONNECTION['user'],
            password=POSTGRES_CONNECTION['password'],
            sslmode=POSTGRES_CONNECTION['sslmode']
        )
        return conn
    except Exception as e:
        print(f"PostgreSQL連接錯誤: {e}")
        return None

def export_table_data(sqlite_conn, table_name):
    """從SQLite匯出表格資料"""
    cursor = sqlite_conn.cursor()
    cursor.execute(f"SELECT * FROM {table_name}")
    rows = cursor.fetchall()
    
    if rows:
        # 取得欄位名稱
        columns = [description[0] for description in cursor.description]
        return columns, [dict(row) for row in rows]
    return [], []

def migrate_users(sqlite_conn, postgres_conn):
    """遷移使用者資料"""
    print("遷移使用者資料...")
    columns, data = export_table_data(sqlite_conn, 'users')
    
    if not data:
        print("沒有使用者資料需要遷移")
        return
    
    postgres_cursor = postgres_conn.cursor()
    
    # 清空目標表格
    postgres_cursor.execute("TRUNCATE TABLE users RESTART IDENTITY CASCADE")
    
    for row in data:
        postgres_cursor.execute("""
            INSERT INTO users (username, email, password_hash, pharmacy_name, phone, 
                             line_user_id, notification_email, notification_line, 
                             created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            row['username'], row['email'], row['password_hash'], 
            row['pharmacy_name'], row['phone'], row['line_user_id'],
            row['notification_email'], row['notification_line'],
            row['created_at'], row['updated_at']
        ))
    
    postgres_conn.commit()
    print(f"已遷移 {len(data)} 筆使用者資料")

def migrate_products(sqlite_conn, postgres_conn):
    """遷移產品資料"""
    print("遷移產品資料...")
    columns, data = export_table_data(sqlite_conn, 'products')
    
    if not data:
        print("沒有產品資料需要遷移")
        return
    
    postgres_cursor = postgres_conn.cursor()
    
    # 清空目標表格
    postgres_cursor.execute("TRUNCATE TABLE products RESTART IDENTITY CASCADE")
    
    for row in data:
        postgres_cursor.execute("""
            INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, 
                                stock_quantity, description, is_active, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            row['nhi_code'], row['name'], row['manufacturer'], row['unit'],
            row['unit_price'], row['stock_quantity'], row['description'],
            row['is_active'], row['created_at'], row['updated_at']
        ))
    
    postgres_conn.commit()
    print(f"已遷移 {len(data)} 筆產品資料")

def migrate_orders(sqlite_conn, postgres_conn):
    """遷移訂單資料"""
    print("遷移訂單資料...")
    columns, data = export_table_data(sqlite_conn, 'orders')
    
    if not data:
        print("沒有訂單資料需要遷移")
        return
    
    postgres_cursor = postgres_conn.cursor()
    
    # 清空目標表格
    postgres_cursor.execute("TRUNCATE TABLE orders RESTART IDENTITY CASCADE")
    
    for row in data:
        postgres_cursor.execute("""
            INSERT INTO orders (order_number, user_id, status, total_amount, 
                              notes, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            row['order_number'], row['user_id'], row['status'], 
            row['total_amount'], row['notes'], row['created_at'], row['updated_at']
        ))
    
    postgres_conn.commit()
    print(f"已遷移 {len(data)} 筆訂單資料")

def migrate_order_items(sqlite_conn, postgres_conn):
    """遷移訂單項目資料"""
    print("遷移訂單項目資料...")
    columns, data = export_table_data(sqlite_conn, 'order_items')
    
    if not data:
        print("沒有訂單項目資料需要遷移")
        return
    
    postgres_cursor = postgres_conn.cursor()
    
    # 清空目標表格
    postgres_cursor.execute("TRUNCATE TABLE order_items RESTART IDENTITY CASCADE")
    
    for row in data:
        postgres_cursor.execute("""
            INSERT INTO order_items (order_id, product_id, quantity, unit_price, 
                                   subtotal, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            row['order_id'], row['product_id'], row['quantity'], 
            row['unit_price'], row['subtotal'], row['created_at']
        ))
    
    postgres_conn.commit()
    print(f"已遷移 {len(data)} 筆訂單項目資料")

def migrate_carts(sqlite_conn, postgres_conn):
    """遷移購物車資料"""
    print("遷移購物車資料...")
    
    # 遷移購物車
    columns, data = export_table_data(sqlite_conn, 'carts')
    if data:
        postgres_cursor = postgres_conn.cursor()
        postgres_cursor.execute("TRUNCATE TABLE carts RESTART IDENTITY CASCADE")
        
        for row in data:
            postgres_cursor.execute("""
                INSERT INTO carts (user_id, created_at, updated_at)
                VALUES (%s, %s, %s)
            """, (row['user_id'], row['created_at'], row['updated_at']))
        
        postgres_conn.commit()
        print(f"已遷移 {len(data)} 筆購物車資料")
    
    # 遷移購物車項目
    columns, data = export_table_data(sqlite_conn, 'cart_items')
    if data:
        postgres_cursor = postgres_conn.cursor()
        postgres_cursor.execute("TRUNCATE TABLE cart_items RESTART IDENTITY CASCADE")
        
        for row in data:
            postgres_cursor.execute("""
                INSERT INTO cart_items (cart_id, product_id, quantity, unit_price, 
                                      subtotal, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                row['cart_id'], row['product_id'], row['quantity'], 
                row['unit_price'], row['subtotal'], row['created_at'], row['updated_at']
            ))
        
        postgres_conn.commit()
        print(f"已遷移 {len(data)} 筆購物車項目資料")

def migrate_notifications(sqlite_conn, postgres_conn):
    """遷移通知相關資料"""
    print("遷移通知資料...")
    
    # 遷移通知偏好設定
    columns, data = export_table_data(sqlite_conn, 'notification_preferences')
    if data:
        postgres_cursor = postgres_conn.cursor()
        postgres_cursor.execute("TRUNCATE TABLE notification_preferences RESTART IDENTITY CASCADE")
        
        for row in data:
            postgres_cursor.execute("""
                INSERT INTO notification_preferences (user_id, email_enabled, line_enabled, 
                                                    line_user_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                row['user_id'], row['email_enabled'], row['line_enabled'],
                row['line_user_id'], row['created_at'], row['updated_at']
            ))
        
        postgres_conn.commit()
        print(f"已遷移 {len(data)} 筆通知偏好設定資料")
    
    # 遷移通知記錄
    columns, data = export_table_data(sqlite_conn, 'notifications')
    if data:
        postgres_cursor = postgres_conn.cursor()
        postgres_cursor.execute("TRUNCATE TABLE notifications RESTART IDENTITY CASCADE")
        
        for row in data:
            postgres_cursor.execute("""
                INSERT INTO notifications (user_id, type, title, message, status, 
                                         sent_at, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                row['user_id'], row['type'], row['title'], row['message'],
                row['status'], row['sent_at'], row['created_at']
            ))
        
        postgres_conn.commit()
        print(f"已遷移 {len(data)} 筆通知記錄資料")

def main():
    """主要遷移流程"""
    print("開始資料遷移...")
    print(f"時間: {datetime.now()}")
    
    # 連接資料庫
    sqlite_conn = connect_sqlite()
    if not sqlite_conn:
        print("無法連接SQLite資料庫")
        return
    
    postgres_conn = connect_postgres()
    if not postgres_conn:
        print("無法連接PostgreSQL資料庫")
        sqlite_conn.close()
        return
    
    try:
        # 按順序遷移資料（考慮外鍵約束）
        migrate_users(sqlite_conn, postgres_conn)
        migrate_products(sqlite_conn, postgres_conn)
        migrate_orders(sqlite_conn, postgres_conn)
        migrate_order_items(sqlite_conn, postgres_conn)
        migrate_carts(sqlite_conn, postgres_conn)
        migrate_notifications(sqlite_conn, postgres_conn)
        
        print("\n✅ 資料遷移完成！")
        
    except Exception as e:
        print(f"\n❌ 遷移過程中發生錯誤: {e}")
        postgres_conn.rollback()
    
    finally:
        sqlite_conn.close()
        postgres_conn.close()

if __name__ == "__main__":
    main()