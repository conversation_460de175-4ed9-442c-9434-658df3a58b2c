#!/usr/bin/env python3
"""
修復order_items表的遷移
"""

import sqlite3
import psycopg2
import os

def get_database_url():
    """從環境變量讀取數據庫連接信息"""
    env_vars = {}
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    return env_vars.get('DATABASE_URL')

def fix_order_items():
    """修復order_items表的遷移"""
    print("正在修復order_items表...")
    
    # 連接數據庫
    sqlite_conn = sqlite3.connect('pharmacy.db')
    sqlite_conn.row_factory = sqlite3.Row
    
    database_url = get_database_url()
    pg_conn = psycopg2.connect(database_url)
    
    try:
        # 從SQLite獲取order_items數據
        sqlite_cursor = sqlite_conn.cursor()
        sqlite_cursor.execute("SELECT * FROM order_items")
        order_items = sqlite_cursor.fetchall()
        
        print(f"找到 {len(order_items)} 條order_items記錄")
        
        # 清空PostgreSQL中的order_items
        pg_cursor = pg_conn.cursor()
        pg_cursor.execute("DELETE FROM order_items")
        
        # 遷移數據，使用subtotal作為total_price
        for item in order_items:
            pg_cursor.execute("""
                INSERT INTO order_items (id, order_id, product_id, quantity,
                                       unit_price, total_price, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                item['id'], item['order_id'], item['product_id'],
                item['quantity'], item['unit_price'], item['subtotal'],  # 使用subtotal作為total_price
                item['created_at'] if 'created_at' in item.keys() else None
            ))
        
        # 修復序列
        pg_cursor.execute("""
            SELECT setval(pg_get_serial_sequence('order_items', 'id'), 
                         COALESCE((SELECT MAX(id) FROM order_items), 1), false);
        """)
        
        pg_conn.commit()
        
        # 驗證結果
        pg_cursor.execute("SELECT COUNT(*) FROM order_items")
        pg_count = pg_cursor.fetchone()[0]
        
        print(f"成功遷移 {pg_count} 條order_items記錄")
        
    except Exception as e:
        print(f"修復過程中發生錯誤: {e}")
        pg_conn.rollback()
    finally:
        sqlite_conn.close()
        pg_conn.close()

if __name__ == "__main__":
    fix_order_items()