#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def fix_remaining_tables():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔧 修復剩餘的表...")
    
    try:
        # 修復 order_items 表
        print("\n📦 修復 order_items 表...")
        
        # 備份資料
        cursor.execute("SELECT order_id, product_id, quantity, unit_price, subtotal, created_at FROM order_items")
        order_items_data = cursor.fetchall()
        print(f"📦 備份 {len(order_items_data)} 筆 order_items 資料")
        
        # 刪除舊表
        cursor.execute("DROP TABLE IF EXISTS order_items CASCADE")
        
        # 重新建立表
        cursor.execute("""
            CREATE TABLE order_items (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                order_id INT8 NOT NULL,
                product_id INT8 NOT NULL,
                quantity INT8 NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                subtotal DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("✅ 重新建立 order_items 表")
        
        # 恢復資料
        for item in order_items_data:
            cursor.execute("""
                INSERT INTO order_items (order_id, product_id, quantity, unit_price, subtotal, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, item)
        
        print(f"✅ 恢復 {len(order_items_data)} 筆 order_items 資料")
        
        # 修復 orders 表
        print("\n📋 修復 orders 表...")
        
        # 備份資料
        cursor.execute("SELECT order_number, user_id, status, total_amount, notes, created_at, updated_at FROM orders")
        orders_data = cursor.fetchall()
        print(f"📦 備份 {len(orders_data)} 筆 orders 資料")
        
        # 刪除舊表
        cursor.execute("DROP TABLE IF EXISTS orders CASCADE")
        
        # 重新建立表
        cursor.execute("""
            CREATE TABLE orders (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                order_number VARCHAR(50) UNIQUE NOT NULL,
                user_id INT8 NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                total_amount DECIMAL(10,2) NOT NULL,
                notes TEXT,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("✅ 重新建立 orders 表")
        
        # 恢復資料
        for order in orders_data:
            cursor.execute("""
                INSERT INTO orders (order_number, user_id, status, total_amount, notes, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, order)
        
        print(f"✅ 恢復 {len(orders_data)} 筆 orders 資料")
        
        # 測試插入
        print("\n🧪 測試插入...")
        
        # 測試 orders 插入
        cursor.execute("""
            INSERT INTO orders (order_number, user_id, total_amount)
            VALUES ('TEST-001', 1, 100.00)
            RETURNING id
        """)
        order_result = cursor.fetchone()
        print(f"✅ orders 測試插入成功: {order_result}")
        
        # 測試 order_items 插入
        cursor.execute("""
            INSERT INTO order_items (order_id, product_id, quantity, unit_price, subtotal)
            VALUES (%s, 1, 2, 50.00, 100.00)
            RETURNING id
        """, (order_result[0],))
        item_result = cursor.fetchone()
        print(f"✅ order_items 測試插入成功: {item_result}")
        
        # 清理測試資料
        cursor.execute("DELETE FROM order_items WHERE order_id = %s", (order_result[0],))
        cursor.execute("DELETE FROM orders WHERE order_number = 'TEST-001'")
        print("🧹 清理測試資料")
        
        conn.commit()
        print("🎉 所有表修復完成！")
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    fix_remaining_tables() 