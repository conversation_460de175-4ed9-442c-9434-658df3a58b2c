#!/usr/bin/env python3
"""
完整的資料遷移腳本 - 將所有SQLite資料遷移到CockroachDB
"""
import psycopg2
import sqlite3

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def migrate_all_data():
    # 連接資料庫
    pg_conn = psycopg2.connect(POSTGRES_URL)
    pg_cursor = pg_conn.cursor()
    
    sqlite_conn = sqlite3.connect('pharmacy.db')
    sqlite_conn.row_factory = sqlite3.Row
    sqlite_cursor = sqlite_conn.cursor()
    
    print("🚀 開始完整資料遷移...")
    
    try:
        # 0. 先重建使用者表格（因為之前已經遷移了）
        print("\n👥 確保使用者資料存在...")
        pg_cursor.execute("DROP TABLE IF EXISTS users CASCADE")
        pg_cursor.execute("""
            CREATE TABLE users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                pharmacy_name VARCHAR(200) NOT NULL,
                phone VARCHAR(20),
                line_user_id VARCHAR(100),
                notification_email BOOLEAN NOT NULL DEFAULT true,
                notification_line BOOLEAN NOT NULL DEFAULT false,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        sqlite_cursor.execute("SELECT * FROM users")
        users = sqlite_cursor.fetchall()
        for user in users:
            pg_cursor.execute("""
                INSERT INTO users (username, email, password_hash, pharmacy_name, phone, 
                                 line_user_id, notification_email, notification_line, 
                                 created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                user['username'], user['email'], user['password_hash'], 
                user['pharmacy_name'], user['phone'], user['line_user_id'],
                bool(user['notification_email']), bool(user['notification_line']),
                user['created_at'], user['updated_at']
            ))
        print(f"✅ 遷移了 {len(users)} 筆使用者資料")
        
        # 1. 遷移產品資料
        print("\n📦 遷移產品資料...")
        pg_cursor.execute("DROP TABLE IF EXISTS products CASCADE")
        pg_cursor.execute("""
            CREATE TABLE products (
                id SERIAL PRIMARY KEY,
                nhi_code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(200) NOT NULL,
                manufacturer VARCHAR(100) NOT NULL,
                unit VARCHAR(20) NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                stock_quantity INTEGER NOT NULL DEFAULT 0,
                description TEXT,
                is_active BOOLEAN NOT NULL DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        sqlite_cursor.execute("SELECT * FROM products")
        products = sqlite_cursor.fetchall()
        for product in products:
            pg_cursor.execute("""
                INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, 
                                    stock_quantity, description, is_active, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                product['nhi_code'], product['name'], product['manufacturer'], product['unit'],
                product['unit_price'], product['stock_quantity'], product['description'],
                bool(product['is_active']), product['created_at'], product['updated_at']
            ))
        print(f"✅ 遷移了 {len(products)} 筆產品資料")
        
        # 2. 遷移訂單資料
        print("\n📋 遷移訂單資料...")
        pg_cursor.execute("DROP TABLE IF EXISTS orders CASCADE")
        pg_cursor.execute("""
            CREATE TABLE orders (
                id SERIAL PRIMARY KEY,
                order_number VARCHAR(50) UNIQUE NOT NULL,
                user_id INTEGER NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                total_amount DECIMAL(10,2) NOT NULL,
                notes TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """)
        
        sqlite_cursor.execute("SELECT * FROM orders")
        orders = sqlite_cursor.fetchall()
        for order in orders:
            pg_cursor.execute("""
                INSERT INTO orders (order_number, user_id, status, total_amount, 
                                  notes, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                order['order_number'], order['user_id'], order['status'], 
                order['total_amount'], order['notes'], order['created_at'], order['updated_at']
            ))
        print(f"✅ 遷移了 {len(orders)} 筆訂單資料")
        
        # 3. 遷移訂單項目
        print("\n📦 遷移訂單項目...")
        pg_cursor.execute("DROP TABLE IF EXISTS order_items CASCADE")
        pg_cursor.execute("""
            CREATE TABLE order_items (
                id SERIAL PRIMARY KEY,
                order_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                subtotal DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(id),
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        """)
        
        sqlite_cursor.execute("SELECT * FROM order_items")
        order_items = sqlite_cursor.fetchall()
        for item in order_items:
            pg_cursor.execute("""
                INSERT INTO order_items (order_id, product_id, quantity, unit_price, 
                                       subtotal, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                item['order_id'], item['product_id'], item['quantity'], 
                item['unit_price'], item['subtotal'], item['created_at']
            ))
        print(f"✅ 遷移了 {len(order_items)} 筆訂單項目")
        
        # 4. 建立索引
        print("\n🏗️  建立索引...")
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_products_nhi_code ON products(nhi_code)",
            "CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)",
            "CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id)",
            "CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id)"
        ]
        
        for index_sql in indexes:
            pg_cursor.execute(index_sql)
        print("✅ 索引建立完成")
        
        # 提交所有變更
        pg_conn.commit()
        print("\n🎉 完整資料遷移成功！")
        
        # 驗證結果
        print("\n📊 遷移結果驗證:")
        pg_cursor.execute("SELECT COUNT(*) FROM users")
        print(f"  👥 使用者: {pg_cursor.fetchone()[0]} 筆")
        
        pg_cursor.execute("SELECT COUNT(*) FROM products")
        print(f"  📦 產品: {pg_cursor.fetchone()[0]} 筆")
        
        pg_cursor.execute("SELECT COUNT(*) FROM orders")
        print(f"  📋 訂單: {pg_cursor.fetchone()[0]} 筆")
        
        pg_cursor.execute("SELECT COUNT(*) FROM order_items")
        print(f"  📦 訂單項目: {pg_cursor.fetchone()[0]} 筆")
        
    except Exception as e:
        print(f"❌ 遷移過程中發生錯誤: {e}")
        pg_conn.rollback()
        raise
    finally:
        pg_cursor.close()
        pg_conn.close()
        sqlite_conn.close()

if __name__ == "__main__":
    migrate_all_data()