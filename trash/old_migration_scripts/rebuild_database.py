#!/usr/bin/env python3
import psycopg2

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def rebuild_database():
    conn = psycopg2.connect(POSTGRES_URL)
    cursor = conn.cursor()
    
    print("🔧 重新建立整個資料庫...")
    
    try:
        # 刪除所有表
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
        """)
        tables = cursor.fetchall()
        
        print(f"🗑️ 刪除 {len(tables)} 個表...")
        for table in tables:
            table_name = table[0]
            # 為 SQL 保留字加上引號
            quoted_table_name = f'"{table_name}"' if table_name.upper() in ['ORDER', 'USER', 'GROUP', 'INDEX', 'TABLE'] else table_name
            cursor.execute(f"DROP TABLE IF EXISTS {quoted_table_name} CASCADE")
            print(f"  - 刪除 {table_name}")
        
        # 重新建立所有表
        print("\n🏗️ 重新建立所有表...")
        
        # 1. 建立 roles 表
        cursor.execute("""
            CREATE TABLE roles (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("  ✅ 建立 roles 表")
        
        # 2. 建立 permissions 表
        cursor.execute("""
            CREATE TABLE permissions (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                name VARCHAR(100) UNIQUE NOT NULL,
                description TEXT,
                resource VARCHAR(50) NOT NULL,
                action VARCHAR(50) NOT NULL,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("  ✅ 建立 permissions 表")
        
        # 3. 建立 role_permissions 表
        cursor.execute("""
            CREATE TABLE role_permissions (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                role_id INT8 NOT NULL,
                permission_id INT8 NOT NULL,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                UNIQUE(role_id, permission_id)
            )
        """)
        print("  ✅ 建立 role_permissions 表")
        
        # 4. 建立 users 表
        cursor.execute("""
            CREATE TABLE users (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                pharmacy_name VARCHAR(200) NOT NULL,
                phone VARCHAR(20),
                line_user_id VARCHAR(100),
                notification_email BOOLEAN NOT NULL DEFAULT true,
                notification_line BOOLEAN NOT NULL DEFAULT false,
                role_id INT8 NOT NULL DEFAULT 3,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("  ✅ 建立 users 表")
        
        # 5. 建立 products 表
        cursor.execute("""
            CREATE TABLE products (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                nhi_code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(200) NOT NULL,
                manufacturer VARCHAR(100) NOT NULL,
                unit VARCHAR(20) NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                stock_quantity INT8 NOT NULL DEFAULT 0,
                description TEXT,
                is_active BOOLEAN NOT NULL DEFAULT true,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("  ✅ 建立 products 表")
        
        # 6. 建立 carts 表
        cursor.execute("""
            CREATE TABLE carts (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                user_id INT8 NOT NULL,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp(),
                UNIQUE(user_id)
            )
        """)
        print("  ✅ 建立 carts 表")
        
        # 7. 建立 cart_items 表
        cursor.execute("""
            CREATE TABLE cart_items (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                cart_id INT8 NOT NULL,
                product_id INT8 NOT NULL,
                quantity INT8 NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                subtotal DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp(),
                UNIQUE(cart_id, product_id)
            )
        """)
        print("  ✅ 建立 cart_items 表")
        
        # 8. 建立 orders 表
        cursor.execute("""
            CREATE TABLE orders (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                order_number VARCHAR(50) UNIQUE NOT NULL,
                user_id INT8 NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                total_amount DECIMAL(10,2) NOT NULL,
                notes TEXT,
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("  ✅ 建立 orders 表")
        
        # 9. 建立 order_items 表
        cursor.execute("""
            CREATE TABLE order_items (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                order_id INT8 NOT NULL,
                product_id INT8 NOT NULL,
                quantity INT8 NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                subtotal DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("  ✅ 建立 order_items 表")
        
        # 10. 建立 notification_preferences 表
        cursor.execute("""
            CREATE TABLE notification_preferences (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                user_id INT8 NOT NULL,
                email_enabled BOOLEAN NOT NULL DEFAULT true,
                line_enabled BOOLEAN NOT NULL DEFAULT false,
                line_user_id VARCHAR(100),
                created_at TIMESTAMPTZ DEFAULT current_timestamp(),
                updated_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("  ✅ 建立 notification_preferences 表")
        
        # 11. 建立 notifications 表
        cursor.execute("""
            CREATE TABLE notifications (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                user_id INT8 NOT NULL,
                type VARCHAR(20) NOT NULL,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                sent_at TIMESTAMPTZ,
                created_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("  ✅ 建立 notifications 表")
        
        # 12. 建立 backup_logs 表
        cursor.execute("""
            CREATE TABLE backup_logs (
                id INT8 DEFAULT unique_rowid() PRIMARY KEY,
                backup_file VARCHAR(255) NOT NULL,
                file_size INT8 NOT NULL,
                status VARCHAR(20) NOT NULL,
                uploaded_to_gcp BOOLEAN NOT NULL DEFAULT false,
                created_at TIMESTAMPTZ DEFAULT current_timestamp()
            )
        """)
        print("  ✅ 建立 backup_logs 表")
        
        # 插入預設資料
        print("\n📝 插入預設資料...")
        
        # 插入預設角色
        default_roles = [
            ('super_admin', '超級管理員'),
            ('admin', '管理員'),
            ('pharmacy', '藥局'),
            ('staff', '員工')
        ]
        
        for role_name, description in default_roles:
            cursor.execute("""
                INSERT INTO roles (name, description)
                VALUES (%s, %s)
            """, (role_name, description))
        print("  ✅ 插入預設角色")
        
        # 插入預設權限
        default_permissions = [
            ('user_management', '用戶管理', 'users', 'manage'),
            ('product_management', '產品管理', 'products', 'manage'),
            ('order_management', '訂單管理', 'orders', 'manage'),
            ('view_all_orders', '查看所有訂單', 'orders', 'view_all'),
            ('system_admin', '系統管理', 'system', 'admin')
        ]
        
        for perm_name, description, resource, action in default_permissions:
            cursor.execute("""
                INSERT INTO permissions (name, description, resource, action)
                VALUES (%s, %s, %s, %s)
            """, (perm_name, description, resource, action))
        print("  ✅ 插入預設權限")
        
        # 為超級管理員分配所有權限
        cursor.execute("SELECT id FROM roles WHERE name = 'super_admin'")
        super_admin_id = cursor.fetchone()[0]
        
        cursor.execute("SELECT id FROM permissions")
        permission_ids = [row[0] for row in cursor.fetchall()]
        
        for perm_id in permission_ids:
            cursor.execute("""
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES (%s, %s)
            """, (super_admin_id, perm_id))
        print("  ✅ 為超級管理員分配權限")
        
        # 測試插入
        print("\n🧪 測試插入...")
        
        # 測試 users 插入
        cursor.execute("""
            INSERT INTO users (username, email, password_hash, pharmacy_name)
            VALUES ('test_rebuild', '<EMAIL>', 'hash', 'test')
            RETURNING id
        """)
        user_result = cursor.fetchone()
        print(f"  ✅ users 測試插入成功: {user_result}")
        
        # 測試 products 插入
        cursor.execute("""
            INSERT INTO products (nhi_code, name, manufacturer, unit, unit_price, stock_quantity)
            VALUES ('REBUILD123', 'Rebuild Test Product', 'Rebuild Manufacturer', '顆', 100.00, 100)
            RETURNING id
        """)
        product_result = cursor.fetchone()
        print(f"  ✅ products 測試插入成功: {product_result}")
        
        # 測試 orders 插入
        cursor.execute("""
            INSERT INTO orders (order_number, user_id, total_amount)
            VALUES ('REBUILD-001', %s, 100.00)
            RETURNING id
        """, (user_result[0],))
        order_result = cursor.fetchone()
        print(f"  ✅ orders 測試插入成功: {order_result}")
        
        # 清理測試資料
        cursor.execute("DELETE FROM orders WHERE order_number = 'REBUILD-001'")
        cursor.execute("DELETE FROM products WHERE nhi_code = 'REBUILD123'")
        cursor.execute("DELETE FROM users WHERE username = 'test_rebuild'")
        print("  🧹 清理測試資料")
        
        conn.commit()
        print("\n🎉 資料庫重建完成！")
        
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    rebuild_database() 