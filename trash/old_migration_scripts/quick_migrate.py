#!/usr/bin/env python3
import psycopg2
import sqlite3

POSTGRES_URL = "postgresql://arguskao:<EMAIL>:26257/happy?sslmode=require"

def migrate():
    # 連接 PostgreSQL
    pg_conn = psycopg2.connect(POSTGRES_URL)
    pg_cursor = pg_conn.cursor()
    
    # 連接 SQLite
    sqlite_conn = sqlite3.connect('pharmacy.db')
    sqlite_conn.row_factory = sqlite3.Row
    sqlite_cursor = sqlite_conn.cursor()
    
    print("🚀 開始快速遷移...")
    
    # 建立基本表格結構
    pg_cursor.execute("DROP TABLE IF EXISTS users CASCADE")
    pg_cursor.execute("""
        CREATE TABLE users (
            id SERIAL PRIMARY KEY,
            username VARCHA<PERSON>(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            pharmacy_name VARCHAR(200) NOT NULL,
            phone VARCHAR(20),
            line_user_id VARCHAR(100),
            notification_email BOOLEAN NOT NULL DEFAULT true,
            notification_line BOOLEAN NOT NULL DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # 遷移使用者
    sqlite_cursor.execute("SELECT * FROM users")
    users = sqlite_cursor.fetchall()
    for user in users:
        pg_cursor.execute("""
            INSERT INTO users (username, email, password_hash, pharmacy_name, phone, 
                             line_user_id, notification_email, notification_line, 
                             created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            user['username'], user['email'], user['password_hash'], 
            user['pharmacy_name'], user['phone'], user['line_user_id'],
            bool(user['notification_email']), bool(user['notification_line']),
            user['created_at'], user['updated_at']
        ))
    
    print(f"✅ 遷移了 {len(users)} 筆使用者資料")
    
    # 提交並關閉
    pg_conn.commit()
    pg_cursor.close()
    pg_conn.close()
    sqlite_conn.close()
    
    print("🎉 遷移完成！")

if __name__ == "__main__":
    migrate()