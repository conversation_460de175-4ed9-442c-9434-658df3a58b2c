#!/usr/bin/env python3
"""
修復遷移狀態 - 讓sqlx知道遷移已經完成
"""

import psycopg2
import os
import hashlib

def get_database_url():
    """從環境變量讀取數據庫連接信息"""
    env_vars = {}
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    return env_vars.get('DATABASE_URL')

def calculate_checksum(content):
    """計算文件內容的校驗和"""
    return hashlib.sha384(content.encode('utf-8')).hexdigest()

def fix_migration_state():
    """修復遷移狀態"""
    print("正在修復遷移狀態...")
    
    database_url = get_database_url()
    conn = psycopg2.connect(database_url)
    
    try:
        cursor = conn.cursor()
        
        # 創建_sqlx_migrations表（如果不存在）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS _sqlx_migrations (
                version BIGINT PRIMARY KEY,
                description TEXT NOT NULL,
                installed_on TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                success BOOLEAN NOT NULL,
                checksum BYTEA NOT NULL,
                execution_time BIGINT NOT NULL
            );
        """)
        
        # 讀取遷移文件並記錄為已完成
        migration_files = [
            ('20240101000001', 'initial_schema', 'migrations/20240101000001_initial_schema.sql'),
            ('20240102000001', 'add_cart_tables', 'migrations/20240102000001_add_cart_tables.sql'),
            ('20240103000001', 'add_user_roles', 'migrations/20240103000001_add_user_roles.sql')
        ]
        
        for version, description, file_path in migration_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                checksum = calculate_checksum(content)
                version_int = int(version)
                
                # 檢查是否已經記錄
                cursor.execute("SELECT version FROM _sqlx_migrations WHERE version = %s", (version_int,))
                if cursor.fetchone() is None:
                    # 插入遷移記錄
                    cursor.execute("""
                        INSERT INTO _sqlx_migrations (version, description, success, checksum, execution_time)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (version_int, description, True, checksum.encode('utf-8'), 0))
                    
                    print(f"  ✓ 記錄遷移: {version} - {description}")
                else:
                    print(f"  - 遷移已存在: {version} - {description}")
            else:
                print(f"  ❌ 找不到遷移文件: {file_path}")
        
        conn.commit()
        
        # 驗證遷移狀態
        cursor.execute("SELECT version, description, success FROM _sqlx_migrations ORDER BY version")
        migrations = cursor.fetchall()
        
        print("\n當前遷移狀態:")
        for version, description, success in migrations:
            status = "✓" if success else "❌"
            print(f"  {status} {version}: {description}")
        
        print("\n✅ 遷移狀態修復完成")
        
    except Exception as e:
        print(f"修復過程中發生錯誤: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_migration_state()