#!/usr/bin/env python3
"""
簡化的SQLite to Neon DB遷移腳本
使用sqlx CLI工具進行遷移
"""

import sqlite3
import json
import os
import subprocess
from datetime import datetime

def backup_sqlite_data(db_path='pharmacy.db'):
    """備份SQLite數據到JSON文件"""
    print("正在備份SQLite數據...")
    
    if not os.path.exists(db_path):
        print(f"錯誤：找不到SQLite數據庫文件 {db_path}")
        return None
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    backup_data = {}
    tables = ['users', 'products', 'orders', 'order_items', 'carts', 'cart_items', 
              'notifications', 'notification_preferences', 'backup_logs']
    
    for table in tables:
        try:
            cursor.execute(f"SELECT * FROM {table}")
            rows = cursor.fetchall()
            backup_data[table] = []
            
            for row in rows:
                row_dict = dict(row)
                backup_data[table].append(row_dict)
            
            print(f"  {table}: {len(backup_data[table])} 條記錄")
        except Exception as e:
            print(f"  警告：無法備份表格 {table}: {e}")
            backup_data[table] = []
    
    conn.close()
    
    # 保存備份文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"sqlite_backup_{timestamp}.json"
    
    with open(backup_filename, 'w', encoding='utf-8') as f:
        json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"SQLite數據已備份到: {backup_filename}")
    return backup_data, backup_filename

def generate_sql_inserts(backup_data, output_file='neon_migration.sql'):
    """生成PostgreSQL插入語句"""
    print(f"正在生成SQL插入語句到 {output_file}...")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- Neon DB 數據遷移SQL\n")
        f.write(f"-- 生成時間: {datetime.now()}\n\n")
        
        # 清空現有數據
        f.write("-- 清空現有數據\n")
        clear_tables = [
            'cart_items', 'carts', 'order_items', 'orders',
            'role_permissions', 'notification_preferences', 'notifications',
            'users', 'products', 'permissions', 'roles', 'backup_logs'
        ]
        
        for table in clear_tables:
            f.write(f"DELETE FROM {table};\n")
        
        f.write("\n-- 重置序列\n")
        for table in ['users', 'products', 'orders', 'order_items', 'carts', 'cart_items', 
                     'notifications', 'notification_preferences', 'backup_logs']:
            f.write(f"ALTER SEQUENCE {table}_id_seq RESTART WITH 1;\n")
        
        f.write("\n-- 插入數據\n")
        
        # 按依賴順序插入數據
        insert_order = ['users', 'products', 'orders', 'order_items', 'carts', 'cart_items',
                       'notifications', 'notification_preferences', 'backup_logs']
        
        for table in insert_order:
            if table in backup_data and backup_data[table]:
                f.write(f"\n-- 插入 {table} 數據\n")
                
                for record in backup_data[table]:
                    if table == 'users':
                        f.write(generate_user_insert(record))
                    elif table == 'products':
                        f.write(generate_product_insert(record))
                    elif table == 'orders':
                        f.write(generate_order_insert(record))
                    elif table == 'order_items':
                        f.write(generate_order_item_insert(record))
                    elif table == 'carts':
                        f.write(generate_cart_insert(record))
                    elif table == 'cart_items':
                        f.write(generate_cart_item_insert(record))
                    elif table == 'notifications':
                        f.write(generate_notification_insert(record))
                    elif table == 'notification_preferences':
                        f.write(generate_notification_pref_insert(record))
                    elif table == 'backup_logs':
                        f.write(generate_backup_log_insert(record))
        
        # 修復序列
        f.write("\n-- 修復序列\n")
        for table in ['users', 'products', 'orders', 'order_items', 'carts', 'cart_items',
                     'notifications', 'notification_preferences', 'backup_logs']:
            f.write(f"""SELECT setval(pg_get_serial_sequence('{table}', 'id'), 
                     COALESCE((SELECT MAX(id) FROM {table}), 1), false);\n""")
    
    print(f"SQL文件已生成: {output_file}")

def escape_sql_string(value):
    """轉義SQL字符串"""
    if value is None:
        return 'NULL'
    if isinstance(value, (int, float)):
        return str(value)
    if isinstance(value, bool):
        return 'true' if value else 'false'
    # 轉義單引號
    return f"'{str(value).replace(chr(39), chr(39)+chr(39))}'"

def generate_user_insert(record):
    """生成用戶插入語句"""
    return f"""INSERT INTO users (id, username, email, password_hash, pharmacy_name, phone, line_user_id, notification_email, notification_line, role_id, created_at, updated_at) VALUES ({record['id']}, {escape_sql_string(record['username'])}, {escape_sql_string(record['email'])}, {escape_sql_string(record['password_hash'])}, {escape_sql_string(record['pharmacy_name'])}, {escape_sql_string(record.get('phone'))}, {escape_sql_string(record.get('line_user_id'))}, {record.get('notification_email', True)}, {record.get('notification_line', False)}, {record.get('role_id', 3)}, {escape_sql_string(record.get('created_at'))}, {escape_sql_string(record.get('updated_at'))});
"""

def generate_product_insert(record):
    """生成產品插入語句"""
    return f"""INSERT INTO products (id, nhi_code, name, english_name, ingredients, dosage_form, manufacturer, unit, unit_price, stock_quantity, description, is_active, created_at, updated_at) VALUES ({record['id']}, {escape_sql_string(record['nhi_code'])}, {escape_sql_string(record['name'])}, {escape_sql_string(record.get('english_name'))}, {escape_sql_string(record.get('ingredients'))}, {escape_sql_string(record.get('dosage_form'))}, {escape_sql_string(record['manufacturer'])}, {escape_sql_string(record['unit'])}, {record['unit_price']}, {record['stock_quantity']}, {escape_sql_string(record.get('description'))}, {record.get('is_active', True)}, {escape_sql_string(record.get('created_at'))}, {escape_sql_string(record.get('updated_at'))});
"""

def generate_order_insert(record):
    """生成訂單插入語句"""
    return f"""INSERT INTO orders (id, order_number, user_id, status, total_amount, notes, created_at, updated_at) VALUES ({record['id']}, {escape_sql_string(record['order_number'])}, {record['user_id']}, {escape_sql_string(record['status'])}, {record['total_amount']}, {escape_sql_string(record.get('notes'))}, {escape_sql_string(record.get('created_at'))}, {escape_sql_string(record.get('updated_at'))});
"""

def generate_order_item_insert(record):
    """生成訂單項目插入語句"""
    return f"""INSERT INTO order_items (id, order_id, product_id, quantity, unit_price, total_price, created_at) VALUES ({record['id']}, {record['order_id']}, {record['product_id']}, {record['quantity']}, {record['unit_price']}, {record['total_price']}, {escape_sql_string(record.get('created_at'))});
"""

def generate_cart_insert(record):
    """生成購物車插入語句"""
    return f"""INSERT INTO carts (id, user_id, created_at, updated_at) VALUES ({record['id']}, {record['user_id']}, {escape_sql_string(record.get('created_at'))}, {escape_sql_string(record.get('updated_at'))});
"""

def generate_cart_item_insert(record):
    """生成購物車項目插入語句"""
    return f"""INSERT INTO cart_items (id, cart_id, product_id, quantity, unit_price, subtotal, created_at, updated_at) VALUES ({record['id']}, {record['cart_id']}, {record['product_id']}, {record['quantity']}, {record['unit_price']}, {record['subtotal']}, {escape_sql_string(record.get('created_at'))}, {escape_sql_string(record.get('updated_at'))});
"""

def generate_notification_insert(record):
    """生成通知插入語句"""
    return f"""INSERT INTO notifications (id, user_id, type, title, message, status, sent_at, created_at) VALUES ({record['id']}, {record['user_id']}, {escape_sql_string(record['type'])}, {escape_sql_string(record['title'])}, {escape_sql_string(record['message'])}, {escape_sql_string(record['status'])}, {escape_sql_string(record.get('sent_at'))}, {escape_sql_string(record.get('created_at'))});
"""

def generate_notification_pref_insert(record):
    """生成通知偏好插入語句"""
    return f"""INSERT INTO notification_preferences (id, user_id, email_enabled, line_enabled, line_user_id, created_at, updated_at) VALUES ({record['id']}, {record['user_id']}, {record['email_enabled']}, {record['line_enabled']}, {escape_sql_string(record.get('line_user_id'))}, {escape_sql_string(record.get('created_at'))}, {escape_sql_string(record.get('updated_at'))});
"""

def generate_backup_log_insert(record):
    """生成備份日誌插入語句"""
    return f"""INSERT INTO backup_logs (id, backup_file, file_size, status, uploaded_to_gcp, created_at) VALUES ({record['id']}, {escape_sql_string(record['backup_file'])}, {record['file_size']}, {escape_sql_string(record['status'])}, {record['uploaded_to_gcp']}, {escape_sql_string(record.get('created_at'))});
"""

def main():
    print("=== 簡化的SQLite to Neon DB 遷移工具 ===")
    print()
    
    # 1. 備份SQLite數據
    result = backup_sqlite_data()
    if not result:
        return
    
    backup_data, backup_filename = result
    
    # 2. 生成SQL插入語句
    generate_sql_inserts(backup_data)
    
    print()
    print("=== 遷移準備完成 ===")
    print("已生成以下文件：")
    print(f"  - {backup_filename} (SQLite數據備份)")
    print("  - neon_migration.sql (PostgreSQL遷移SQL)")
    print()
    print("接下來的步驟：")
    print("1. 確保你的Rust應用程序已停止")
    print("2. 運行遷移：")
    print("   sqlx migrate run --database-url $DATABASE_URL")
    print("3. 執行數據遷移：")
    print("   psql $DATABASE_URL -f neon_migration.sql")
    print("4. 重新啟動你的應用程序")
    print()
    print("或者運行完整的遷移腳本：")
    print("   python3 migrate_to_neon.py")

if __name__ == "__main__":
    main()