-- 第四步：插入預設角色和權限

-- 插入預設角色
INSERT INTO roles (name, description) VALUES 
    ('super_admin', '超級管理員 - 擁有所有權限'),
    ('admin', '管理員 - 可以管理產品和訂單'),
    ('pharmacy', '藥局用戶 - 可以下訂單和查看自己的資料'),
    ('viewer', '檢視者 - 只能查看資料')
ON CONFLICT (name) DO NOTHING;

-- 插入基本權限
INSERT INTO permissions (name, description, resource, action) VALUES 
    -- 產品管理權限
    ('products.create', '創建產品', 'products', 'create'),
    ('products.read', '查看產品', 'products', 'read'),
    ('products.update', '更新產品', 'products', 'update'),
    ('products.delete', '刪除產品', 'products', 'delete'),
    
    -- 訂單管理權限
    ('orders.create', '創建訂單', 'orders', 'create'),
    ('orders.read', '查看訂單', 'orders', 'read'),
    ('orders.update', '更新訂單', 'orders', 'update'),
    ('orders.delete', '刪除訂單', 'orders', 'delete'),
    ('orders.read_all', '查看所有用戶訂單', 'orders', 'read_all'),
    
    -- 用戶管理權限
    ('users.create', '創建用戶', 'users', 'create'),
    ('users.read', '查看用戶', 'users', 'read'),
    ('users.update', '更新用戶', 'users', 'update'),
    ('users.delete', '刪除用戶', 'users', 'delete'),
    ('users.read_all', '查看所有用戶', 'users', 'read_all'),
    
    -- 系統管理權限
    ('system.backup', '系統備份', 'system', 'backup'),
    ('system.settings', '系統設定', 'system', 'settings'),
    ('system.logs', '查看系統日誌', 'system', 'logs'),
    
    -- 角色和權限管理
    ('roles.create', '創建角色', 'roles', 'create'),
    ('roles.read', '查看角色', 'roles', 'read'),
    ('roles.update', '更新角色', 'roles', 'update'),
    ('roles.delete', '刪除角色', 'roles', 'delete'),
    
    -- 通知權限
    ('notifications.read', '查看通知', 'notifications', 'read'),
    ('notifications.send', '發送通知', 'notifications', 'send')
ON CONFLICT (name) DO NOTHING;

SELECT 'Step 4a completed: Roles and permissions inserted' as status;