-- 為現有產品添加 dosage_form 和 ingredients 值
-- 這個腳本會根據產品名稱推測劑型和成分含量

UPDATE products 
SET 
    dosage_form = CASE 
        WHEN name ILIKE '%錠%' OR name ILIKE '%tablet%' THEN '錠劑'
        WHEN name ILIKE '%膠囊%' OR name ILIKE '%capsule%' THEN '膠囊'
        WHEN name ILIKE '%糖漿%' OR name ILIKE '%syrup%' THEN '糖漿'
        WHEN name ILIKE '%注射%' OR name ILIKE '%injection%' THEN '注射劑'
        WHEN name ILIKE '%軟膏%' OR name ILIKE '%ointment%' THEN '軟膏'
        WHEN name ILIKE '%滴劑%' OR name ILIKE '%drop%' THEN '滴劑'
        WHEN name ILIKE '%粉%' OR name ILIKE '%powder%' THEN '散劑'
        WHEN name ILIKE '%液%' OR name ILIKE '%solution%' THEN '溶液'
        ELSE '錠劑'  -- 預設為錠劑
    END,
    ingredients = CASE 
        WHEN name ILIKE '%普拿疼%' OR name ILIKE '%paracetamol%' THEN 'Paracetamol 500mg'
        WHEN name ILIKE '%阿斯匹靈%' OR name ILIKE '%阿司匹林%' OR name ILIKE '%aspirin%' THEN 'Acetylsalicylic Acid 100mg'
        WHEN name ILIKE '%布洛芬%' OR name ILIKE '%ibuprofen%' THEN 'Ibuprofen 400mg'
        WHEN name ILIKE '%安莫西林%' OR name ILIKE '%amoxicillin%' THEN 'Amoxicillin 250mg'
        WHEN name ILIKE '%歐美拉唑%' OR name ILIKE '%omeprazole%' THEN 'Omeprazole 20mg'
        WHEN name ILIKE '%二甲雙胍%' OR name ILIKE '%metformin%' THEN 'Metformin 500mg'
        WHEN name ILIKE '%立普妥%' OR name ILIKE '%atorvastatin%' THEN 'Atorvastatin 10mg'
        WHEN name ILIKE '%洛薩坦%' OR name ILIKE '%losartan%' THEN 'Losartan 50mg'
        WHEN name ILIKE '%安普羅%' OR name ILIKE '%amlodipine%' THEN 'Amlodipine 5mg'
        WHEN name ILIKE '%500mg%' THEN SUBSTRING(name FROM '\w+') || ' 500mg'
        WHEN name ILIKE '%400mg%' THEN SUBSTRING(name FROM '\w+') || ' 400mg'
        WHEN name ILIKE '%250mg%' THEN SUBSTRING(name FROM '\w+') || ' 250mg'
        WHEN name ILIKE '%100mg%' THEN SUBSTRING(name FROM '\w+') || ' 100mg'
        WHEN name ILIKE '%20mg%' THEN SUBSTRING(name FROM '\w+') || ' 20mg'
        WHEN name ILIKE '%10mg%' THEN SUBSTRING(name FROM '\w+') || ' 10mg'
        WHEN name ILIKE '%5mg%' THEN SUBSTRING(name FROM '\w+') || ' 5mg'
        ELSE '主要成分 100mg'
    END
WHERE (dosage_form IS NULL OR dosage_form = '') OR (ingredients IS NULL OR ingredients = '');

-- 檢查更新結果
SELECT nhi_code, name, ingredients, dosage_form FROM products LIMIT 10;