#!/usr/bin/env python3
"""
檢查數據庫表格結構
"""

import psycopg2
import os

def get_database_url():
    """從環境變量讀取數據庫連接信息"""
    env_vars = {}
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    return env_vars.get('DATABASE_URL')

def check_schema():
    """檢查數據庫表格結構"""
    print("=== 檢查數據庫表格結構 ===")
    print()
    
    database_url = get_database_url()
    conn = psycopg2.connect(database_url)
    
    try:
        cursor = conn.cursor()
        
        # 檢查products表格的結構
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'products' AND table_schema = 'public'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        
        print("Products表格結構:")
        print("-" * 60)
        print(f"{'欄位名稱':<20} {'數據類型':<15} {'可空':<8} {'默認值'}")
        print("-" * 60)
        
        for column_name, data_type, is_nullable, column_default in columns:
            nullable = "是" if is_nullable == "YES" else "否"
            default = column_default if column_default else "無"
            print(f"{column_name:<20} {data_type:<15} {nullable:<8} {default}")
        
        print()
        
        # 檢查stock_quantity的具體信息
        cursor.execute("""
            SELECT column_name, data_type, numeric_precision, numeric_scale
            FROM information_schema.columns 
            WHERE table_name = 'products' AND column_name = 'stock_quantity'
            AND table_schema = 'public';
        """)
        
        stock_info = cursor.fetchone()
        if stock_info:
            column_name, data_type, precision, scale = stock_info
            print(f"stock_quantity詳細信息:")
            print(f"  數據類型: {data_type}")
            print(f"  精度: {precision}")
            print(f"  小數位: {scale}")
        
        # 檢查一些樣本數據
        cursor.execute("SELECT id, name, stock_quantity FROM products LIMIT 3")
        products = cursor.fetchall()
        
        print("\n樣本數據:")
        print("-" * 40)
        for product_id, name, stock_quantity in products:
            print(f"ID: {product_id}, 名稱: {name}, 庫存: {stock_quantity} (類型: {type(stock_quantity)})")
        
    except Exception as e:
        print(f"檢查過程中發生錯誤: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_schema()