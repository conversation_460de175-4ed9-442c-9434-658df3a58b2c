-- 第五步：設置角色權限關聯

-- 超級管理員 - 擁有所有權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'super_admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 管理員 - 產品、訂單、部分用戶管理權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'admin' AND p.name IN (
    'products.create', 'products.read', 'products.update', 'products.delete',
    'orders.create', 'orders.read', 'orders.update', 'orders.delete', 'orders.read_all',
    'users.read', 'users.read_all',
    'notifications.read', 'notifications.send'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 藥局用戶 - 基本訂單和產品查看權限
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'pharmacy' AND p.name IN (
    'products.read',
    'orders.create', 'orders.read', 'orders.update',
    'notifications.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 檢視者 - 只能查看
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'viewer' AND p.name IN (
    'products.read',
    'orders.read',
    'notifications.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 為現有用戶設置預設角色（pharmacy）
UPDATE users SET role_id = (SELECT id FROM roles WHERE name = 'pharmacy') WHERE role_id IS NULL;

-- 將 role_id 設為 NOT NULL（在設置預設值後）
DO $$ 
BEGIN 
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='role_id' AND is_nullable='YES') THEN
        ALTER TABLE users ALTER COLUMN role_id SET NOT NULL;
    END IF;
END $$;

SELECT 'Step 5 completed: Role assignments configured' as status;