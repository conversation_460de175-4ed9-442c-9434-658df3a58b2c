#!/usr/bin/env python3
import pandas as pd

# 讀取兩個檔案，不使用第一行作為欄位名稱
print('=== 重新檢查檔案結構（不使用header） ===')

df1 = pd.read_excel('1_3.xlsx', header=None)
df2 = pd.read_excel('2_3.xlsx', header=None)

print(f'1_3.xlsx: {len(df1)} 行, {len(df1.columns)} 欄')
print(f'2_3.xlsx: {len(df2)} 行, {len(df2.columns)} 欄')

print()
print('1_3.xlsx 前3行:')
print(df1.head(3))
print()
print('2_3.xlsx 前3行:')
print(df2.head(3))

print()
print('檢查欄位數量是否可以對齊...')
if len(df1.columns) != len(df2.columns):
    min_cols = min(len(df1.columns), len(df2.columns))
    max_cols = max(len(df1.columns), len(df2.columns))
    print(f'兩個檔案欄位數不同')
    print(f'最少欄位數: {min_cols}')
    print(f'最多欄位數: {max_cols}')
    print(f'可以取前 {min_cols} 欄進行合併，或者補齊欄位到 {max_cols} 欄')
else:
    print('兩個檔案欄位數相同，可以直接合併')