#!/usr/bin/env python3
"""
測試新的管理員訂單功能
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8080"

def test_new_admin_features():
    """測試新的管理員功能"""
    print("🎯 === 測試新的管理員訂單功能 ===")
    print()
    
    # 登錄獲取令牌
    admin_user = {"username": "admin", "password": "admin123"}
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json=admin_user)
    
    if login_response.status_code != 200:
        print("❌ 登錄失敗")
        return False
    
    token = login_response.json().get('data', {}).get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    # 測試1: 只有三種狀態的篩選
    print("1. 測試新的狀態篩選（只有待處理、檢貨中、已出貨）:")
    statuses = ['pending', 'processing', 'shipped']
    
    for status in statuses:
        response = requests.get(f"{BASE_URL}/api/orders/all?status={status}", headers=headers)
        
        if response.status_code == 200:
            orders = response.json().get('data', {}).get('orders', [])
            print(f"   {status.capitalize():12}: {len(orders):2d} 個訂單 ✅")
        else:
            print(f"   {status.capitalize():12}: API錯誤 ({response.status_code}) ❌")
    
    # 測試2: 日期篩選（今天和昨天）
    print("\n2. 測試日期篩選（今天和昨天的訂單）:")
    
    today = datetime.now()
    yesterday = today - timedelta(days=1)
    
    start_date = yesterday.strftime('%Y-%m-%dT00:00:00Z')
    end_date = today.strftime('%Y-%m-%dT23:59:59Z')
    
    date_response = requests.get(
        f"{BASE_URL}/api/orders/all?start_date={start_date}&end_date={end_date}", 
        headers=headers
    )
    
    if date_response.status_code == 200:
        date_orders = date_response.json().get('data', {}).get('orders', [])
        print(f"   今天和昨天的訂單: {len(date_orders)} 個 ✅")
        
        # 驗證日期範圍
        if len(date_orders) > 0:
            sample_order = date_orders[0]
            order_date = datetime.fromisoformat(sample_order['created_at'].replace('Z', '+00:00'))
            if yesterday.date() <= order_date.date() <= today.date():
                print("   日期範圍驗證正確 ✅")
            else:
                print("   日期範圍驗證失敗 ❌")
    else:
        print(f"   日期篩選失敗: {date_response.status_code} ❌")
    
    # 測試3: 單個訂單狀態更新
    print("\n3. 測試單個訂單狀態更新:")
    
    # 先獲取一個訂單
    all_orders_response = requests.get(f"{BASE_URL}/api/orders/all", headers=headers)
    if all_orders_response.status_code == 200:
        orders = all_orders_response.json().get('data', {}).get('orders', [])
        if len(orders) > 0:
            test_order = orders[0]
            order_id = test_order['id']
            original_status = test_order['status']
            
            print(f"   測試訂單: {test_order['order_number']} (原狀態: {original_status})")
            
            # 更新狀態為檢貨中
            update_response = requests.put(
                f"{BASE_URL}/api/orders/{order_id}/status",
                headers=headers,
                json={"status": "processing"}
            )
            
            if update_response.status_code == 200:
                print("   狀態更新為檢貨中 ✅")
                
                # 驗證狀態是否真的更新了
                verify_response = requests.get(f"{BASE_URL}/api/orders/all", headers=headers)
                if verify_response.status_code == 200:
                    updated_orders = verify_response.json().get('data', {}).get('orders', [])
                    updated_order = next((o for o in updated_orders if o['id'] == order_id), None)
                    if updated_order and updated_order['status'] == 'Processing':
                        print("   狀態更新驗證成功 ✅")
                    else:
                        print("   狀態更新驗證失敗 ❌")
            else:
                print(f"   狀態更新失敗: {update_response.status_code} ❌")
        else:
            print("   沒有找到測試訂單 ⚠️")
    
    # 測試4: 前端HTML檢查
    print("\n4. 測試前端HTML更新:")
    
    index_response = requests.get(f"{BASE_URL}/")
    if index_response.status_code == 200:
        html_content = index_response.text
        
        # 檢查批次操作元素
        if 'batch-operations' in html_content:
            print("   批次操作HTML元素存在 ✅")
        else:
            print("   批次操作HTML元素缺失 ❌")
        
        # 檢查新的狀態選項
        if 'value="processing">檢貨中' in html_content:
            print("   檢貨中狀態選項存在 ✅")
        else:
            print("   檢貨中狀態選項缺失 ❌")
        
        # 檢查是否移除了舊狀態
        if 'confirmed' not in html_content or 'delivered' not in html_content:
            print("   舊狀態選項已移除 ✅")
        else:
            print("   舊狀態選項仍存在 ⚠️")
    else:
        print(f"   前端頁面無法訪問: {index_response.status_code} ❌")
    
    print("\n🎉 新功能測試完成！")
    return True

if __name__ == "__main__":
    test_new_admin_features()