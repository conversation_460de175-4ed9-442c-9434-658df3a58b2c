#!/usr/bin/env python3
import pandas as pd
import sys

def process_2_xlsx():
    # 讀取Excel檔案
    print("正在讀取 2.xlsx...")
    df = pd.read_excel('2.xlsx')
    original_count = len(df)
    print(f"原始資料總數: {original_count}")
    
    # 健保代碼欄位 (第2欄，索引1)
    health_code_col = df.columns[1]
    # E欄位 (第5欄，索引4) 
    e_col = df.columns[4]
    # C欄位 (第3欄，索引2)
    c_col = df.columns[2]
    
    print(f"健保代碼欄位: {health_code_col}")
    print(f"E欄位: {e_col}")
    print(f"C欄位: {c_col}")
    
    # 第一步：處理E欄位=9991231的邏輯
    print("\n=== 第一步：處理E欄位邏輯 ===")
    
    # 找出每個健保代碼中有9991231的記錄
    codes_with_9991231 = df[df.iloc[:, 4] == 9991231].iloc[:, 1].unique()
    print(f"有E欄位=9991231的健保代碼數量: {len(codes_with_9991231)}")
    
    # 對於有9991231的健保代碼，只保留E欄位=9991231的記錄
    # 對於沒有9991231的健保代碼，保留所有記錄
    
    filtered_rows = []
    
    for code in df.iloc[:, 1].unique():
        code_data = df[df.iloc[:, 1] == code]
        
        # 檢查這個健保代碼是否有E欄位=9991231的記錄
        has_9991231 = any(code_data.iloc[:, 4] == 9991231)
        
        if has_9991231:
            # 只保留E欄位=9991231的記錄
            filtered_data = code_data[code_data.iloc[:, 4] == 9991231]
        else:
            # 保留所有記錄
            filtered_data = code_data
            
        filtered_rows.append(filtered_data)
    
    # 合併所有篩選後的資料
    step1_df = pd.concat(filtered_rows, ignore_index=True)
    step1_count = len(step1_df)
    
    print(f"第一步處理後資料總數: {step1_count}")
    print(f"第一步刪除的記錄數: {original_count - step1_count}")
    
    # 第二步：刪除C欄位=0的記錄
    print("\n=== 第二步：刪除C欄位=0的記錄 ===")
    
    # 統計C欄位=0的記錄數
    zero_count = len(step1_df[step1_df.iloc[:, 2] == 0])
    print(f"C欄位=0的記錄數: {zero_count}")
    
    # 刪除C欄位=0的記錄
    final_df = step1_df[step1_df.iloc[:, 2] != 0].copy()
    final_count = len(final_df)
    
    print(f"最終資料總數: {final_count}")
    print(f"第二步刪除的記錄數: {zero_count}")
    
    # 儲存結果
    output_file = '2_processed.xlsx'
    print(f"\n正在儲存到 {output_file}...")
    final_df.to_excel(output_file, index=False)
    
    print("處理完成！")
    print(f"原始檔案: 2.xlsx ({original_count} 筆記錄)")
    print(f"處理後檔案: {output_file} ({final_count} 筆記錄)")
    print(f"總共刪除: {original_count - final_count} 筆記錄")
    
    # 顯示最終統計資訊
    print("\n=== 最終統計資訊 ===")
    print(f"有E欄位=9991231的健保代碼: {len(codes_with_9991231)} 個")
    print(f"E欄位=9991231的總記錄數: {len(final_df[final_df.iloc[:, 4] == 9991231])}")
    print("\n最終C欄位統計 (前10個):")
    print(final_df.iloc[:, 2].value_counts().head(10))

if __name__ == "__main__":
    try:
        process_2_xlsx()
    except Exception as e:
        print(f"錯誤: {e}")
        sys.exit(1)