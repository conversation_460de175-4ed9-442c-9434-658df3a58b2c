#!/usr/bin/env python3
"""
測試管理員訂單管理功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_admin_orders():
    """測試管理員訂單管理功能"""
    print("🧪 === 管理員訂單管理功能測試 ===")
    print()
    
    # 測試數據
    admin_user = {
        "username": "admin",
        "password": "admin123"
    }
    
    regular_user = {
        "username": "testuser", 
        "password": "password123"
    }
    
    try:
        # 1. 測試管理員登錄
        print("1. 測試管理員登錄...")
        admin_login_response = requests.post(f"{BASE_URL}/api/auth/login", json=admin_user)
        
        if admin_login_response.status_code == 200:
            admin_token = admin_login_response.json().get('token')
            print(f"   ✅ 管理員登錄成功")
        else:
            print(f"   ❌ 管理員登錄失敗: {admin_login_response.status_code}")
            print(f"   響應: {admin_login_response.text}")
            return False
        
        # 2. 測試普通用戶登錄
        print("\n2. 測試普通用戶登錄...")
        user_login_response = requests.post(f"{BASE_URL}/api/auth/login", json=regular_user)
        
        if user_login_response.status_code == 200:
            user_token = user_login_response.json().get('token')
            print(f"   ✅ 普通用戶登錄成功")
        else:
            print(f"   ❌ 普通用戶登錄失敗: {user_login_response.status_code}")
            print(f"   響應: {user_login_response.text}")
            return False
        
        # 3. 測試管理員查看所有訂單
        print("\n3. 測試管理員查看所有訂單...")
        admin_headers = {"Authorization": f"Bearer {admin_token}"}
        
        admin_orders_response = requests.get(f"{BASE_URL}/api/orders/all", headers=admin_headers)
        
        if admin_orders_response.status_code == 200:
            admin_orders = admin_orders_response.json()
            orders_count = len(admin_orders.get('data', {}).get('orders', []))
            print(f"   ✅ 管理員可以查看所有訂單: {orders_count} 個訂單")
            
            if orders_count > 0:
                first_order = admin_orders['data']['orders'][0]
                print(f"   📋 第一個訂單: {first_order.get('order_number')} - {first_order.get('status')}")
                test_order_id = first_order.get('id')
            else:
                print("   ⚠️  沒有找到訂單，無法測試狀態更新功能")
                test_order_id = None
        else:
            print(f"   ❌ 管理員查看訂單失敗: {admin_orders_response.status_code}")
            print(f"   響應: {admin_orders_response.text}")
            test_order_id = None
        
        # 4. 測試普通用戶查看自己的訂單
        print("\n4. 測試普通用戶查看自己的訂單...")
        user_headers = {"Authorization": f"Bearer {user_token}"}
        
        user_orders_response = requests.get(f"{BASE_URL}/api/orders", headers=user_headers)
        
        if user_orders_response.status_code == 200:
            user_orders = user_orders_response.json()
            user_orders_count = len(user_orders.get('data', {}).get('orders', []))
            print(f"   ✅ 普通用戶可以查看自己的訂單: {user_orders_count} 個訂單")
        else:
            print(f"   ❌ 普通用戶查看訂單失敗: {user_orders_response.status_code}")
        
        # 5. 測試普通用戶嘗試查看所有訂單（應該失敗）
        print("\n5. 測試普通用戶嘗試查看所有訂單...")
        user_all_orders_response = requests.get(f"{BASE_URL}/api/orders/all", headers=user_headers)
        
        if user_all_orders_response.status_code == 403:
            print(f"   ✅ 普通用戶無法查看所有訂單（權限正確）")
        elif user_all_orders_response.status_code == 401:
            print(f"   ✅ 普通用戶無法查看所有訂單（需要認證）")
        else:
            print(f"   ❌ 權限控制可能有問題: {user_all_orders_response.status_code}")
        
        # 6. 測試管理員更新訂單狀態
        if test_order_id:
            print(f"\n6. 測試管理員更新訂單狀態（訂單ID: {test_order_id}）...")
            
            status_update = {"status": "confirmed"}
            update_response = requests.put(
                f"{BASE_URL}/api/orders/{test_order_id}/status", 
                headers=admin_headers,
                json=status_update
            )
            
            if update_response.status_code == 200:
                print(f"   ✅ 管理員成功更新訂單狀態為 'confirmed'")
                
                # 驗證狀態是否真的更新了
                verify_response = requests.get(f"{BASE_URL}/api/orders/all", headers=admin_headers)
                if verify_response.status_code == 200:
                    updated_orders = verify_response.json()
                    updated_order = next((o for o in updated_orders['data']['orders'] if o['id'] == test_order_id), None)
                    if updated_order and updated_order['status'] == 'Confirmed':
                        print(f"   ✅ 訂單狀態更新驗證成功")
                    else:
                        print(f"   ⚠️  訂單狀態更新驗證失敗")
            else:
                print(f"   ❌ 管理員更新訂單狀態失敗: {update_response.status_code}")
                print(f"   響應: {update_response.text}")
        
        # 7. 測試訂單篩選功能
        print("\n7. 測試訂單篩選功能...")
        
        filter_params = {"status": "confirmed"}
        filter_response = requests.get(
            f"{BASE_URL}/api/orders/all", 
            headers=admin_headers,
            params=filter_params
        )
        
        if filter_response.status_code == 200:
            filtered_orders = filter_response.json()
            filtered_count = len(filtered_orders.get('data', {}).get('orders', []))
            print(f"   ✅ 篩選功能正常: 找到 {filtered_count} 個已確認的訂單")
        else:
            print(f"   ❌ 篩選功能失敗: {filter_response.status_code}")
        
        # 8. 測試API響應時間
        print("\n8. 測試API響應時間...")
        start_time = time.time()
        perf_response = requests.get(f"{BASE_URL}/api/orders/all", headers=admin_headers)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000
        print(f"   📊 API響應時間: {response_time:.2f}ms")
        
        if response_time < 1000:
            print(f"   ✅ 響應時間良好")
        else:
            print(f"   ⚠️  響應時間較慢，可能需要優化")
        
        print("\n🎉 === 測試完成 ===")
        print("✅ 管理員訂單管理功能基本正常")
        print("\n📋 測試總結:")
        print("   • 管理員可以查看所有訂單")
        print("   • 普通用戶只能查看自己的訂單")
        print("   • 權限控制正常工作")
        print("   • 管理員可以更新訂單狀態")
        print("   • 篩選功能正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        return False

def test_frontend_integration():
    """測試前端集成"""
    print("\n🌐 === 前端集成測試 ===")
    
    try:
        # 測試靜態文件服務
        index_response = requests.get(f"{BASE_URL}/")
        
        if index_response.status_code == 200:
            print("✅ 前端頁面可以正常訪問")
            
            # 檢查是否包含管理員相關的HTML元素
            html_content = index_response.text
            
            if 'admin-order-controls' in html_content:
                print("✅ 管理員控制面板HTML元素存在")
            else:
                print("❌ 管理員控制面板HTML元素缺失")
            
            if 'order-stats' in html_content:
                print("✅ 訂單統計HTML元素存在")
            else:
                print("❌ 訂單統計HTML元素缺失")
                
            if 'status-filter' in html_content:
                print("✅ 狀態篩選HTML元素存在")
            else:
                print("❌ 狀態篩選HTML元素缺失")
        else:
            print(f"❌ 前端頁面無法訪問: {index_response.status_code}")
            
    except Exception as e:
        print(f"❌ 前端測試錯誤: {e}")

if __name__ == "__main__":
    print("🚀 開始測試管理員訂單管理功能...")
    print("=" * 50)
    
    # 等待服務器完全啟動
    time.sleep(2)
    
    # 測試後端API
    api_success = test_admin_orders()
    
    # 測試前端集成
    test_frontend_integration()
    
    print("\n" + "=" * 50)
    if api_success:
        print("🎉 測試完成！管理員訂單管理功能正常工作")
    else:
        print("⚠️  測試發現一些問題，請檢查日誌")