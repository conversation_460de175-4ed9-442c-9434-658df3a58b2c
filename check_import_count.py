#!/usr/bin/env python3
import pandas as pd

df = pd.read_excel('4.xlsx', header=None)
nhi_data = df.iloc[:, [1, 2]].copy()
nhi_data.columns = ['nhi_code', 'nhi_price']

# 套用相同的過濾條件
nhi_data = nhi_data.dropna()
nhi_data = nhi_data[nhi_data['nhi_code'] != '']
nhi_data = nhi_data[nhi_data['nhi_price'] != 0]
nhi_data = nhi_data.drop_duplicates(subset=['nhi_code'], keep='first')

print(f'4.xlsx 中應該匯入的有效記錄數: {len(nhi_data)}')
print('目前已匯入: 2000 筆')
remaining = len(nhi_data) - 2000
print(f'尚未匯入: {remaining} 筆')

if remaining > 0:
    print(f'\n匯入進度: {2000}/{len(nhi_data)} ({2000/len(nhi_data)*100:.1f}%)')
else:
    print('\n✅ 匯入完成！')