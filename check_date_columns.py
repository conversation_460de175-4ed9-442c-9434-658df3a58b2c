#!/usr/bin/env python3
import psycopg2

DATABASE_URL = "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require"

def check_date_columns():
    print("🔍 檢查所有資料表的日期欄位...")
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # 查詢所有日期/時間相關欄位
        cursor.execute("""
            SELECT 
                table_name,
                column_name,
                data_type,
                is_nullable
            FROM information_schema.columns 
            WHERE table_schema = 'public'
            AND (
                data_type LIKE '%timestamp%' OR 
                data_type = 'date' OR 
                data_type = 'time' OR
                column_name LIKE '%_at' OR
                column_name LIKE '%date%' OR
                column_name LIKE '%time%'
            )
            ORDER BY table_name, column_name
        """)
        
        columns = cursor.fetchall()
        
        if not columns:
            print("   ℹ️  沒有找到日期相關欄位")
            return True
        
        print(f"\n📅 找到 {len(columns)} 個日期相關欄位:")
        
        # 按表格分組顯示
        current_table = None
        for table_name, column_name, data_type, is_nullable in columns:
            if table_name != current_table:
                print(f"\n   📋 表格: {table_name}")
                current_table = table_name
            
            nullable_info = "NULL" if is_nullable == "YES" else "NOT NULL"
            print(f"      {column_name}: {data_type} {nullable_info}")
        
        # 檢查每個表的樣本數據
        print(f"\n📊 檢查各表的日期數據樣本:")
        
        tables_with_dates = set(row[0] for row in columns)
        for table_name in sorted(tables_with_dates):
            try:
                # 獲取該表的日期欄位
                table_date_columns = [row[1] for row in columns if row[0] == table_name]
                
                # 構建查詢語句
                date_cols = ", ".join(table_date_columns)
                cursor.execute(f"SELECT {date_cols} FROM {table_name} LIMIT 3")
                
                sample_data = cursor.fetchall()
                if sample_data:
                    print(f"\n   📋 {table_name} 樣本:")
                    for i, row in enumerate(sample_data, 1):
                        print(f"      第{i}筆: {dict(zip(table_date_columns, row))}")
                else:
                    print(f"\n   📋 {table_name}: 無數據")
                    
            except Exception as e:
                print(f"   ⚠️  讀取 {table_name} 失敗: {e}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎯 需要修改的項目:")
        print("   1. 前端顯示格式：將 timestamp 格式化為僅顯示日期")
        print("   2. API 回應：確保日期欄位以 YYYY-MM-DD 格式回傳")
        print("   3. 檢查現有的日期處理邏輯")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        return False

if __name__ == "__main__":
    success = check_date_columns()
    print(f"\n{'🎊 檢查完成!' if success else '❌ 檢查失敗'}")