#!/usr/bin/env python3
import pandas as pd
import psycopg2
import sys
from decimal import Decimal
import os
from datetime import datetime

def test_import():
    print("=== 測試匯入 4.xlsx 前10筆到 nhi_prices 資料表 ===")
    
    # 讀取 Excel 檔案，只取前10行
    print("正在讀取 4.xlsx (前10行)...")
    df = pd.read_excel('4.xlsx', header=None, nrows=10)
    
    # 提取 B 欄 (nhi_code) 和 C 欄 (nhi_price)
    nhi_data = df.iloc[:, [1, 2]].copy()
    nhi_data.columns = ['nhi_code', 'nhi_price']
    
    # 過濾掉空值和無效資料
    nhi_data = nhi_data.dropna()
    nhi_data = nhi_data[nhi_data['nhi_code'] != '']
    nhi_data = nhi_data[nhi_data['nhi_price'] != 0]
    
    print(f"有效測試資料: {len(nhi_data)} 筆")
    print("測試資料:")
    print(nhi_data)
    
    # 連接資料庫
    database_url = os.getenv('DATABASE_URL')
    print(f"\n正在連接資料庫...")
    
    try:
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        print("資料庫連接成功")
        
        # 清空資料表進行測試
        cursor.execute("DELETE FROM nhi_prices")
        conn.commit()
        print("清空資料表用於測試")
        
        # 簡單的插入SQL (不使用UPSERT)
        insert_sql = """
        INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price, effective_date)
        VALUES (%s, %s, %s, %s)
        """
        
        current_date = datetime.now().date()
        
        for index, row in nhi_data.iterrows():
            try:
                nhi_code = str(row['nhi_code']).strip()
                nhi_price = Decimal(str(row['nhi_price']))
                
                print(f"匯入: {nhi_code} = ${nhi_price}")
                
                cursor.execute(insert_sql, (
                    nhi_code,
                    nhi_price,
                    nhi_price,
                    current_date
                ))
                
            except Exception as e:
                print(f"錯誤: {e}")
                conn.rollback()
                continue
        
        conn.commit()
        
        # 檢查結果
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        count = cursor.fetchone()[0]
        print(f"\n匯入完成，共 {count} 筆記錄")
        
        if count > 0:
            cursor.execute("SELECT nhi_code, nhi_price, selling_price FROM nhi_prices")
            print("匯入的資料:")
            for row in cursor.fetchall():
                print(f"  {row[0]}: NHI=${row[1]}, 售價=${row[2]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"錯誤: {e}")
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        sys.exit(1)

if __name__ == "__main__":
    try:
        test_import()
    except Exception as e:
        print(f"執行失敗: {e}")
        sys.exit(1)