#!/usr/bin/env python3
import json
import requests

API_BASE = "http://localhost:8080"

def test_product_api():
    print("🔍 測試產品 API 實際回應...")
    
    try:
        response = requests.get(f"{API_BASE}/api/products", timeout=5)
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            # 打印完整回應以便檢查
            response_text = response.text
            print(f"回應長度: {len(response_text)}")
            print(f"前500字符: {response_text[:500]}")
            
            try:
                products = response.json()
                print(f"產品數量: {len(products) if isinstance(products, list) else '非列表'}")
                
                if isinstance(products, list) and len(products) > 0:
                    first_product = products[0]
                    print("第一個產品的字段:")
                    for key, value in first_product.items():
                        print(f"  {key}: {value}")
                        
                        # 特別檢查日期字段
                        if key in ['created_at', 'updated_at']:
                            print(f"    → 日期格式分析: {type(value)}, 長度: {len(str(value))}")
                            if isinstance(value, str):
                                # 檢查是否為 YYYY-MM-DD 格式
                                if len(value) == 10 and value.count('-') == 2:
                                    print(f"    ✅ {key} 格式正確 (僅日期)")
                                elif 'T' in value:
                                    print(f"    ❌ {key} 包含時間: {value}")
                                else:
                                    print(f"    ⚠️  {key} 格式不明: {value}")
                
                elif isinstance(products, dict):
                    print("回應是字典格式:")
                    print(json.dumps(products, indent=2, ensure_ascii=False))
                    
            except json.JSONDecodeError as e:
                print(f"JSON 解析錯誤: {e}")
                
        else:
            print(f"錯誤回應: {response.text}")
            
    except Exception as e:
        print(f"請求失敗: {e}")

if __name__ == "__main__":
    test_product_api()