# 藥品中盤商採購系統

一個使用 Rust 和 Axum 框架開發的現代化藥品採購管理系統。

## 功能特色

- 🔐 使用者認證與授權 (JWT)
- 📦 產品管理與庫存追蹤
- 🛒 購物車與訂單管理
- 📊 Excel/CSV 檔案匯入
- 📧 Email 通知系統
- 💬 Line Bot 整合
- ☁️ GCP 雲端備份
- 📝 完整的 API 文檔
- 🧪 全面的測試覆蓋

## 快速開始

### 1. 環境需求

- Rust 1.70+
- SQLite 3

### 2. 安裝與設定

```bash
# 複製專案
git clone <repository-url>
cd pharmacy-system

# 設定環境變數
cp .env.example .env
# 編輯 .env 檔案，至少設定 JWT_SECRET

# 安裝依賴並編譯
cargo build
```

### 3. 啟動系統

#### 生產模式
```bash
./start.sh
```

#### 開發模式 (推薦)
```bash
./dev.sh
```

或直接使用 cargo：
```bash
cargo run
```

### 4. 存取系統

#### Web 介面 (推薦)
- **主頁面**: http://localhost:8080/
- 功能完整的 Web 介面，包含：
  - 使用者註冊/登入
  - 產品瀏覽和搜尋
  - 購物車管理
  - 訂單管理
  - 個人資料設定

#### API 端點
- API 資訊: http://localhost:8080/api
- 健康檢查: http://localhost:8080/health
- 詳細健康檢查: http://localhost:8080/health/detailed

### 5. 快速測試

```bash
# 快速檢查系統狀態和可用端點
./quick_test.sh

# 完整 API 功能測試
./test_api.sh
```

## API 端點

### 認證
- `POST /api/auth/register` - 使用者註冊
- `POST /api/auth/login` - 使用者登入
- `POST /api/auth/refresh` - 刷新 token
- `GET /api/auth/me` - 取得當前使用者資訊
- `GET /api/auth/profile` - 取得使用者個人資料
- `POST /api/auth/profile` - 更新使用者個人資料

### 產品管理
- `GET /api/products` - 取得產品清單
- `GET /api/products/:id` - 取得產品詳情
- `GET /api/products/nhi/:nhi_code` - 根據健保代碼取得產品
- `POST /api/products/import/csv` - 匯入 CSV 檔案
- `POST /api/products/import/excel` - 匯入 Excel 檔案

### 購物車
- `GET /api/cart` - 取得購物車內容
- `POST /api/cart` - 添加商品到購物車
- `DELETE /api/cart/clear` - 清空購物車
- `GET /api/cart/summary` - 取得購物車摘要

### 訂單管理
- `GET /api/orders` - 取得訂單清單
- `POST /api/orders` - 建立訂單
- `POST /api/orders/cart` - 從購物車建立訂單
- `GET /api/orders/:id` - 取得訂單詳情
- `GET /api/orders/number/:order_number` - 根據訂單編號取得訂單

## API 使用範例

### 1. 註冊新使用者
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "pharmacy_user",
    "email": "<EMAIL>",
    "password": "secure_password",
    "pharmacy_name": "我的藥局",
    "phone": "0912345678"
  }'
```

### 2. 使用者登入
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "pharmacy_user",
    "password": "secure_password"
  }'
```

### 3. 取得產品清單 (需要認證)
```bash
# 先從登入回應中取得 token
TOKEN="your_jwt_token_here"

curl -X GET http://localhost:8080/api/products \
  -H "Authorization: Bearer $TOKEN"
```

### 4. 添加商品到購物車
```bash
curl -X POST http://localhost:8080/api/cart \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": 1,
    "quantity": 2
  }'
```

### 5. 從購物車建立訂單
```bash
curl -X POST http://localhost:8080/api/orders/cart \
  -H "Authorization: Bearer $TOKEN"
```

## 測試

### 執行所有測試
```bash
cargo test
```

### 執行整合測試
```bash
cargo test --test comprehensive_integration_test
```

### 執行端到端測試
```bash
cargo test --test end_to_end_test
```

## 環境變數說明

| 變數名稱 | 說明 | 預設值 |
|---------|------|--------|
| `DATABASE_URL` | 資料庫連線字串 | `sqlite:pharmacy.db` |
| `JWT_SECRET` | JWT 簽名密鑰 | **必須設定** |
| `PORT` | 伺服器埠號 | `8080` |
| `SMTP_HOST` | SMTP 伺服器 | `smtp.gmail.com` |
| `SMTP_USERNAME` | SMTP 使用者名稱 | - |
| `SMTP_PASSWORD` | SMTP 密碼 | - |

## 開發工具

### 安裝 cargo-watch (自動重載)
```bash
cargo install cargo-watch
```

### 程式碼格式化
```bash
cargo fmt
```

### 程式碼檢查
```bash
cargo clippy
```

## 專案結構

```
src/
├── api/           # API 路由和處理器
├── database/      # 資料庫連線和遷移
├── models/        # 資料模型
├── repositories/  # 資料存取層
├── services/      # 業務邏輯層
├── config.rs      # 配置管理
├── error.rs       # 錯誤處理
├── logging.rs     # 日誌系統
└── main.rs        # 應用程式入口

tests/
├── comprehensive_integration_test.rs  # 整合測試
├── end_to_end_test.rs                # 端到端測試
└── ...
```

## 貢獻指南

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 授權

本專案採用 MIT 授權 - 詳見 [LICENSE](LICENSE) 檔案。