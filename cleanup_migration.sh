#!/bin/bash

echo "=== 遷移清理腳本 ==="
echo

# 檢查trash目錄
if [ -d "trash" ]; then
    echo "trash目錄結構："
    echo "---------------"
    echo "📁 trash/"
    echo "  ├── 📁 check_scripts/ (13個檢查腳本)"
    echo "  ├── 📁 old_migration_scripts/ (19個舊遷移腳本)"
    echo "  ├── 📁 test_scripts/ (5個測試腳本)"
    echo "  └── 9個其他舊文件"
    echo
    total_files=$(find trash -name "*.py" | wc -l)
    echo "總共 $total_files 個Python文件已移到trash目錄"
    echo
fi

# 列出當前遷移相關文件
echo "當前目錄中的核心遷移文件："
echo "--------------------------------"
ls -la migrate_to_neon.py migrate_to_neon.sh test_migration_success.py 2>/dev/null || echo "沒有找到核心文件"

echo
echo "備份文件："
echo "----------"
ls -la *backup*.json *backup*.db 2>/dev/null || echo "沒有找到備份文件"

echo
echo "清理選項："
echo "1. 保留所有文件（推薦，直到確認遷移成功）"
echo "2. 只保留備份文件和主要腳本"
echo "3. 清理trash目錄中的舊文件"
echo "4. 完全清理（保留備份）"
echo "5. 退出"

read -p "請選擇 (1-5): " choice

case $choice in
    1)
        echo "保留所有文件。建議在確認應用程序正常運行後再清理。"
        ;;
    2)
        echo "保留核心文件，清理臨時文件..."
        # 保留主要的遷移腳本和備份
        echo "保留的文件："
        echo "- migrate_to_neon.py (主要遷移腳本)"
        echo "- migrate_to_neon.sh (Shell腳本)"
        echo "- test_migration_success.py (測試腳本)"
        echo "- *backup*.json (數據備份)"
        echo "- *backup*.db (SQLite備份)"
        ;;
    3)
        echo "清理trash目錄..."
        if [ -d "trash" ]; then
            read -p "確定要刪除trash目錄中的所有文件嗎？(y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                rm -rf trash/
                echo "trash目錄已清理。"
            else
                echo "取消清理trash目錄。"
            fi
        else
            echo "沒有找到trash目錄。"
        fi
        ;;
    4)
        echo "⚠️  警告：這將刪除除備份文件外的所有遷移腳本！"
        read -p "確定要繼續嗎？(y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            # 保留備份文件，刪除腳本
            rm -f migrate_to_neon.py migrate_to_neon.sh test_migration_success.py
            rm -f MIGRATION_ISSUES_AND_FIXES.md
            rm -rf trash/
            echo "清理完成。備份文件已保留："
            ls -la *backup*.json *backup*.db 2>/dev/null || echo "沒有找到備份文件"
        else
            echo "取消清理。"
        fi
        ;;
    5)
        echo "退出清理。"
        ;;
    *)
        echo "無效選擇。"
        ;;
esac

echo
echo "遷移狀態摘要："
echo "---------------"
echo "✓ SQLite數據已遷移到Neon DB"
echo "✓ 布爾值轉換問題已修復"
echo "✓ 錯誤處理已改進"
echo "✓ 測試超時問題已解決"
echo
echo "建議的後續步驟："
echo "1. 運行 python3 test_migration_success.py 測試遷移結果"
echo "2. 確認所有功能正常工作"
echo "3. 備份舊的SQLite文件到安全位置"
echo "4. 更新生產環境配置"
echo "5. 監控應用程序性能"