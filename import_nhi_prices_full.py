#!/usr/bin/env python3
import pandas as pd
import psycopg2
import sys
from decimal import Decimal
import os
from datetime import datetime

def full_import():
    print("=== 匯入 4.xlsx 全部資料到 nhi_prices 資料表 ===")
    
    # 讀取 Excel 檔案
    print("正在讀取 4.xlsx...")
    df = pd.read_excel('4.xlsx', header=None)
    print(f"Excel 檔案總行數: {len(df)}")
    
    # 提取 B 欄 (nhi_code) 和 C 欄 (nhi_price)
    nhi_data = df.iloc[:, [1, 2]].copy()
    nhi_data.columns = ['nhi_code', 'nhi_price']
    
    # 過濾掉空值和無效資料
    original_count = len(nhi_data)
    nhi_data = nhi_data.dropna()
    nhi_data = nhi_data[nhi_data['nhi_code'] != '']
    nhi_data = nhi_data[nhi_data['nhi_price'] != 0]  # 過濾掉價格為0的記錄
    
    # 去重 - 相同 nhi_code 只保留一筆
    nhi_data = nhi_data.drop_duplicates(subset=['nhi_code'], keep='first')
    
    filtered_count = len(nhi_data)
    print(f"過濾後有效資料: {filtered_count} 筆 (原始: {original_count} 筆)")
    print(f"已排除: {original_count - filtered_count} 筆無效或重複資料")
    
    if filtered_count == 0:
        print("沒有有效資料可匯入！")
        return
    
    # 顯示前後幾筆資料作為預覽
    print("\n前5筆將匯入的資料:")
    print(nhi_data.head())
    print("\n後5筆將匯入的資料:")
    print(nhi_data.tail())
    
    # 連接資料庫
    database_url = os.getenv('DATABASE_URL')
    print(f"\n正在連接資料庫...")
    
    try:
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        print("資料庫連接成功")
        
        # 檢查現有資料
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        existing_count = cursor.fetchone()[0]
        print(f"資料表現有記錄: {existing_count} 筆")
        
        # 清空資料表（因為我們要匯入新的完整資料）
        user_input = input("是否要清空現有資料並重新匯入？(y/N): ")
        if user_input.lower() == 'y':
            cursor.execute("DELETE FROM nhi_prices")
            conn.commit()
            print("已清空現有資料")
        
        # 批次插入SQL
        insert_sql = """
        INSERT INTO nhi_prices (nhi_code, nhi_price, selling_price, effective_date)
        VALUES (%s, %s, %s, %s)
        """
        
        current_date = datetime.now().date()
        batch_size = 1000
        success_count = 0
        error_count = 0
        
        # 準備批次資料
        batch_data = []
        
        for index, row in nhi_data.iterrows():
            try:
                nhi_code = str(row['nhi_code']).strip()
                nhi_price = Decimal(str(row['nhi_price']))
                
                batch_data.append((
                    nhi_code,
                    nhi_price,
                    nhi_price,  # selling_price 設為與 nhi_price 相同
                    current_date
                ))
                
                # 當批次資料達到指定大小時，執行批次插入
                if len(batch_data) >= batch_size:
                    try:
                        cursor.executemany(insert_sql, batch_data)
                        conn.commit()
                        success_count += len(batch_data)
                        print(f"已匯入 {success_count} 筆...")
                        batch_data = []
                    except Exception as e:
                        print(f"批次匯入失敗: {e}")
                        conn.rollback()
                        error_count += len(batch_data)
                        batch_data = []
                        
            except Exception as e:
                error_count += 1
                print(f"第 {index+1} 行資料處理失敗: {e}")
        
        # 處理最後一批資料
        if batch_data:
            try:
                cursor.executemany(insert_sql, batch_data)
                conn.commit()
                success_count += len(batch_data)
                print(f"已匯入最後 {len(batch_data)} 筆，總共 {success_count} 筆")
            except Exception as e:
                print(f"最後批次匯入失敗: {e}")
                conn.rollback()
                error_count += len(batch_data)
        
        # 統計最終結果
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        final_count = cursor.fetchone()[0]
        
        print(f"\n=== 匯入完成 ===")
        print(f"成功匯入: {success_count} 筆")
        print(f"匯入失敗: {error_count} 筆")
        print(f"資料表最終記錄數: {final_count}")
        
        # 顯示一些統計資訊
        if final_count > 0:
            cursor.execute("SELECT MIN(nhi_price), MAX(nhi_price), AVG(nhi_price)::DECIMAL(10,2) FROM nhi_prices")
            min_price, max_price, avg_price = cursor.fetchone()
            print(f"價格範圍: ${min_price} ~ ${max_price} (平均: ${avg_price})")
            
            cursor.execute("SELECT nhi_code, nhi_price FROM nhi_prices ORDER BY nhi_price DESC LIMIT 5")
            print("價格最高的5筆:")
            for row in cursor.fetchall():
                print(f"  {row[0]}: ${row[1]}")
        
        cursor.close()
        conn.close()
        print("資料庫連接已關閉")
        
    except Exception as e:
        print(f"資料庫操作失敗: {e}")
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        sys.exit(1)

if __name__ == "__main__":
    try:
        full_import()
    except Exception as e:
        print(f"執行失敗: {e}")
        sys.exit(1)