#!/usr/bin/env python3
import psycopg2

DATABASE_URL = "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require"

def check_nhi_relationship():
    print("🔍 檢查 nhi_prices 與 products 的關聯狀況...")
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # 1. 檢查 products 表結構
        print("\n📋 檢查 products 表結構...")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'products' 
            AND column_name IN ('unit_price', 'nhi_code', 'nhi_price')
            ORDER BY ordinal_position
        """)
        product_columns = cursor.fetchall()
        print("   products 表相關欄位:")
        for col_name, data_type, nullable, default in product_columns:
            nullable_info = "NULL" if nullable == "YES" else "NOT NULL"
            default_info = f", DEFAULT: {default}" if default else ""
            print(f"     {col_name}: {data_type} {nullable_info}{default_info}")
        
        # 2. 檢查外鍵約束
        print("\n🔗 檢查外鍵約束...")
        cursor.execute("""
            SELECT 
                tc.constraint_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name 
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage ccu 
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.table_name = 'products' 
                AND tc.constraint_type = 'FOREIGN KEY'
        """)
        foreign_keys = cursor.fetchall()
        if foreign_keys:
            for constraint_name, column_name, foreign_table, foreign_column in foreign_keys:
                print(f"     {constraint_name}: {column_name} → {foreign_table}.{foreign_column}")
        else:
            print("     ❌ 沒有找到外鍵約束")
        
        # 3. 檢查數據對應關係
        print("\n📊 檢查數據對應關係...")
        cursor.execute("SELECT COUNT(*) FROM products")
        total_products = cursor.fetchone()[0]
        print(f"   總產品數: {total_products}")
        
        cursor.execute("SELECT COUNT(*) FROM products WHERE nhi_code IS NOT NULL")
        products_with_nhi_code = cursor.fetchone()[0]
        print(f"   有 nhi_code 的產品: {products_with_nhi_code}")
        
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        total_nhi_prices = cursor.fetchone()[0]
        print(f"   健保價格記錄數: {total_nhi_prices}")
        
        # 4. 檢查關聯查詢
        print("\n🔄 測試關聯查詢...")
        cursor.execute("""
            SELECT 
                p.id,
                p.name,
                p.unit_price,
                p.nhi_code,
                np.nhi_price as linked_nhi_price,
                np.selling_price as linked_selling_price
            FROM products p
            LEFT JOIN nhi_prices np ON p.nhi_code = np.nhi_code
            LIMIT 5
        """)
        results = cursor.fetchall()
        
        print("   關聯查詢結果樣本:")
        successful_links = 0
        for product_id, name, unit_price, nhi_code, linked_nhi_price, linked_selling_price in results:
            if linked_nhi_price is not None:
                successful_links += 1
                status = "✅"
            else:
                status = "❌"
            print(f"     {status} {name[:20]}...")
            print(f"         unit_price: {unit_price}")
            print(f"         nhi_code: {nhi_code}")
            print(f"         linked_nhi_price: {linked_nhi_price}")
            print(f"         linked_selling_price: {linked_selling_price}")
        
        print(f"\n   成功關聯: {successful_links}/5 個樣本")
        
        # 5. 檢查價格不一致的情況
        print("\n⚠️  檢查價格不一致情況...")
        cursor.execute("""
            SELECT 
                p.id,
                p.name,
                p.unit_price,
                np.nhi_price,
                np.selling_price
            FROM products p
            JOIN nhi_prices np ON p.nhi_code = np.nhi_code
            WHERE p.unit_price != np.nhi_price OR p.unit_price != np.selling_price
            LIMIT 10
        """)
        inconsistent = cursor.fetchall()
        
        if inconsistent:
            print(f"   找到 {len(inconsistent)} 筆價格不一致的記錄:")
            for product_id, name, unit_price, nhi_price, selling_price in inconsistent:
                print(f"     - {name[:30]}...")
                print(f"       unit_price: {unit_price}")
                print(f"       nhi_price: {nhi_price}")
                print(f"       selling_price: {selling_price}")
        else:
            print("   ✅ 沒有發現價格不一致的情況")
        
        # 6. 模擬價格更新測試
        print("\n🧪 模擬價格更新測試...")
        cursor.execute("SELECT nhi_code, nhi_price FROM nhi_prices LIMIT 1")
        test_record = cursor.fetchone()
        if test_record:
            test_nhi_code, current_price = test_record
            print(f"   測試健保代碼: {test_nhi_code}")
            print(f"   當前健保價格: {current_price}")
            
            # 查看有多少產品會受影響
            cursor.execute("SELECT COUNT(*) FROM products WHERE nhi_code = %s", (test_nhi_code,))
            affected_count = cursor.fetchone()[0]
            print(f"   受影響產品數量: {affected_count}")
            
            if affected_count > 0:
                print("   💡 如果更新這個健保價格，會自動影響所有關聯產品")
            else:
                print("   ⚠️  沒有產品使用這個健保代碼")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查失敗: {e}")
        return False

if __name__ == "__main__":
    success = check_nhi_relationship()
    print(f"\n{'🎊 檢查完成!' if success else '❌ 檢查失敗'}")