#!/bin/bash

BASE_URL="http://localhost:8080"
echo "🧪 測試購物車四捨五入功能"
echo "========================"

# 檢查伺服器是否運行
echo "1. 檢查伺服器狀態..."
curl -s "$BASE_URL/health" > /dev/null
if [ $? -ne 0 ]; then
    echo "❌ 伺服器未運行，請先執行 ./dev.sh 或 cargo run"
    exit 1
fi
echo "✅ 伺服器正在運行"

# 註冊新使用者
echo -e "\n2. 註冊測試使用者..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "cart_test_user",
    "email": "<EMAIL>",
    "password": "test123456",
    "pharmacy_name": "購物車測試藥局",
    "phone": "**********"
  }')

echo "$REGISTER_RESPONSE" | jq .

# 如果註冊失敗（使用者已存在），繼續使用現有使用者
if echo "$REGISTER_RESPONSE" | grep -q "already exists"; then
    echo "ℹ️  使用者已存在，使用現有使用者進行測試"
fi

# 登入取得 token
echo -e "\n3. 使用者登入..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "cart_test_user",
    "password": "test123456"
  }')

echo "$LOGIN_RESPONSE" | jq .

# 提取 token
TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token // empty')

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
    echo "❌ 無法取得認證 token"
    exit 1
fi

echo "✅ 成功取得認證 token"

# 清空購物車
echo -e "\n4. 清空購物車..."
curl -s -X DELETE "$BASE_URL/api/cart/clear" \
  -H "Authorization: Bearer $TOKEN" | jq .

# 添加價格會產生小數的商品到購物車
echo -e "\n5. 測試四捨五入功能..."

# 測試案例1: 普拿疼錠 85.50 * 3 = 256.50 -> 應該四捨五入到 257
echo "案例1: 普拿疼錠 85.50 * 3 = 256.50 (應該四捨五入到 257)"
ADD_RESPONSE1=$(curl -s -X POST "$BASE_URL/api/cart" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "product_id": 1,
    "quantity": 3
  }')

echo "$ADD_RESPONSE1" | jq .

# 取得購物車內容檢查
echo -e "\n6. 檢查購物車內容..."
CART_RESPONSE=$(curl -s -X GET "$BASE_URL/api/cart" \
  -H "Authorization: Bearer $TOKEN")

echo "$CART_RESPONSE" | jq .

# 提取小計金額進行驗證
SUBTOTAL=$(echo "$CART_RESPONSE" | jq -r '.data.cart.items[0].item.subtotal // empty')
TOTAL_AMOUNT=$(echo "$CART_RESPONSE" | jq -r '.data.cart.total_amount // empty')

echo -e "\n7. 驗證結果:"
echo "項目小計: $SUBTOTAL"
echo "總金額: $TOTAL_AMOUNT"

# 檢查是否為整數
if echo "$SUBTOTAL" | grep -q "\."; then
    echo "❌ 小計包含小數點: $SUBTOTAL"
else
    echo "✅ 小計已正確四捨五入到整數: $SUBTOTAL"
fi

if echo "$TOTAL_AMOUNT" | grep -q "\."; then
    echo "❌ 總金額包含小數點: $TOTAL_AMOUNT"
else
    echo "✅ 總金額已正確四捨五入到整數: $TOTAL_AMOUNT"
fi

# 清空購物車以便重複測試
echo -e "\n8. 清空購物車..."
curl -s -X DELETE "$BASE_URL/api/cart/clear" \
  -H "Authorization: Bearer $TOKEN" | jq .

echo -e "\n✅ 購物車四捨五入測試完成！"