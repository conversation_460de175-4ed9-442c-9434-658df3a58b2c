#!/bin/bash

BASE_URL="http://localhost:8080"
echo "🧪 測試藥品採購系統 API"
echo "========================"

# 檢查伺服器是否運行
echo "1. 檢查伺服器狀態..."
curl -s "$BASE_URL/health" > /dev/null
if [ $? -ne 0 ]; then
    echo "❌ 伺服器未運行，請先執行 ./dev.sh 或 cargo run"
    exit 1
fi
echo "✅ 伺服器正在運行"

# 取得 API 資訊
echo -e "\n2. API 資訊:"
curl -s "$BASE_URL/" | jq .

# 註冊新使用者
echo -e "\n3. 註冊新使用者..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "api_test_user",
    "email": "<EMAIL>",
    "password": "test123456",
    "pharmacy_name": "API測試藥局",
    "phone": "**********"
  }')

echo "$REGISTER_RESPONSE" | jq .

# 如果註冊失敗（使用者已存在），繼續使用現有使用者
if echo "$REGISTER_RESPONSE" | grep -q "already exists"; then
    echo "ℹ️  使用者已存在，使用現有使用者進行測試"
fi

# 登入取得 token
echo -e "\n4. 使用者登入..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "api_test_user",
    "password": "test123456"
  }')

echo "$LOGIN_RESPONSE" | jq .

# 提取 token
TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token // empty')

if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
    echo "❌ 無法取得認證 token"
    exit 1
fi

echo "✅ 成功取得認證 token"

# 取得使用者資訊
echo -e "\n5. 取得使用者資訊..."
curl -s -X GET "$BASE_URL/api/auth/me" \
  -H "Authorization: Bearer $TOKEN" | jq .

# 取得產品清單
echo -e "\n6. 取得產品清單..."
curl -s -X GET "$BASE_URL/api/products" \
  -H "Authorization: Bearer $TOKEN" | jq .

# 取得購物車內容
echo -e "\n7. 取得購物車內容..."
curl -s -X GET "$BASE_URL/api/cart" \
  -H "Authorization: Bearer $TOKEN" | jq .

# 取得訂單清單
echo -e "\n8. 取得訂單清單..."
curl -s -X GET "$BASE_URL/api/orders" \
  -H "Authorization: Bearer $TOKEN" | jq .

echo -e "\n✅ API 測試完成！"
echo "💡 你可以使用取得的 token 進行更多 API 測試："
echo "   export TOKEN=\"$TOKEN\""
echo "   curl -H \"Authorization: Bearer \$TOKEN\" $BASE_URL/api/products"