#!/usr/bin/env python3
"""
測試統計功能修復
"""

import requests
import json

BASE_URL = "http://localhost:8080"

def test_stats_fix():
    """測試統計功能修復"""
    print("📊 === 測試統計功能修復 ===")
    print()
    
    # 登錄獲取令牌
    admin_user = {"username": "admin", "password": "admin123"}
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json=admin_user)
    
    if login_response.status_code != 200:
        print("❌ 登錄失敗")
        return False
    
    token = login_response.json().get('data', {}).get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    # 獲取所有訂單
    response = requests.get(f"{BASE_URL}/api/orders/all", headers=headers)
    
    if response.status_code != 200:
        print("❌ 獲取訂單失敗")
        return False
    
    orders = response.json().get('data', {}).get('orders', [])
    
    print(f"總訂單數: {len(orders)}")
    print()
    
    # 統計各狀態的訂單數量
    status_counts = {}
    for order in orders:
        status = order['status']
        status_counts[status] = status_counts.get(status, 0) + 1
    
    print("實際狀態統計:")
    for status, count in status_counts.items():
        print(f"  {status}: {count} 個")
    
    print()
    print("應該顯示的統計（新的三種狀態）:")
    
    # 新的三種狀態統計
    new_stats = {
        'Pending': status_counts.get('Pending', 0),
        'Processing': status_counts.get('Processing', 0), 
        'Shipped': status_counts.get('Shipped', 0)
    }
    
    print(f"  待處理: {new_stats['Pending']} 個")
    print(f"  檢貨中: {new_stats['Processing']} 個")
    print(f"  已出貨: {new_stats['Shipped']} 個")
    
    # 檢查是否還有舊狀態
    old_statuses = ['Confirmed', 'Delivered', 'Cancelled']
    old_count = sum(status_counts.get(status, 0) for status in old_statuses)
    
    if old_count > 0:
        print(f"\n⚠️  發現 {old_count} 個舊狀態的訂單:")
        for status in old_statuses:
            if status in status_counts:
                print(f"    {status}: {status_counts[status]} 個")
        print("建議將這些訂單的狀態更新為新的三種狀態之一")
    else:
        print("\n✅ 沒有發現舊狀態的訂單")
    
    # 測試狀態篩選是否只返回新狀態
    print("\n測試狀態篩選:")
    new_status_filters = ['pending', 'processing', 'shipped']
    
    for status_filter in new_status_filters:
        filter_response = requests.get(f"{BASE_URL}/api/orders/all?status={status_filter}", headers=headers)
        if filter_response.status_code == 200:
            filtered_orders = filter_response.json().get('data', {}).get('orders', [])
            print(f"  {status_filter}: {len(filtered_orders)} 個訂單 ✅")
        else:
            print(f"  {status_filter}: API錯誤 ❌")
    
    print("\n🎉 統計功能測試完成！")
    return True

if __name__ == "__main__":
    test_stats_fix()