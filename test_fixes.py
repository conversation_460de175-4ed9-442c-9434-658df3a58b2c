#!/usr/bin/env python3
"""
測試修復後的功能
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8080"

def test_fixes():
    """測試修復後的功能"""
    print("🔧 === 測試修復後的功能 ===")
    print()
    
    # 登錄獲取令牌
    admin_user = {"username": "admin", "password": "admin123"}
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json=admin_user)
    
    if login_response.status_code != 200:
        print("❌ 登錄失敗")
        return False
    
    token = login_response.json().get('data', {}).get('token')
    headers = {"Authorization": f"Bearer {token}"}
    
    # 測試1: 日期篩選是否正確工作
    print("1. 測試日期篩選功能:")
    
    today = datetime.now()
    yesterday = today - timedelta(days=1)
    
    start_date = yesterday.strftime('%Y-%m-%dT00:00:00Z')
    end_date = today.strftime('%Y-%m-%dT23:59:59Z')
    
    print(f"   日期範圍: {start_date} 到 {end_date}")
    
    # 測試有日期篩選的請求
    date_response = requests.get(
        f"{BASE_URL}/api/orders/all?start_date={start_date}&end_date={end_date}", 
        headers=headers
    )
    
    # 測試無篩選的請求
    all_response = requests.get(f"{BASE_URL}/api/orders/all", headers=headers)
    
    if date_response.status_code == 200 and all_response.status_code == 200:
        date_orders = date_response.json().get('data', {}).get('orders', [])
        all_orders = all_response.json().get('data', {}).get('orders', [])
        
        print(f"   有日期篩選: {len(date_orders)} 個訂單")
        print(f"   無篩選: {len(all_orders)} 個訂單")
        
        if len(date_orders) < len(all_orders):
            print("   ✅ 日期篩選正常工作（篩選後訂單數量減少）")
        elif len(date_orders) == len(all_orders):
            print("   ⚠️  日期篩選可能沒有效果（所有訂單都在範圍內）")
        else:
            print("   ❌ 日期篩選異常")
    else:
        print("   ❌ API請求失敗")
    
    # 測試2: 單個訂單狀態更新
    print("\n2. 測試單個訂單狀態更新:")
    
    if all_response.status_code == 200:
        orders = all_response.json().get('data', {}).get('orders', [])
        if len(orders) > 0:
            test_order = orders[0]
            order_id = test_order['id']
            original_status = test_order['status']
            
            print(f"   測試訂單: {test_order['order_number']}")
            print(f"   原狀態: {original_status}")
            
            # 選擇一個不同的狀態進行測試
            new_status = 'pending' if original_status != 'Pending' else 'processing'
            
            # 更新狀態
            update_response = requests.put(
                f"{BASE_URL}/api/orders/{order_id}/status",
                headers=headers,
                json={"status": new_status}
            )
            
            if update_response.status_code == 200:
                update_data = update_response.json()
                if update_data.get('success'):
                    updated_status = update_data['data']['order']['order']['status']
                    print(f"   新狀態: {updated_status}")
                    print("   ✅ 狀態更新成功")
                    
                    # 驗證狀態是否真的更新了
                    verify_response = requests.get(f"{BASE_URL}/api/orders/all", headers=headers)
                    if verify_response.status_code == 200:
                        verify_orders = verify_response.json().get('data', {}).get('orders', [])
                        verify_order = next((o for o in verify_orders if o['id'] == order_id), None)
                        if verify_order and verify_order['status'].lower() == updated_status.lower():
                            print("   ✅ 狀態更新驗證成功")
                        else:
                            print("   ❌ 狀態更新驗證失敗")
                else:
                    print(f"   ❌ 狀態更新失敗: {update_data.get('error', '未知錯誤')}")
            else:
                print(f"   ❌ 狀態更新請求失敗: {update_response.status_code}")
        else:
            print("   ⚠️  沒有找到測試訂單")
    
    # 測試3: 狀態篩選
    print("\n3. 測試狀態篩選:")
    
    statuses = ['pending', 'processing', 'shipped']
    for status in statuses:
        response = requests.get(f"{BASE_URL}/api/orders/all?status={status}", headers=headers)
        
        if response.status_code == 200:
            orders = response.json().get('data', {}).get('orders', [])
            print(f"   {status.capitalize():12}: {len(orders):2d} 個訂單 ✅")
            
            # 驗證返回的訂單確實是該狀態
            if len(orders) > 0:
                sample_order = orders[0]
                actual_status = sample_order.get('status', '').lower()
                if actual_status == status.lower():
                    print(f"      狀態驗證正確")
                else:
                    print(f"      ❌ 狀態驗證失敗: 期望 {status}, 實際 {actual_status}")
        else:
            print(f"   {status.capitalize():12}: API錯誤 ({response.status_code}) ❌")
    
    print("\n🎉 修復測試完成！")
    return True

if __name__ == "__main__":
    test_fixes()