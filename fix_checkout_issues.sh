#!/bin/bash

echo "🔧 修復結帳問題..."

# 1. 設定環境變數
export JWT_SECRET="my-super-secret-jwt-key-for-testing-2024"
export DATABASE_URL="postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require"

echo "✅ 環境變數已設定"

# 2. 停止現有伺服器
echo "🛑 停止現有伺服器..."
pkill -f pharmacy-system

# 3. 等待伺服器完全停止
sleep 2

# 4. 重新啟動伺服器
echo "🚀 重新啟動伺服器..."
cargo run --bin pharmacy-system &
SERVER_PID=$!

# 5. 等待伺服器啟動
echo "⏳ 等待伺服器啟動..."
sleep 5

# 6. 測試健康檢查
echo "🏥 測試健康檢查..."
curl -s http://localhost:8080/health > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ 伺服器啟動成功"
else
    echo "❌ 伺服器啟動失敗"
    exit 1
fi

# 7. 測試註冊新用戶
echo "👤 測試用戶註冊..."
REGISTER_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "checkouttest",
    "email": "<EMAIL>", 
    "password": "TestPass123!",
    "pharmacy_name": "結帳測試藥局",
    "phone": "**********"
  }')

echo "註冊回應: $REGISTER_RESPONSE"

# 8. 測試登入
echo "🔐 測試用戶登入..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "checkouttest",
    "password": "TestPass123!"
  }')

echo "登入回應: $LOGIN_RESPONSE"

# 9. 提取 token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
    echo "✅ 成功取得 token"
    
    # 10. 測試添加商品到購物車
    echo "🛒 測試添加商品到購物車..."
    CART_RESPONSE=$(curl -s -X POST http://localhost:8080/api/cart \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d '{
        "product_id": 32,
        "quantity": 2
      }')
    
    echo "購物車回應: $CART_RESPONSE"
    
    # 11. 測試結帳
    echo "💳 測試結帳..."
    CHECKOUT_RESPONSE=$(curl -s -X POST http://localhost:8080/api/orders/cart \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d '{
        "notes": "測試訂單"
      }')
    
    echo "結帳回應: $CHECKOUT_RESPONSE"
    
else
    echo "❌ 無法取得 token"
fi

echo "🎉 修復完成！"
echo ""
echo "📋 測試結果摘要："
echo "- 伺服器狀態: ✅ 正常"
echo "- 用戶註冊: $(if echo $REGISTER_RESPONSE | grep -q 'success.*true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
echo "- 用戶登入: $(if echo $LOGIN_RESPONSE | grep -q 'success.*true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
echo "- 購物車操作: $(if echo $CART_RESPONSE | grep -q 'success.*true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
echo "- 結帳功能: $(if echo $CHECKOUT_RESPONSE | grep -q 'success.*true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"

echo ""
echo "🌐 您現在可以訪問 http://localhost:8080 來測試完整的結帳流程" 