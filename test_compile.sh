#!/bin/bash

echo "🔍 檢查 Rust 工具鏈是否可用..."
if command -v cargo &> /dev/null; then
    echo "✅ Cargo 可用"
    
    echo ""
    echo "🔨 運行 cargo check..."
    cargo check 2>&1
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ 編譯檢查通過！"
    else
        echo ""
        echo "❌ 編譯檢查失敗，請檢查上面的錯誤信息"
    fi
else
    echo "❌ Cargo 不可用，請確保已安裝 Rust"
    echo "可以通過以下方式安裝："
    echo "curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
fi