use pharmacy_system::{
    services::{
        file_processing::{FileProcessingService, FileProcessingServiceImpl},
        product::{ProductService, ProductServiceImpl},
    },
    repositories::product::SqliteProductRepository,
    database::Database,
};
use std::sync::Arc;

#[tokio::test]
async fn test_csv_file_import_integration() {
    // 建立測試資料庫 (遷移會自動執行)
    let database = Database::new(":memory:").await.expect("Failed to create test database");

    // 建立服務
    let product_repo = Arc::new(SqliteProductRepository::new(database.pool().clone()));
    let file_service = Arc::new(FileProcessingServiceImpl::new());
    let product_service = ProductServiceImpl::new(product_repo, file_service);

    // 建立測試 CSV 資料
    let csv_content = "nhi_code,name,manufacturer,unit,unit_price,stock_quantity,description\nA001234567,阿司匹林,台灣製藥,盒,50.5,100,止痛藥\nB002345678,維他命C,健康製藥,瓶,25.0,200,維他命補充劑";
    let csv_data = csv_content.as_bytes().to_vec();

    // 執行匯入
    let result = product_service.import_from_csv(csv_data).await;
    
    // 驗證結果
    assert!(result.is_ok());
    let import_result = result.unwrap();
    assert_eq!(import_result.imported_count, 2);
    assert_eq!(import_result.error_count, 0);
    assert_eq!(import_result.total_rows, 2);

    // 驗證產品是否已儲存到資料庫
    let products = product_service.get_products(None, Some(1), Some(10)).await.unwrap();
    assert_eq!(products.len(), 2);
    
    // 驗證第一個產品
    let product1 = products.iter().find(|p| p.nhi_code == "A001234567").unwrap();
    assert_eq!(product1.name, "阿司匹林");
    assert_eq!(product1.manufacturer, "台灣製藥");
    assert_eq!(product1.stock_quantity, 100);
    
    // 驗證第二個產品
    let product2 = products.iter().find(|p| p.nhi_code == "B002345678").unwrap();
    assert_eq!(product2.name, "維他命C");
    assert_eq!(product2.manufacturer, "健康製藥");
    assert_eq!(product2.stock_quantity, 200);
}

#[tokio::test]
async fn test_file_processing_error_handling() {
    let file_service = FileProcessingServiceImpl::new();

    // 測試空檔案
    let empty_data = Vec::new();
    let result = file_service.process_csv_file(empty_data).await;
    assert!(result.is_err());
    assert!(result.unwrap_err().to_string().contains("File is empty"));

    // 測試過大檔案
    let oversized_data = vec![0u8; 11 * 1024 * 1024]; // 11MB
    let result = file_service.process_csv_file(oversized_data).await;
    assert!(result.is_err());
    assert!(result.unwrap_err().to_string().contains("File size exceeds maximum limit"));
}

#[tokio::test]
async fn test_csv_parsing_with_invalid_data() {
    let file_service = FileProcessingServiceImpl::new();

    // 測試包含無效資料的 CSV
    let csv_content = "nhi_code,name,manufacturer,unit,unit_price,stock_quantity,description\nA001234567,阿司匹林,台灣製藥,盒,50.5,100,止痛藥\nINVALID,,,,invalid_price,-10,";
    let csv_data = csv_content.as_bytes().to_vec();

    let result = file_service.parse_csv_data(csv_data).await;
    
    // 應該只解析出一個有效產品，無效的行會被跳過
    assert!(result.is_ok());
    let products = result.unwrap();
    assert_eq!(products.len(), 1);
    assert_eq!(products[0].nhi_code, "A001234567");
}