use pharmacy_system::{
    database::Database,
    repositories::{
        user::{UserRepository, SqliteUserRepository},
        product::{ProductRepository, SqliteProductRepository},
        order::{OrderRepository, SqliteOrderRepository},
        cart::{CartRepository, SqliteCartRepository},
    },
    models::{
        user::{User, CreateUserRequest},
        product::{Product, CreateProductRequest},
        order::{Order, OrderStatus, CreateOrderRequest, OrderItem},
        cart::{CartItem, CreateCartItemRequest},
    },
};
use std::sync::Arc;
use rust_decimal::Decimal;
use chrono::Utc;

/// 建立測試資料庫的輔助函數
async fn create_test_database() -> Database {
    let database = Database::new(":memory:").await.expect("Failed to create test database");
    database
}

// ============================================================================
// 使用者 Repository 整合測試
// ============================================================================

#[tokio::test]
async fn test_user_repository_integration() {
    let database = create_test_database().await;
    let user_repo: Arc<dyn UserRepository> = Arc::new(SqliteUserRepository::new(database.pool().clone()));
    
    // 1. 測試建立使用者
    let create_request = CreateUserRequest {
        username: "testuser".to_string(),
        email: "<EMAIL>".to_string(),
        password_hash: "hashed_password".to_string(),
        pharmacy_name: "測試藥局".to_string(),
        phone: Some("0912345678".to_string()),
        line_user_id: None,
        notification_email: true,
        notification_line: false,
    };
    
    let created_user = user_repo.create(create_request).await.expect("Failed to create user");
    assert_eq!(created_user.username, "testuser");
    assert_eq!(created_user.email, "<EMAIL>");
    assert_eq!(created_user.pharmacy_name, "測試藥局");
    assert!(created_user.id > 0);
    
    let user_id = created_user.id;
    
    // 2. 測試根據 ID 查詢使用者
    let found_user = user_repo.find_by_id(user_id).await.expect("Failed to find user by id");
    assert!(found_user.is_some());
    let found_user = found_user.unwrap();
    assert_eq!(found_user.username, "testuser");
    assert_eq!(found_user.email, "<EMAIL>");
    
    // 3. 測試根據使用者名稱查詢
    let found_by_username = user_repo.find_by_username("testuser").await.expect("Failed to find user by username");
    assert!(found_by_username.is_some());
    let found_by_username = found_by_username.unwrap();
    assert_eq!(found_by_username.id, user_id);
    
    // 4. 測試根據 email 查詢
    let found_by_email = user_repo.find_by_email("<EMAIL>").await.expect("Failed to find user by email");
    assert!(found_by_email.is_some());
    let found_by_email = found_by_email.unwrap();
    assert_eq!(found_by_email.id, user_id);
    
    // 5. 測試更新使用者
    let mut updated_user = found_user.clone();
    updated_user.pharmacy_name = "更新後的藥局".to_string();
    updated_user.phone = Some("0987654321".to_string());
    updated_user.notification_line = true;
    
    let update_result = user_repo.update(updated_user).await.expect("Failed to update user");
    assert_eq!(update_result.pharmacy_name, "更新後的藥局");
    assert_eq!(update_result.phone, Some("0987654321".to_string()));
    assert_eq!(update_result.notification_line, true);
    
    // 6. 測試更新密碼
    let password_updated = user_repo.update_password(user_id, "new_hashed_password").await.expect("Failed to update password");
    assert!(password_updated);
    
    // 7. 測試刪除使用者
    let deleted = user_repo.delete(user_id).await.expect("Failed to delete user");
    assert!(deleted);
    
    // 8. 驗證使用者已被刪除
    let deleted_user = user_repo.find_by_id(user_id).await.expect("Failed to check deleted user");
    assert!(deleted_user.is_none());
}

// ============================================================================
// 產品 Repository 整合測試
// ============================================================================

#[tokio::test]
async fn test_product_repository_integration() {
    let database = create_test_database().await;
    let product_repo: Arc<dyn ProductRepository> = Arc::new(SqliteProductRepository::new(database.pool().clone()));
    
    // 1. 測試建立產品
    let create_request = CreateProductRequest {
        nhi_code: "A001234567".to_string(),
        name: "阿司匹林 100mg".to_string(),
        manufacturer: "台灣製藥".to_string(),
        unit: "盒".to_string(),
        unit_price: Decimal::new(5050, 2), // 50.50
        stock_quantity: 100,
        description: Some("止痛藥".to_string()),
        is_active: true,
    };
    
    let created_product = product_repo.create(create_request).await.expect("Failed to create product");
    assert_eq!(created_product.nhi_code, "A001234567");
    assert_eq!(created_product.name, "阿司匹林 100mg");
    assert_eq!(created_product.stock_quantity, 100);
    assert!(created_product.id > 0);
    
    let product_id = created_product.id;
    
    // 2. 測試根據 ID 查詢產品
    let found_product = product_repo.find_by_id(product_id).await.expect("Failed to find product by id");
    assert!(found_product.is_some());
    let found_product = found_product.unwrap();
    assert_eq!(found_product.nhi_code, "A001234567");
    
    // 3. 測試根據健保代碼查詢
    let found_by_nhi = product_repo.find_by_nhi_code("A001234567").await.expect("Failed to find product by NHI code");
    assert!(found_by_nhi.is_some());
    let found_by_nhi = found_by_nhi.unwrap();
    assert_eq!(found_by_nhi.id, product_id);
    
    // 4. 測試查詢所有產品
    let all_products = product_repo.find_all(None, Some(1), Some(10)).await.expect("Failed to find all products");
    assert!(all_products.len() > 0);
    assert!(all_products.iter().any(|p| p.id == product_id));
    
    // 5. 測試搜尋產品
    let search_results = product_repo.search("阿司匹林", None, Some(1), Some(10)).await.expect("Failed to search products");
    assert!(search_results.len() > 0);
    assert!(search_results.iter().any(|p| p.id == product_id));
    
    // 6. 測試根據製造商篩選
    let manufacturer_results = product_repo.find_by_manufacturer("台灣製藥", None, Some(1), Some(10)).await.expect("Failed to find by manufacturer");
    assert!(manufacturer_results.len() > 0);
    assert!(manufacturer_results.iter().any(|p| p.id == product_id));
    
    // 7. 測試更新庫存
    let updated_stock = product_repo.update_stock(product_id, 150).await.expect("Failed to update stock");
    assert!(updated_stock);
    
    let updated_product = product_repo.find_by_id(product_id).await.expect("Failed to find updated product").unwrap();
    assert_eq!(updated_product.stock_quantity, 150);
    
    // 8. 測試查詢低庫存產品
    // 先將庫存設為很低的值
    product_repo.update_stock(product_id, 5).await.expect("Failed to set low stock");
    let low_stock_products = product_repo.find_low_stock(10).await.expect("Failed to find low stock products");
    assert!(low_stock_products.iter().any(|p| p.id == product_id));
    
    // 9. 測試更新產品
    let mut updated_product = found_product.clone();
    updated_product.name = "更新後的阿司匹林".to_string();
    updated_product.unit_price = Decimal::new(6000, 2); // 60.00
    updated_product.is_active = false;
    
    let update_result = product_repo.update(updated_product).await.expect("Failed to update product");
    assert_eq!(update_result.name, "更新後的阿司匹林");
    assert_eq!(update_result.unit_price, Decimal::new(6000, 2));
    assert_eq!(update_result.is_active, false);
    
    // 10. 測試刪除產品
    let deleted = product_repo.delete(product_id).await.expect("Failed to delete product");
    assert!(deleted);
    
    // 11. 驗證產品已被刪除
    let deleted_product = product_repo.find_by_id(product_id).await.expect("Failed to check deleted product");
    assert!(deleted_product.is_none());
}

// ============================================================================
// 購物車 Repository 整合測試
// ============================================================================

#[tokio::test]
async fn test_cart_repository_integration() {
    let database = create_test_database().await;
    let cart_repo: Arc<dyn CartRepository> = Arc::new(SqliteCartRepository::new(database.pool().clone()));
    let user_repo: Arc<dyn UserRepository> = Arc::new(SqliteUserRepository::new(database.pool().clone()));
    let product_repo: Arc<dyn ProductRepository> = Arc::new(SqliteProductRepository::new(database.pool().clone()));
    
    // 建立測試使用者
    let user_request = CreateUserRequest {
        username: "cartuser".to_string(),
        email: "<EMAIL>".to_string(),
        password_hash: "hashed_password".to_string(),
        pharmacy_name: "購物車測試藥局".to_string(),
        phone: None,
        line_user_id: None,
        notification_email: true,
        notification_line: false,
    };
    let user = user_repo.create(user_request).await.expect("Failed to create test user");
    
    // 建立測試產品
    let product_request = CreateProductRequest {
        nhi_code: "CART001".to_string(),
        name: "購物車測試產品".to_string(),
        manufacturer: "測試製藥".to_string(),
        unit: "盒".to_string(),
        unit_price: Decimal::new(10000, 2), // 100.00
        stock_quantity: 50,
        description: Some("測試用產品".to_string()),
        is_active: true,
    };
    let product = product_repo.create(product_request).await.expect("Failed to create test product");
    
    // 1. 測試添加商品到購物車
    let cart_item_request = CreateCartItemRequest {
        user_id: user.id,
        product_id: product.id,
        quantity: 3,
    };
    
    let cart_item = cart_repo.add_item(cart_item_request).await.expect("Failed to add item to cart");
    assert_eq!(cart_item.user_id, user.id);
    assert_eq!(cart_item.product_id, product.id);
    assert_eq!(cart_item.quantity, 3);
    assert!(cart_item.id > 0);
    
    // 2. 測試查詢使用者購物車
    let cart_items = cart_repo.get_user_cart(user.id).await.expect("Failed to get user cart");
    assert_eq!(cart_items.len(), 1);
    assert_eq!(cart_items[0].id, cart_item.id);
    
    // 3. 測試更新購物車項目數量
    let updated_item = cart_repo.update_item_quantity(cart_item.id, user.id, 5).await.expect("Failed to update item quantity");
    assert!(updated_item.is_some());
    let updated_item = updated_item.unwrap();
    assert_eq!(updated_item.quantity, 5);
    
    // 4. 測試查詢購物車項目總數
    let total_items = cart_repo.get_cart_item_count(user.id).await.expect("Failed to get cart item count");
    assert_eq!(total_items, 5); // 數量為 5
    
    // 5. 測試查詢購物車總金額
    let total_amount = cart_repo.get_cart_total(user.id).await.expect("Failed to get cart total");
    assert_eq!(total_amount, Decimal::new(50000, 2)); // 5 * 100.00 = 500.00
    
    // 6. 測試檢查購物車項目是否存在
    let item_exists = cart_repo.item_exists(user.id, product.id).await.expect("Failed to check if item exists");
    assert!(item_exists);
    
    // 7. 測試移除購物車項目
    let removed = cart_repo.remove_item(cart_item.id, user.id).await.expect("Failed to remove cart item");
    assert!(removed);
    
    // 8. 驗證項目已被移除
    let empty_cart = cart_repo.get_user_cart(user.id).await.expect("Failed to get empty cart");
    assert_eq!(empty_cart.len(), 0);
    
    // 9. 測試清空購物車（先添加一些項目）
    let new_item_request = CreateCartItemRequest {
        user_id: user.id,
        product_id: product.id,
        quantity: 2,
    };
    cart_repo.add_item(new_item_request).await.expect("Failed to add new item");
    
    let cleared = cart_repo.clear_cart(user.id).await.expect("Failed to clear cart");
    assert!(cleared);
    
    let cleared_cart = cart_repo.get_user_cart(user.id).await.expect("Failed to get cleared cart");
    assert_eq!(cleared_cart.len(), 0);
}

// ============================================================================
// 訂單 Repository 整合測試
// ============================================================================

#[tokio::test]
async fn test_order_repository_integration() {
    let database = create_test_database().await;
    let order_repo: Arc<dyn OrderRepository> = Arc::new(SqliteOrderRepository::new(database.pool().clone()));
    let user_repo: Arc<dyn UserRepository> = Arc::new(SqliteUserRepository::new(database.pool().clone()));
    let product_repo: Arc<dyn ProductRepository> = Arc::new(SqliteProductRepository::new(database.pool().clone()));
    
    // 建立測試使用者
    let user_request = CreateUserRequest {
        username: "orderuser".to_string(),
        email: "<EMAIL>".to_string(),
        password_hash: "hashed_password".to_string(),
        pharmacy_name: "訂單測試藥局".to_string(),
        phone: None,
        line_user_id: None,
        notification_email: true,
        notification_line: false,
    };
    let user = user_repo.create(user_request).await.expect("Failed to create test user");
    
    // 建立測試產品
    let product_request = CreateProductRequest {
        nhi_code: "ORDER001".to_string(),
        name: "訂單測試產品".to_string(),
        manufacturer: "測試製藥".to_string(),
        unit: "盒".to_string(),
        unit_price: Decimal::new(15000, 2), // 150.00
        stock_quantity: 100,
        description: Some("測試用產品".to_string()),
        is_active: true,
    };
    let product = product_repo.create(product_request).await.expect("Failed to create test product");
    
    // 1. 測試建立訂單
    let order_items = vec![
        OrderItem {
            id: 0, // 新建項目，ID 為 0
            order_id: 0, // 將由資料庫設定
            product_id: product.id,
            quantity: 2,
            unit_price: product.unit_price,
            subtotal: product.unit_price * Decimal::new(2, 0),
        }
    ];
    
    let create_order_request = CreateOrderRequest {
        user_id: user.id,
        items: order_items,
        notes: Some("測試訂單".to_string()),
    };
    
    let created_order = order_repo.create(create_order_request).await.expect("Failed to create order");
    assert_eq!(created_order.user_id, user.id);
    assert_eq!(created_order.status, OrderStatus::Pending);
    assert_eq!(created_order.total_amount, Decimal::new(30000, 2)); // 2 * 150.00 = 300.00
    assert!(!created_order.order_number.is_empty());
    assert!(created_order.id > 0);
    
    let order_id = created_order.id;
    
    // 2. 測試根據 ID 查詢訂單
    let found_order = order_repo.find_by_id(order_id).await.expect("Failed to find order by id");
    assert!(found_order.is_some());
    let found_order = found_order.unwrap();
    assert_eq!(found_order.id, order_id);
    assert_eq!(found_order.user_id, user.id);
    
    // 3. 測試根據訂單編號查詢
    let found_by_number = order_repo.find_by_order_number(&created_order.order_number).await.expect("Failed to find order by number");
    assert!(found_by_number.is_some());
    let found_by_number = found_by_number.unwrap();
    assert_eq!(found_by_number.id, order_id);
    
    // 4. 測試查詢使用者訂單
    let user_orders = order_repo.find_by_user_id(user.id, None, Some(1), Some(10)).await.expect("Failed to find user orders");
    assert!(user_orders.len() > 0);
    assert!(user_orders.iter().any(|o| o.id == order_id));
    
    // 5. 測試根據狀態查詢訂單
    let pending_orders = order_repo.find_by_status(OrderStatus::Pending, None, Some(1), Some(10)).await.expect("Failed to find pending orders");
    assert!(pending_orders.iter().any(|o| o.id == order_id));
    
    // 6. 測試更新訂單狀態
    let status_updated = order_repo.update_status(order_id, OrderStatus::Confirmed).await.expect("Failed to update order status");
    assert!(status_updated);
    
    let updated_order = order_repo.find_by_id(order_id).await.expect("Failed to find updated order").unwrap();
    assert_eq!(updated_order.status, OrderStatus::Confirmed);
    
    // 7. 測試查詢訂單項目
    let order_items = order_repo.get_order_items(order_id).await.expect("Failed to get order items");
    assert_eq!(order_items.len(), 1);
    assert_eq!(order_items[0].product_id, product.id);
    assert_eq!(order_items[0].quantity, 2);
    
    // 8. 測試統計使用者訂單數量
    let order_count = order_repo.count_user_orders(user.id, None).await.expect("Failed to count user orders");
    assert!(order_count > 0);
    
    // 9. 測試根據日期範圍查詢訂單
    let start_date = Utc::now() - chrono::Duration::hours(1);
    let end_date = Utc::now() + chrono::Duration::hours(1);
    
    let date_range_orders = order_repo.find_by_date_range(start_date, end_date, None, Some(1), Some(10)).await.expect("Failed to find orders by date range");
    assert!(date_range_orders.iter().any(|o| o.id == order_id));
    
    // 10. 測試刪除訂單
    let deleted = order_repo.delete(order_id).await.expect("Failed to delete order");
    assert!(deleted);
    
    // 11. 驗證訂單已被刪除
    let deleted_order = order_repo.find_by_id(order_id).await.expect("Failed to check deleted order");
    assert!(deleted_order.is_none());
}

// ============================================================================
// 資料庫交易測試
// ============================================================================

#[tokio::test]
async fn test_database_transaction_integration() {
    let database = create_test_database().await;
    let user_repo: Arc<dyn UserRepository> = Arc::new(SqliteUserRepository::new(database.pool().clone()));
    let product_repo: Arc<dyn ProductRepository> = Arc::new(SqliteProductRepository::new(database.pool().clone()));
    
    // 測試交易回滾
    let mut tx = database.pool().begin().await.expect("Failed to begin transaction");
    
    // 在交易中建立使用者
    let user_request = CreateUserRequest {
        username: "txuser".to_string(),
        email: "<EMAIL>".to_string(),
        password_hash: "hashed_password".to_string(),
        pharmacy_name: "交易測試藥局".to_string(),
        phone: None,
        line_user_id: None,
        notification_email: true,
        notification_line: false,
    };
    
    // 直接在交易中執行 SQL（模擬交易操作）
    let result = sqlx::query!(
        "INSERT INTO users (username, email, password_hash, pharmacy_name, notification_email, notification_line) VALUES (?, ?, ?, ?, ?, ?)",
        user_request.username,
        user_request.email,
        user_request.password_hash,
        user_request.pharmacy_name,
        user_request.notification_email,
        user_request.notification_line
    ).execute(&mut *tx).await;
    
    assert!(result.is_ok());
    
    // 回滾交易
    tx.rollback().await.expect("Failed to rollback transaction");
    
    // 驗證使用者未被建立
    let found_user = user_repo.find_by_username("txuser").await.expect("Failed to check rollback");
    assert!(found_user.is_none());
    
    // 測試交易提交
    let mut tx = database.pool().begin().await.expect("Failed to begin transaction");
    
    let result = sqlx::query!(
        "INSERT INTO users (username, email, password_hash, pharmacy_name, notification_email, notification_line) VALUES (?, ?, ?, ?, ?, ?)",
        user_request.username,
        user_request.email,
        user_request.password_hash,
        user_request.pharmacy_name,
        user_request.notification_email,
        user_request.notification_line
    ).execute(&mut *tx).await;
    
    assert!(result.is_ok());
    
    // 提交交易
    tx.commit().await.expect("Failed to commit transaction");
    
    // 驗證使用者已被建立
    let found_user = user_repo.find_by_username("txuser").await.expect("Failed to check commit");
    assert!(found_user.is_some());
}

// ============================================================================
// 資料庫連線池測試
// ============================================================================

#[tokio::test]
async fn test_database_connection_pool() {
    let database = create_test_database().await;
    
    // 測試並發查詢
    let mut handles = vec![];
    
    for i in 0..10 {
        let pool = database.pool().clone();
        let handle = tokio::spawn(async move {
            let result = sqlx::query!("SELECT ? as test_value", i)
                .fetch_one(&*pool)
                .await;
            
            assert!(result.is_ok());
            let row = result.unwrap();
            assert_eq!(row.test_value, Some(i));
        });
        
        handles.push(handle);
    }
    
    // 等待所有查詢完成
    for handle in handles {
        handle.await.expect("Task failed");
    }
}