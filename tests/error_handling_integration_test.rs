use pharmacy_system::{
    error::{AppError, AppResult, helpers},
    logging::{LoggingConfig, init_logging},
};

#[tokio::test]
async fn test_error_handling_integration() {
    // 初始化日誌系統
    let logging_config = LoggingConfig {
        json_format: false,
        ..Default::default()
    };
    init_logging(logging_config).expect("Failed to initialize logging");

    // 測試各種錯誤類型
    let validation_error = helpers::validation_error("測試驗證錯誤");
    assert!(matches!(validation_error, AppError::Validation(_)));
    assert_eq!(validation_error.error_code(), "VALIDATION_ERROR");
    assert_eq!(validation_error.user_message(), "測試驗證錯誤");

    let not_found_error = helpers::not_found_error("使用者");
    assert!(matches!(not_found_error, AppError::NotFound(_)));
    assert_eq!(not_found_error.error_code(), "NOT_FOUND");
    assert_eq!(not_found_error.user_message(), "使用者 未找到");

    let auth_error = helpers::auth_error("認證失敗");
    assert!(matches!(auth_error, AppError::Authentication(_)));
    assert_eq!(auth_error.error_code(), "AUTHENTICATION_ERROR");
    assert_eq!(auth_error.user_message(), "認證失敗，請重新登入");

    // 測試錯誤嚴重程度
    use pharmacy_system::error::ErrorSeverity;
    assert!(matches!(validation_error.severity(), ErrorSeverity::Low));
    assert!(matches!(auth_error.severity(), ErrorSeverity::Medium));

    // 測試是否應該記錄詳細錯誤
    assert!(!validation_error.should_log_details());
    assert!(!auth_error.should_log_details());

    let internal_error = helpers::internal_error("系統內部錯誤");
    assert!(internal_error.should_log_details());
}

#[tokio::test]
async fn test_structured_logging() {
    use pharmacy_system::logging::structured_logging;
    use std::time::Instant;

    // 初始化日誌系統
    let logging_config = LoggingConfig {
        json_format: false,
        ..Default::default()
    };
    init_logging(logging_config).expect("Failed to initialize logging");

    // 測試結構化日誌記錄
    let start_time = Instant::now();
    
    // 模擬一些操作
    tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
    
    let duration = start_time.elapsed();

    // 記錄 API 請求
    structured_logging::log_api_request(
        "GET",
        "/api/products",
        Some(1),
        "test-request-id",
        Some(duration.as_millis() as u64),
        Some(200),
    );

    // 記錄資料庫操作
    structured_logging::log_database_operation(
        "SELECT",
        "products",
        duration.as_millis() as u64,
        Some(10),
        Some("test-request-id"),
    );

    // 記錄檔案處理
    structured_logging::log_file_processing(
        "excel_import",
        "test.xlsx",
        Some(1024),
        duration.as_millis() as u64,
        true,
        Some(100),
        Some(0),
        Some("test-request-id"),
    );

    // 記錄通知發送
    structured_logging::log_notification_sent(
        "email",
        "<EMAIL>",
        true,
        duration.as_millis() as u64,
        None,
        Some("test-request-id"),
    );

    // 記錄備份操作
    structured_logging::log_backup_operation(
        "create_backup",
        "database",
        Some(2048),
        duration.as_millis() as u64,
        true,
        Some("gs://bucket/backup.db"),
        None,
    );

    // 記錄健康檢查
    structured_logging::log_health_check(
        "database",
        "healthy",
        duration.as_millis() as u64,
        Some(serde_json::json!({
            "connection_count": 5,
            "active_queries": 2
        })),
    );
}

#[tokio::test]
async fn test_performance_monitoring() {
    use pharmacy_system::logging::performance_monitoring::{PerformanceMonitor, PerformanceTimer};
    use std::time::Duration;

    // 建立效能監控器
    let monitor = PerformanceMonitor::new(100);

    // 測試效能計時器
    let mut timer = PerformanceTimer::new("test_operation");
    timer.add_data("test_key", "test_value");
    
    // 模擬一些工作
    tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
    
    timer.finish(&monitor, true).await;

    // 檢查平均回應時間
    let avg_time = monitor.get_average_response_time("test_operation").await;
    assert!(avg_time.is_some());
    assert!(avg_time.unwrap() >= Duration::from_millis(10));

    // 檢查成功率
    let success_rate = monitor.get_success_rate("test_operation").await;
    assert!(success_rate.is_some());
    assert_eq!(success_rate.unwrap(), 1.0);

    // 測試清除舊指標
    monitor.clear_old_metrics(chrono::Duration::seconds(0)).await;
    
    let avg_time_after_clear = monitor.get_average_response_time("test_operation").await;
    assert!(avg_time_after_clear.is_none());
}

#[test]
fn test_gcp_logging_integration() {
    use pharmacy_system::logging::gcp_logging::{GcpSeverity, create_gcp_log_entry};
    use serde_json::json;

    // 測試 GCP 日誌條目建立
    let entry = create_gcp_log_entry(
        GcpSeverity::Info,
        "測試訊息",
        Some(json!({"service": "pharmacy-system", "version": "1.0.0"})),
        Some(json!({"file": "test.rs", "line": 123})),
        Some(json!({"method": "GET", "url": "/api/test", "status": 200})),
        Some(json!({"id": "op-123", "producer": "test"})),
    );

    assert_eq!(entry["severity"], "INFO");
    assert_eq!(entry["message"], "測試訊息");
    assert_eq!(entry["labels"]["service"], "pharmacy-system");
    assert_eq!(entry["sourceLocation"]["file"], "test.rs");
    assert_eq!(entry["httpRequest"]["method"], "GET");
    assert_eq!(entry["operation"]["id"], "op-123");
    assert!(entry["timestamp"].is_string());
}