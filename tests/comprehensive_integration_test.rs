use axum_test::TestServer;
use pharmacy_system::api::auth::AppState;
use pharmacy_system::api::create_api_router;
use pharmacy_system::config::Config;
use pharmacy_system::database::Database;
use pharmacy_system::repositories::{
    order::{OrderRepository, SqliteOrderRepository},
    cart::{CartRepository, SqliteCartRepository},
    product::{ProductRepository, SqliteProductRepository},
    user::{UserRepository, SqliteUserRepository},
};
use pharmacy_system::services::{
    order::{OrderService, OrderServiceImpl},
    product::{ProductService, ProductServiceImpl},
    file_processing::{FileProcessingService, FileProcessingServiceImpl},
    auth::{AuthService, AuthServiceImpl},
    cart::{CartService, CartServiceImpl},
    backup::{BackupService, BackupServiceImpl},
    notification::{NotificationService, NotificationServiceImpl},
};
use serde_json::json;
use std::sync::Arc;
use axum::http::{HeaderName, HeaderValue};
use tempfile::NamedTempFile;
use std::io::Write;

/// 建立測試伺服器的輔助函數
async fn create_test_server() -> TestServer {
    // 載入測試配置
    dotenvy::dotenv().ok();
    
    let config = Config::from_env().expect("Failed to load config");
    let database = Database::new(":memory:").await.expect("Failed to connect to database");
    
    let db_pool = database.pool().clone();

    // 建立 repositories
    let _user_repository: Arc<dyn UserRepository> = Arc::new(SqliteUserRepository::new(db_pool.clone()));
    let order_repository: Arc<dyn OrderRepository> = Arc::new(SqliteOrderRepository::new(db_pool.clone()));
    let cart_repository: Arc<dyn CartRepository> = Arc::new(SqliteCartRepository::new(db_pool.clone()));
    let product_repository: Arc<dyn ProductRepository> = Arc::new(SqliteProductRepository::new(db_pool.clone()));

    // 建立 services
    let file_processing_service: Arc<dyn FileProcessingService> = Arc::new(FileProcessingServiceImpl::new());
    let auth_service: Arc<dyn AuthService> = Arc::new(AuthServiceImpl::new(
        db_pool.clone(),
        config.clone(),
    ));
    let product_service: Arc<dyn ProductService> = Arc::new(ProductServiceImpl::new(
        product_repository.clone(),
        file_processing_service,
    ));
    let cart_service: Arc<dyn CartService> = Arc::new(CartServiceImpl::new(
        cart_repository.clone(),
        product_service.clone(),
    ));
    let order_service: Arc<dyn OrderService> = Arc::new(OrderServiceImpl::new(
        order_repository.clone(),
        cart_repository.clone(),
        product_service.clone(),
    ));
    let backup_service: Arc<dyn BackupService> = Arc::new(BackupServiceImpl::new(
        db_pool.clone(),
        config.gcp.storage_bucket.clone(),
        config.gcp.credentials_path.clone(),
    ).await.expect("Failed to create backup service"));
    let notification_service: Arc<dyn NotificationService> = Arc::new(NotificationServiceImpl::new(
        config.clone(),
    ));

    // 建立應用狀態
    let app_state = AppState {
        db: db_pool,
        config: config.clone(),
        auth_service,
        product_service,
        cart_service,
        order_service,
        backup_service,
        notification_service,
    };
    
    // 建立測試應用程式
    let app = create_api_router(app_state);
    
    TestServer::new(app).unwrap()
}

/// 建立認證標頭的輔助函數
fn create_auth_header(token: &str) -> (HeaderName, HeaderValue) {
    (
        HeaderName::from_static("authorization"),
        HeaderValue::from_str(&format!("Bearer {}", token)).unwrap()
    )
}

/// 註冊並登入測試使用者，返回 JWT token
async fn setup_test_user(server: &TestServer) -> String {
    // 註冊測試使用者
    let user_data = json!({
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "password123",
        "pharmacy_name": "測試藥局",
        "phone": "0912345678"
    });
    
    let _register_response = server
        .post("/api/auth/register")
        .json(&user_data)
        .await;
    
    // 登入取得 token
    let login_data = json!({
        "username": "testuser",
        "password": "password123"
    });
    
    let login_response = server
        .post("/api/auth/login")
        .json(&login_data)
        .await;
    
    let login_body: serde_json::Value = login_response.json();
    login_body["data"]["token"].as_str().unwrap().to_string()
}

/// 建立測試產品資料的輔助函數
async fn setup_test_products(_server: &TestServer, _token: &str) {
    // 這個函數暫時跳過實際的產品建立，因為需要實際的 multipart 實作
    // 在實際測試中，我們會直接在資料庫中插入測試資料
}

// ============================================================================
// 認證 API 整合測試
// ============================================================================

#[tokio::test]
async fn test_auth_flow_integration() {
    let server = create_test_server().await;
    
    // 1. 測試註冊
    let user_data = json!({
        "username": "integrationuser",
        "email": "<EMAIL>",
        "password": "password123",
        "pharmacy_name": "整合測試藥局",
        "phone": "0987654321"
    });
    
    let register_response = server
        .post("/api/auth/register")
        .json(&user_data)
        .await;
    
    assert_eq!(register_response.status_code(), 200);
    let register_body: serde_json::Value = register_response.json();
    assert_eq!(register_body["success"], true);
    assert!(register_body["data"]["id"].is_number());
    
    // 2. 測試登入
    let login_data = json!({
        "username": "integrationuser",
        "password": "password123"
    });
    
    let login_response = server
        .post("/api/auth/login")
        .json(&login_data)
        .await;
    
    assert_eq!(login_response.status_code(), 200);
    let login_body: serde_json::Value = login_response.json();
    assert_eq!(login_body["success"], true);
    assert!(login_body["data"]["token"].is_string());
    assert!(login_body["data"]["refresh_token"].is_string());
    
    let token = login_body["data"]["token"].as_str().unwrap();
    let refresh_token = login_body["data"]["refresh_token"].as_str().unwrap();
    
    // 3. 測試取得當前使用者資訊
    let (header_name, header_value) = create_auth_header(token);
    let me_response = server
        .get("/api/auth/me")
        .add_header(header_name.clone(), header_value.clone())
        .await;
    
    assert_eq!(me_response.status_code(), 200);
    let me_body: serde_json::Value = me_response.json();
    assert_eq!(me_body["success"], true);
    assert_eq!(me_body["data"]["username"], "integrationuser");
    
    // 4. 測試取得使用者個人資料
    let profile_response = server
        .get("/api/auth/profile")
        .add_header(header_name.clone(), header_value.clone())
        .await;
    
    assert_eq!(profile_response.status_code(), 200);
    let profile_body: serde_json::Value = profile_response.json();
    assert_eq!(profile_body["success"], true);
    assert_eq!(profile_body["data"]["pharmacy_name"], "整合測試藥局");
    
    // 5. 測試更新個人資料
    let update_data = json!({
        "pharmacy_name": "更新後的藥局名稱",
        "phone": "0911111111",
        "notification_email": true,
        "notification_line": false
    });
    
    let update_response = server
        .post("/api/auth/profile")
        .add_header(header_name.clone(), header_value.clone())
        .json(&update_data)
        .await;
    
    assert_eq!(update_response.status_code(), 200);
    let update_body: serde_json::Value = update_response.json();
    assert_eq!(update_body["success"], true);
    assert_eq!(update_body["data"]["pharmacy_name"], "更新後的藥局名稱");
    
    // 6. 測試變更密碼
    let change_password_data = json!({
        "old_password": "password123",
        "new_password": "newpassword456"
    });
    
    let change_password_response = server
        .post("/api/auth/change-password")
        .add_header(header_name.clone(), header_value.clone())
        .json(&change_password_data)
        .await;
    
    assert_eq!(change_password_response.status_code(), 200);
    
    // 7. 測試 token 刷新
    let refresh_data = json!({
        "refresh_token": refresh_token
    });
    
    let refresh_response = server
        .post("/api/auth/refresh")
        .json(&refresh_data)
        .await;
    
    assert_eq!(refresh_response.status_code(), 200);
    let refresh_body: serde_json::Value = refresh_response.json();
    assert_eq!(refresh_body["success"], true);
    assert!(refresh_body["data"]["token"].is_string());
    
    // 8. 測試登出
    let logout_response = server
        .post("/api/auth/logout")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(logout_response.status_code(), 200);
}

// ============================================================================
// 產品 API 整合測試
// ============================================================================

#[tokio::test]
async fn test_product_management_integration() {
    let server = create_test_server().await;
    let token = setup_test_user(&server).await;
    setup_test_products(&server, &token).await;
    
    let (header_name, header_value) = create_auth_header(&token);
    
    // 1. 測試產品清單查詢
    let products_response = server
        .get("/api/products")
        .add_header(header_name.clone(), header_value.clone())
        .await;
    
    assert_eq!(products_response.status_code(), 200);
    let products_body: serde_json::Value = products_response.json();
    assert!(products_body["products"].is_array());
    assert!(products_body["products"].as_array().unwrap().len() > 0);
    
    // 2. 測試產品搜尋
    let search_response = server
        .get("/api/products")
        .add_header(header_name.clone(), header_value.clone())
        .add_query_param("search", "阿司匹林")
        .await;
    
    assert_eq!(search_response.status_code(), 200);
    let search_body: serde_json::Value = search_response.json();
    assert!(search_body["products"].is_array());
    
    // 3. 測試製造商篩選
    let manufacturer_response = server
        .get("/api/products")
        .add_header(header_name.clone(), header_value.clone())
        .add_query_param("manufacturer", "台灣製藥")
        .await;
    
    assert_eq!(manufacturer_response.status_code(), 200);
    
    // 4. 測試分頁
    let pagination_response = server
        .get("/api/products")
        .add_header(header_name.clone(), header_value.clone())
        .add_query_param("page", "1")
        .add_query_param("limit", "2")
        .await;
    
    assert_eq!(pagination_response.status_code(), 200);
    let pagination_body: serde_json::Value = pagination_response.json();
    assert_eq!(pagination_body["page"], 1);
    assert_eq!(pagination_body["limit"], 2);
    
    // 5. 測試根據 ID 查詢產品
    let product_response = server
        .get("/api/products/1")
        .add_header(header_name.clone(), header_value.clone())
        .await;
    
    if product_response.status_code() == 200 {
        let product_body: serde_json::Value = product_response.json();
        assert!(product_body["product"].is_object());
    }
    
    // 6. 測試根據健保代碼查詢產品
    let nhi_response = server
        .get("/api/products/nhi/A001234567")
        .add_header(header_name.clone(), header_value.clone())
        .await;
    
    if nhi_response.status_code() == 200 {
        let nhi_body: serde_json::Value = nhi_response.json();
        assert!(nhi_body["product"].is_object());
        assert_eq!(nhi_body["product"]["nhi_code"], "A001234567");
    }
    
    // 7. 測試低庫存產品查詢
    let low_stock_response = server
        .get("/api/products/low-stock")
        .add_header(header_name.clone(), header_value.clone())
        .await;
    
    assert_eq!(low_stock_response.status_code(), 200);
    let low_stock_body: serde_json::Value = low_stock_response.json();
    assert!(low_stock_body["products"].is_array());
}

// ============================================================================
// 購物車 API 整合測試
// ============================================================================

#[tokio::test]
async fn test_cart_management_integration() {
    let server = create_test_server().await;
    let token = setup_test_user(&server).await;
    setup_test_products(&server, &token).await;
    
    let (header_name, header_value) = create_auth_header(&token);
    
    // 1. 測試取得空購物車
    let empty_cart_response = server
        .get("/api/cart")
        .add_header(header_name.clone(), header_value.clone())
        .await;
    
    assert_eq!(empty_cart_response.status_code(), 200);
    let empty_cart_body: serde_json::Value = empty_cart_response.json();
    assert_eq!(empty_cart_body["success"], true);
    assert_eq!(empty_cart_body["data"]["cart"]["total_items"], 0);
    
    // 2. 測試添加商品到購物車
    let add_to_cart_data = json!({
        "product_id": 1,
        "quantity": 2
    });
    
    let add_response = server
        .post("/api/cart")
        .add_header(header_name.clone(), header_value.clone())
        .json(&add_to_cart_data)
        .await;
    
    if add_response.status_code() == 200 {
        let add_body: serde_json::Value = add_response.json();
        assert_eq!(add_body["success"], true);
        
        // 3. 測試取得購物車內容
        let cart_response = server
            .get("/api/cart")
            .add_header(header_name.clone(), header_value.clone())
            .await;
        
        assert_eq!(cart_response.status_code(), 200);
        let cart_body: serde_json::Value = cart_response.json();
        assert_eq!(cart_body["success"], true);
        assert!(cart_body["data"]["cart"]["total_items"].as_u64().unwrap() > 0);
        
        // 4. 測試購物車摘要
        let summary_response = server
            .get("/api/cart/summary")
            .add_header(header_name.clone(), header_value.clone())
            .await;
        
        assert_eq!(summary_response.status_code(), 200);
        let summary_body: serde_json::Value = summary_response.json();
        assert_eq!(summary_body["success"], true);
        assert!(summary_body["data"]["summary"]["total_items"].is_number());
        
        // 5. 測試購物車驗證
        let validate_response = server
            .get("/api/cart/validate")
            .add_header(header_name.clone(), header_value.clone())
            .await;
        
        assert_eq!(validate_response.status_code(), 200);
        let validate_body: serde_json::Value = validate_response.json();
        assert_eq!(validate_body["success"], true);
        assert!(validate_body["data"]["validation"]["is_valid"].is_boolean());
    }
    
    // 6. 測試清空購物車
    let clear_response = server
        .delete("/api/cart/clear")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(clear_response.status_code(), 200);
}

// ============================================================================
// 訂單 API 整合測試
// ============================================================================

#[tokio::test]
async fn test_order_management_integration() {
    let server = create_test_server().await;
    let token = setup_test_user(&server).await;
    setup_test_products(&server, &token).await;
    
    let (header_name, header_value) = create_auth_header(&token);
    
    // 1. 測試從請求建立訂單
    let order_data = json!({
        "items": [
            {
                "product_id": 1,
                "quantity": 2
            }
        ]
    });
    
    let create_order_response = server
        .post("/api/orders")
        .add_header(header_name.clone(), header_value.clone())
        .json(&order_data)
        .await;
    
    if create_order_response.status_code() == 200 {
        let create_order_body: serde_json::Value = create_order_response.json();
        assert_eq!(create_order_body["success"], true);
        assert!(create_order_body["data"]["order"]["order"]["order_number"].is_string());
        
        let order_id = create_order_body["data"]["order"]["order"]["id"].as_i64().unwrap();
        let order_number = create_order_body["data"]["order"]["order"]["order_number"].as_str().unwrap();
        
        // 2. 測試查詢訂單列表
        let orders_response = server
            .get("/api/orders")
            .add_header(header_name.clone(), header_value.clone())
            .await;
        
        assert_eq!(orders_response.status_code(), 200);
        let orders_body: serde_json::Value = orders_response.json();
        assert_eq!(orders_body["success"], true);
        assert!(orders_body["data"]["orders"].is_array());
        
        // 3. 測試根據 ID 查詢訂單詳情
        let order_details_response = server
            .get(&format!("/api/orders/{}", order_id))
            .add_header(header_name.clone(), header_value.clone())
            .await;
        
        assert_eq!(order_details_response.status_code(), 200);
        
        // 4. 測試根據訂單編號查詢訂單
        let order_by_number_response = server
            .get(&format!("/api/orders/number/{}", order_number))
            .add_header(header_name.clone(), header_value.clone())
            .await;
        
        assert_eq!(order_by_number_response.status_code(), 200);
        
        // 5. 測試訂單數量統計
        let count_response = server
            .get("/api/orders/count")
            .add_header(header_name.clone(), header_value.clone())
            .await;
        
        assert_eq!(count_response.status_code(), 200);
        let count_body: serde_json::Value = count_response.json();
        assert_eq!(count_body["success"], true);
        assert!(count_body["data"]["count"].is_number());
        
        // 6. 測試更新訂單狀態
        let status_update_data = json!({
            "status": "confirmed"
        });
        
        let status_update_response = server
            .put(&format!("/api/orders/{}/status", order_id))
            .add_header(header_name.clone(), header_value.clone())
            .json(&status_update_data)
            .await;
        
        if status_update_response.status_code() == 200 {
            let status_update_body: serde_json::Value = status_update_response.json();
            assert_eq!(status_update_body["success"], true);
        }
        
        // 7. 測試取消訂單
        let cancel_response = server
            .put(&format!("/api/orders/{}/cancel", order_id))
            .add_header(header_name, header_value)
            .await;
        
        if cancel_response.status_code() == 200 {
            let cancel_body: serde_json::Value = cancel_response.json();
            assert_eq!(cancel_body["success"], true);
        }
    }
}

// ============================================================================
// 檔案上傳整合測試
// ============================================================================

#[tokio::test]
async fn test_file_upload_integration() {
    let server = create_test_server().await;
    let token = setup_test_user(&server).await;
    
    let (header_name, header_value) = create_auth_header(&token);
    
    // 1. 測試 CSV 檔案上傳
    let csv_content = "nhi_code,name,manufacturer,unit,unit_price,stock_quantity,description\nTEST001,測試藥品1,測試製藥,盒,100.0,50,測試描述1\nTEST002,測試藥品2,測試製藥,瓶,200.0,30,測試描述2";
    
    let mut csv_temp_file = NamedTempFile::new().expect("Failed to create temp CSV file");
    csv_temp_file.write_all(csv_content.as_bytes()).expect("Failed to write CSV content");
    
    // 讀取 CSV 檔案內容
    let csv_file_content = std::fs::read(csv_temp_file.path()).expect("Failed to read CSV file");
    
    let csv_upload_response = server
        .post("/api/products/import/csv")
        .add_header(header_name.clone(), header_value.clone())
        .add_header(
            axum::http::HeaderName::from_static("content-type"),
            axum::http::HeaderValue::from_static("text/csv")
        )
        .bytes(csv_file_content.into())
        .await;
    
    if csv_upload_response.status_code() == 200 {
        let csv_body: serde_json::Value = csv_upload_response.json();
        assert!(csv_body["result"]["imported_count"].is_number());
        assert_eq!(csv_body["result"]["error_count"], 0);
    }
    
    // 2. 測試無效檔案上傳
    let invalid_content = "invalid,csv,content\nwithout,proper,headers";
    let mut invalid_temp_file = NamedTempFile::new().expect("Failed to create temp file");
    invalid_temp_file.write_all(invalid_content.as_bytes()).expect("Failed to write content");
    
    // 讀取無效檔案內容
    let invalid_file_content = std::fs::read(invalid_temp_file.path()).expect("Failed to read invalid file");
    
    let invalid_upload_response = server
        .post("/api/products/import/csv")
        .add_header(header_name, header_value)
        .add_header(
            axum::http::HeaderName::from_static("content-type"),
            axum::http::HeaderValue::from_static("text/csv")
        )
        .bytes(invalid_file_content.into())
        .await;
    
    // 無效檔案應該返回錯誤或部分成功
    let invalid_status = invalid_upload_response.status_code();
    assert!(invalid_status == 200 || invalid_status == 400);
}

// ============================================================================
// 系統健康檢查整合測試
// ============================================================================

#[tokio::test]
async fn test_health_check_integration() {
    let server = create_test_server().await;
    
    // 1. 測試基本健康檢查
    let health_response = server
        .get("/health")
        .await;
    
    assert_eq!(health_response.status_code(), 200);
    let health_body: serde_json::Value = health_response.json();
    assert!(health_body["status"].is_string());
    assert!(health_body["timestamp"].is_string());
    assert!(health_body["version"].is_string());
    
    // 2. 測試詳細健康檢查
    let detailed_health_response = server
        .get("/health/detailed")
        .await;
    
    assert_eq!(detailed_health_response.status_code(), 200);
    let detailed_body: serde_json::Value = detailed_health_response.json();
    assert!(detailed_body["status"].is_string());
    assert!(detailed_body["checks"].is_object());
    assert!(detailed_body["summary"].is_object());
    
    // 3. 測試 API 資訊端點
    let api_info_response = server
        .get("/")
        .await;
    
    assert_eq!(api_info_response.status_code(), 200);
    let api_info_body: serde_json::Value = api_info_response.json();
    assert_eq!(api_info_body["name"], "Pharmacy Procurement System API");
    assert!(api_info_body["endpoints"].is_object());
}

// ============================================================================
// 錯誤處理整合測試
// ============================================================================

#[tokio::test]
async fn test_error_handling_integration() {
    let server = create_test_server().await;
    let token = setup_test_user(&server).await;
    
    let (header_name, header_value) = create_auth_header(&token);
    
    // 1. 測試未授權存取
    let unauthorized_response = server
        .get("/api/products")
        .await;
    
    assert_eq!(unauthorized_response.status_code(), 401);
    
    // 2. 測試無效 token
    let invalid_token_response = server
        .get("/api/products")
        .add_header(
            HeaderName::from_static("authorization"),
            HeaderValue::from_static("Bearer invalid_token")
        )
        .await;
    
    assert_eq!(invalid_token_response.status_code(), 401);
    
    // 3. 測試不存在的資源
    let not_found_response = server
        .get("/api/products/99999")
        .add_header(header_name.clone(), header_value.clone())
        .await;
    
    assert_eq!(not_found_response.status_code(), 404);
    
    // 4. 測試無效的請求資料
    let invalid_data = json!({
        "invalid": "data"
    });
    
    let invalid_request_response = server
        .post("/api/orders")
        .add_header(header_name, header_value)
        .json(&invalid_data)
        .await;
    
    let invalid_status = invalid_request_response.status_code();
    assert!(invalid_status.as_u16() >= 400);
}