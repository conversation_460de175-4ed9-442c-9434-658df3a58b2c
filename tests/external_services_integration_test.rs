use pharmacy_system::{
    services::{
        notification::{NotificationService, NotificationServiceImpl, EmailNotification, LineNotification},
        backup::{BackupService, BackupServiceImpl},
        file_processing::{FileProcessingService, FileProcessingServiceImpl},
    },
    config::Config,
    database::Database,
    models::User,
};
use mockall::predicate::*;
use std::sync::Arc;
use tempfile::{NamedTempFile, TempDir};
use std::io::Write;
use tokio::fs;

// ============================================================================
// 通知服務整合測試
// ============================================================================

#[tokio::test]
async fn test_notification_service_integration() {
    // 建立測試配置
    let mut config = Config::from_env().unwrap_or_default();
    
    // 設定測試用的 SMTP 配置（使用假的配置進行測試）
    config.email.smtp_host = "smtp.test.com".to_string();
    config.email.smtp_port = 587;
    config.email.smtp_username = "<EMAIL>".to_string();
    config.email.smtp_password = "test_password".to_string();
    config.email.from_email = "<EMAIL>".to_string();
    config.email.from_name = "Test Pharmacy System".to_string();
    
    // 設定測試用的 Line 配置
    config.line.channel_access_token = "test_channel_access_token".to_string();
    config.line.channel_secret = "test_channel_secret".to_string();
    
    let notification_service = NotificationServiceImpl::new(config.clone());
    
    // 建立測試使用者
    let test_user = User {
        id: 1,
        username: "testuser".to_string(),
        email: "<EMAIL>".to_string(),
        password_hash: "hashed_password".to_string(),
        pharmacy_name: "測試藥局".to_string(),
        phone: Some("0912345678".to_string()),
        line_user_id: Some("test_line_user_id".to_string()),
        notification_email: true,
        notification_line: true,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    };
    
    // 1. 測試 Email 通知（會失敗，因為是假的 SMTP 設定，但我們測試邏輯）
    let email_notification = EmailNotification {
        to_email: test_user.email.clone(),
        to_name: test_user.pharmacy_name.clone(),
        subject: "測試通知".to_string(),
        body: "這是一個測試通知".to_string(),
        html_body: Some("<p>這是一個測試通知</p>".to_string()),
    };
    
    let email_result = notification_service.send_email(email_notification).await;
    // 由於使用假的 SMTP 設定，這應該會失敗，但我們檢查錯誤處理
    assert!(email_result.is_err());
    
    // 2. 測試 Line 通知（會失敗，因為是假的 token，但我們測試邏輯）
    let line_notification = LineNotification {
        user_id: test_user.line_user_id.clone().unwrap(),
        message: "測試 Line 通知".to_string(),
        notification_type: "order_confirmation".to_string(),
    };
    
    let line_result = notification_service.send_line_message(line_notification).await;
    // 由於使用假的 token，這應該會失敗，但我們檢查錯誤處理
    assert!(line_result.is_err());
    
    // 3. 測試訂單確認通知（整合測試）
    let order = pharmacy_system::models::Order {
        id: 1,
        order_number: "ORD-20240101-001".to_string(),
        user_id: test_user.id,
        status: pharmacy_system::models::OrderStatus::Pending,
        total_amount: rust_decimal::Decimal::new(15000, 2), // 150.00
        items: vec![],
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    };
    
    let order_confirmation_result = notification_service.send_order_confirmation(&order, &test_user).await;
    // 這也會失敗，但我們檢查邏輯是否正確執行
    assert!(order_confirmation_result.is_err());
}

// ============================================================================
// 備份服務整合測試
// ============================================================================

#[tokio::test]
async fn test_backup_service_integration() {
    // 建立測試資料庫
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let db_path = temp_dir.path().join("test.db");
    let database = Database::new(db_path.to_str().unwrap()).await.expect("Failed to create test database");
    
    // 插入一些測試資料
    sqlx::query!(
        "INSERT INTO users (username, email, password_hash, pharmacy_name, notification_email, notification_line) VALUES (?, ?, ?, ?, ?, ?)",
        "backupuser",
        "<EMAIL>",
        "hashed_password",
        "備份測試藥局",
        true,
        false
    ).execute(database.pool()).await.expect("Failed to insert test data");
    
    // 建立備份服務（使用假的 GCP 配置）
    let backup_service = BackupServiceImpl::new(
        database.pool().clone(),
        "test-backup-bucket".to_string(),
        Some("fake-credentials.json".to_string()),
    ).await;
    
    // 由於 GCP 配置是假的，服務建立可能會失敗
    if backup_service.is_err() {
        println!("Backup service creation failed as expected with fake GCP config");
        return;
    }
    
    let backup_service = backup_service.unwrap();
    
    // 1. 測試本地備份建立
    let backup_result = backup_service.create_backup().await;
    
    if backup_result.is_ok() {
        let backup_info = backup_result.unwrap();
        assert!(!backup_info.file_path.is_empty());
        assert!(backup_info.file_size > 0);
        
        // 檢查備份檔案是否存在
        let backup_exists = fs::metadata(&backup_info.file_path).await.is_ok();
        assert!(backup_exists);
        
        // 2. 測試上傳到雲端（會失敗，因為是假的配置）
        let upload_result = backup_service.upload_to_cloud(&backup_info.file_path).await;
        assert!(upload_result.is_err()); // 預期失敗
        
        // 清理測試檔案
        let _ = fs::remove_file(&backup_info.file_path).await;
    } else {
        println!("Backup creation failed as expected");
    }
    
    // 3. 測試清理舊備份
    let cleanup_result = backup_service.cleanup_old_backups().await;
    // 這個操作可能成功或失敗，取決於實作
    println!("Cleanup result: {:?}", cleanup_result);
}

// ============================================================================
// 檔案處理服務整合測試
// ============================================================================

#[tokio::test]
async fn test_file_processing_service_integration() {
    let file_service = FileProcessingServiceImpl::new();
    
    // 1. 測試 CSV 檔案處理
    let csv_content = "nhi_code,name,manufacturer,unit,unit_price,stock_quantity,description\nA001234567,阿司匹林,台灣製藥,盒,50.5,100,止痛藥\nB002345678,維他命C,健康製藥,瓶,25.0,200,維他命補充劑\nC003456789,普拿疼,國際藥廠,盒,75.0,50,解熱鎮痛劑";
    let csv_data = csv_content.as_bytes().to_vec();
    
    let csv_result = file_service.process_csv_file(csv_data).await;
    assert!(csv_result.is_ok());
    
    let products = csv_result.unwrap();
    assert_eq!(products.len(), 3);
    
    // 驗證第一個產品
    assert_eq!(products[0].nhi_code, "A001234567");
    assert_eq!(products[0].name, "阿司匹林");
    assert_eq!(products[0].manufacturer, "台灣製藥");
    assert_eq!(products[0].stock_quantity, 100);
    
    // 2. 測試包含錯誤資料的 CSV
    let invalid_csv_content = "nhi_code,name,manufacturer,unit,unit_price,stock_quantity,description\nA001234567,阿司匹林,台灣製藥,盒,50.5,100,止痛藥\nINVALID,,,,invalid_price,-10,\nB002345678,維他命C,健康製藥,瓶,25.0,200,維他命補充劑";
    let invalid_csv_data = invalid_csv_content.as_bytes().to_vec();
    
    let invalid_csv_result = file_service.parse_csv_data(invalid_csv_data).await;
    assert!(invalid_csv_result.is_ok());
    
    let valid_products = invalid_csv_result.unwrap();
    // 應該只解析出有效的產品，跳過無效的行
    assert_eq!(valid_products.len(), 2);
    assert_eq!(valid_products[0].nhi_code, "A001234567");
    assert_eq!(valid_products[1].nhi_code, "B002345678");
    
    // 3. 測試空檔案
    let empty_data = Vec::new();
    let empty_result = file_service.process_csv_file(empty_data).await;
    assert!(empty_result.is_err());
    assert!(empty_result.unwrap_err().to_string().contains("File is empty"));
    
    // 4. 測試過大檔案
    let oversized_data = vec![0u8; 11 * 1024 * 1024]; // 11MB
    let oversized_result = file_service.process_csv_file(oversized_data).await;
    assert!(oversized_result.is_err());
    assert!(oversized_result.unwrap_err().to_string().contains("File size exceeds maximum limit"));
    
    // 5. 測試無效的 CSV 格式
    let invalid_format = "這不是有效的CSV格式\n沒有正確的標頭\n隨機資料";
    let invalid_format_data = invalid_format.as_bytes().to_vec();
    
    let invalid_format_result = file_service.process_csv_file(invalid_format_data).await;
    assert!(invalid_format_result.is_err());
    
    // 6. 測試 Excel 檔案處理（建立一個簡單的測試檔案）
    // 注意：這裡我們測試錯誤處理，因為建立真正的 Excel 檔案比較複雜
    let fake_excel_data = b"PK\x03\x04fake excel data".to_vec(); // 假的 Excel 檔案標頭
    
    let excel_result = file_service.process_excel_file(fake_excel_data).await;
    // 這應該會失敗，因為不是真正的 Excel 檔案
    assert!(excel_result.is_err());
}

// ============================================================================
// HTTP 客戶端整合測試（模擬外部 API 呼叫）
// ============================================================================

#[tokio::test]
async fn test_http_client_integration() {
    // 測試 HTTP 客戶端的基本功能
    let client = reqwest::Client::new();
    
    // 1. 測試 GET 請求（使用公開的測試 API）
    let get_result = client
        .get("https://httpbin.org/get")
        .timeout(std::time::Duration::from_secs(10))
        .send()
        .await;
    
    if get_result.is_ok() {
        let response = get_result.unwrap();
        assert!(response.status().is_success());
        
        let json_result = response.json::<serde_json::Value>().await;
        assert!(json_result.is_ok());
    } else {
        println!("HTTP GET test skipped due to network issues");
    }
    
    // 2. 測試 POST 請求
    let post_data = serde_json::json!({
        "test": "data",
        "number": 123
    });
    
    let post_result = client
        .post("https://httpbin.org/post")
        .json(&post_data)
        .timeout(std::time::Duration::from_secs(10))
        .send()
        .await;
    
    if post_result.is_ok() {
        let response = post_result.unwrap();
        assert!(response.status().is_success());
        
        let json_result = response.json::<serde_json::Value>().await;
        if json_result.is_ok() {
            let json_data = json_result.unwrap();
            // 檢查回傳的資料是否包含我們發送的資料
            assert!(json_data["json"]["test"].as_str().unwrap_or("") == "data");
        }
    } else {
        println!("HTTP POST test skipped due to network issues");
    }
    
    // 3. 測試錯誤處理（無效的 URL）
    let invalid_result = client
        .get("https://invalid-domain-that-does-not-exist.com")
        .timeout(std::time::Duration::from_secs(5))
        .send()
        .await;
    
    assert!(invalid_result.is_err());
    
    // 4. 測試超時處理
    let timeout_result = client
        .get("https://httpbin.org/delay/10") // 延遲 10 秒
        .timeout(std::time::Duration::from_secs(2)) // 但只等 2 秒
        .send()
        .await;
    
    if timeout_result.is_err() {
        // 預期會超時
        println!("Timeout test passed");
    }
}

// ============================================================================
// 檔案系統操作整合測試
// ============================================================================

#[tokio::test]
async fn test_filesystem_integration() {
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    
    // 1. 測試檔案建立和寫入
    let test_file_path = temp_dir.path().join("test_file.txt");
    let test_content = "這是測試內容\n包含中文字符\n和多行文字";
    
    let write_result = fs::write(&test_file_path, test_content).await;
    assert!(write_result.is_ok());
    
    // 2. 測試檔案讀取
    let read_result = fs::read_to_string(&test_file_path).await;
    assert!(read_result.is_ok());
    assert_eq!(read_result.unwrap(), test_content);
    
    // 3. 測試檔案元資料
    let metadata_result = fs::metadata(&test_file_path).await;
    assert!(metadata_result.is_ok());
    
    let metadata = metadata_result.unwrap();
    assert!(metadata.is_file());
    assert!(metadata.len() > 0);
    
    // 4. 測試目錄建立
    let test_dir_path = temp_dir.path().join("test_subdir");
    let create_dir_result = fs::create_dir(&test_dir_path).await;
    assert!(create_dir_result.is_ok());
    
    // 5. 測試目錄列表
    let read_dir_result = fs::read_dir(temp_dir.path()).await;
    assert!(read_dir_result.is_ok());
    
    let mut entries = read_dir_result.unwrap();
    let mut found_file = false;
    let mut found_dir = false;
    
    while let Some(entry) = entries.next_entry().await.unwrap() {
        let file_name = entry.file_name();
        if file_name == "test_file.txt" {
            found_file = true;
        } else if file_name == "test_subdir" {
            found_dir = true;
        }
    }
    
    assert!(found_file);
    assert!(found_dir);
    
    // 6. 測試檔案刪除
    let remove_file_result = fs::remove_file(&test_file_path).await;
    assert!(remove_file_result.is_ok());
    
    // 7. 測試目錄刪除
    let remove_dir_result = fs::remove_dir(&test_dir_path).await;
    assert!(remove_dir_result.is_ok());
    
    // 8. 測試檔案複製
    let source_file = temp_dir.path().join("source.txt");
    let dest_file = temp_dir.path().join("dest.txt");
    
    fs::write(&source_file, "複製測試內容").await.expect("Failed to write source file");
    
    let copy_result = fs::copy(&source_file, &dest_file).await;
    assert!(copy_result.is_ok());
    
    let copied_content = fs::read_to_string(&dest_file).await.expect("Failed to read copied file");
    assert_eq!(copied_content, "複製測試內容");
}

// ============================================================================
// 日期時間處理整合測試
// ============================================================================

#[tokio::test]
async fn test_datetime_integration() {
    use chrono::{DateTime, Utc, Duration, TimeZone};
    
    // 1. 測試當前時間
    let now = Utc::now();
    assert!(now.timestamp() > 0);
    
    // 2. 測試時間格式化
    let formatted = now.format("%Y-%m-%d %H:%M:%S UTC").to_string();
    assert!(formatted.contains("UTC"));
    
    // 3. 測試時間解析
    let time_str = "2024-01-01T12:00:00Z";
    let parsed_result = DateTime::parse_from_rfc3339(time_str);
    assert!(parsed_result.is_ok());
    
    let parsed_time = parsed_result.unwrap().with_timezone(&Utc);
    assert_eq!(parsed_time.year(), 2024);
    assert_eq!(parsed_time.month(), 1);
    assert_eq!(parsed_time.day(), 1);
    
    // 4. 測試時間計算
    let future_time = now + Duration::hours(24);
    let duration_diff = future_time - now;
    assert_eq!(duration_diff.num_hours(), 24);
    
    // 5. 測試時區轉換
    let taipei_tz = chrono_tz::Asia::Taipei;
    let taipei_time = now.with_timezone(&taipei_tz);
    
    // 台北時間應該比 UTC 快 8 小時
    let hour_diff = taipei_time.hour() as i32 - now.hour() as i32;
    // 考慮跨日的情況
    let normalized_diff = if hour_diff < -12 { hour_diff + 24 } else if hour_diff > 12 { hour_diff - 24 } else { hour_diff };
    assert_eq!(normalized_diff, 8);
}

// ============================================================================
// JSON 序列化/反序列化整合測試
// ============================================================================

#[tokio::test]
async fn test_json_serialization_integration() {
    use serde_json::{json, Value};
    
    // 1. 測試基本 JSON 操作
    let test_data = json!({
        "name": "測試產品",
        "price": 123.45,
        "active": true,
        "tags": ["藥品", "處方藥"],
        "metadata": {
            "manufacturer": "測試製藥",
            "expiry_date": "2025-12-31"
        }
    });
    
    // 2. 測試序列化
    let json_string = serde_json::to_string(&test_data).expect("Failed to serialize JSON");
    assert!(json_string.contains("測試產品"));
    assert!(json_string.contains("123.45"));
    
    // 3. 測試反序列化
    let parsed_data: Value = serde_json::from_str(&json_string).expect("Failed to deserialize JSON");
    assert_eq!(parsed_data["name"], "測試產品");
    assert_eq!(parsed_data["price"], 123.45);
    assert_eq!(parsed_data["active"], true);
    
    // 4. 測試陣列操作
    let tags = parsed_data["tags"].as_array().expect("Tags should be an array");
    assert_eq!(tags.len(), 2);
    assert_eq!(tags[0], "藥品");
    assert_eq!(tags[1], "處方藥");
    
    // 5. 測試巢狀物件
    let metadata = &parsed_data["metadata"];
    assert_eq!(metadata["manufacturer"], "測試製藥");
    assert_eq!(metadata["expiry_date"], "2025-12-31");
    
    // 6. 測試錯誤處理
    let invalid_json = "{invalid json}";
    let parse_result = serde_json::from_str::<Value>(invalid_json);
    assert!(parse_result.is_err());
}