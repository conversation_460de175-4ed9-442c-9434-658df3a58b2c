use axum_test::TestServer;
use pharmacy_system::api::auth::AppState;
use pharmacy_system::api::create_api_router;
use pharmacy_system::config::Config;
use pharmacy_system::database::Database;
use pharmacy_system::repositories::{
    order::{OrderRepository, SqliteOrderRepository},
    cart::{CartRepository, SqliteCartRepository},
    product::{ProductRepository, SqliteProductRepository},
    user::{UserRepository, SqliteUserRepository},
};
use pharmacy_system::services::{
    order::{OrderService, OrderServiceImpl},
    product::{ProductService, ProductServiceImpl},
    file_processing::{FileProcessingService, FileProcessingServiceImpl},
    auth::{AuthService, AuthServiceImpl},
    cart::{CartService, CartServiceImpl},
    backup::{BackupService, BackupServiceImpl},
    notification::{NotificationService, NotificationServiceImpl},
};
use serde_json::json;
use std::sync::Arc;
use axum::http::{HeaderName, HeaderValue};

async fn create_test_server() -> TestServer {
    // 載入測試配置
    dotenvy::dotenv().ok();
    
    let config = Config::from_env().expect("Failed to load config");
    let database = Database::new(&config.database_url).await.expect("Failed to connect to database");
    
    let db_pool = database.pool().clone();

    // 建立 repositories
    let _user_repository: Arc<dyn UserRepository> = Arc::new(SqliteUserRepository::new(db_pool.clone()));
    let order_repository: Arc<dyn OrderRepository> = Arc::new(SqliteOrderRepository::new(db_pool.clone()));
    let cart_repository: Arc<dyn CartRepository> = Arc::new(SqliteCartRepository::new(db_pool.clone()));
    let product_repository: Arc<dyn ProductRepository> = Arc::new(SqliteProductRepository::new(db_pool.clone()));

    // 建立 services
    let file_processing_service: Arc<dyn FileProcessingService> = Arc::new(FileProcessingServiceImpl::new());
    let auth_service: Arc<dyn AuthService> = Arc::new(AuthServiceImpl::new(
        db_pool.clone(),
        config.clone(),
    ));
    let product_service: Arc<dyn ProductService> = Arc::new(ProductServiceImpl::new(
        product_repository.clone(),
        file_processing_service,
    ));
    let cart_service: Arc<dyn CartService> = Arc::new(CartServiceImpl::new(
        cart_repository.clone(),
        product_service.clone(),
    ));
    let order_service: Arc<dyn OrderService> = Arc::new(OrderServiceImpl::new(
        order_repository.clone(),
        cart_repository.clone(),
        product_service.clone(),
    ));
    let backup_service: Arc<dyn BackupService> = Arc::new(BackupServiceImpl::new(
        db_pool.clone(),
        config.gcp.storage_bucket.clone(),
        config.gcp.credentials_path.clone(),
    ).await.expect("Failed to create backup service"));
    let notification_service: Arc<dyn NotificationService> = Arc::new(NotificationServiceImpl::new(
        config.clone(),
    ));

    // 建立應用狀態
    let app_state = AppState {
        db: db_pool,
        config: config.clone(),
        auth_service,
        product_service,
        cart_service,
        order_service,
        backup_service,
        notification_service,
    };
    
    // 建立測試應用程式
    let app = create_api_router(app_state);
    
    TestServer::new(app).unwrap()
}

fn create_auth_header(token: &str) -> (HeaderName, HeaderValue) {
    (
        HeaderName::from_static("authorization"),
        HeaderValue::from_str(&format!("Bearer {}", token)).unwrap()
    )
}

async fn get_auth_token(server: &TestServer) -> String {
    // 先註冊一個測試使用者
    let user_data = json!({
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "password123",
        "pharmacy_name": "測試藥局",
        "phone": "0912345678"
    });
    
    let _register_response = server
        .post("/api/auth/register")
        .json(&user_data)
        .await;
    
    // 登入取得 token
    let login_data = json!({
        "username": "testuser",
        "password": "password123"
    });
    
    let login_response = server
        .post("/api/auth/login")
        .json(&login_data)
        .await;
    
    let login_body: serde_json::Value = login_response.json();
    login_body["data"]["token"].as_str().unwrap().to_string()
}

#[tokio::test]
async fn test_product_list_api() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["products"].is_array());
    assert!(body["total"].is_number());
    assert!(body["page"].is_number());
    assert!(body["limit"].is_number());
}

#[tokio::test]
async fn test_product_list_with_search() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products")
        .add_header(header_name, header_value)
        .add_query_param("search", "阿司匹林")
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["products"].is_array());
}

#[tokio::test]
async fn test_product_list_with_manufacturer_filter() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products")
        .add_header(header_name, header_value)
        .add_query_param("manufacturer", "台灣製藥")
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["products"].is_array());
}

#[tokio::test]
async fn test_product_list_with_active_filter() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products")
        .add_header(header_name, header_value)
        .add_query_param("is_active", "true")
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["products"].is_array());
}

#[tokio::test]
async fn test_product_list_with_pagination() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products")
        .add_header(header_name, header_value)
        .add_query_param("page", "1")
        .add_query_param("limit", "10")
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["products"].is_array());
    assert_eq!(body["page"], 1);
    assert_eq!(body["limit"], 10);
}

#[tokio::test]
async fn test_product_list_combined_filters() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products")
        .add_header(header_name, header_value)
        .add_query_param("search", "藥品")
        .add_query_param("manufacturer", "台灣")
        .add_query_param("is_active", "true")
        .add_query_param("page", "1")
        .add_query_param("limit", "20")
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["products"].is_array());
    assert_eq!(body["page"], 1);
    assert_eq!(body["limit"], 20);
}

#[tokio::test]
async fn test_get_product_by_id_not_found() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products/99999")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 404);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
}

#[tokio::test]
async fn test_get_product_by_nhi_code_not_found() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products/nhi/NONEXISTENT")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 404);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
}

#[tokio::test]
async fn test_get_low_stock_products() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products/low-stock")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["products"].is_array());
}

#[tokio::test]
async fn test_update_stock_invalid_product() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .post("/api/products/99999/stock")
        .add_header(header_name, header_value)
        .json(&json!({ "quantity": 100 }))
        .await;
    
    // 應該返回 404，因為產品不存在
    assert_eq!(response.status_code(), 404);
}

#[tokio::test]
async fn test_update_stock_negative_quantity() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .post("/api/products/1/stock")
        .add_header(header_name, header_value)
        .json(&json!({ "quantity": -10 }))
        .await;
    
    // 應該返回錯誤，因為數量不能為負數
    assert_eq!(response.status_code(), 400);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
}

#[tokio::test]
async fn test_get_stock_status_invalid_product() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/products/99999/stock/status")
        .add_header(header_name, header_value)
        .await;
    
    // 應該返回 404，因為產品不存在
    assert_eq!(response.status_code(), 404);
}

#[tokio::test]
async fn test_check_stock_availability_invalid_product() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .post("/api/products/99999/stock/check")
        .add_header(header_name, header_value)
        .json(&json!({ "required_quantity": 10 }))
        .await;
    
    // 應該返回 404，因為產品不存在
    assert_eq!(response.status_code(), 404);
}

#[tokio::test]
async fn test_bulk_update_stock_empty_updates() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .post("/api/products/stock/bulk-update")
        .add_header(header_name, header_value)
        .json(&json!({ "updates": [] }))
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["updated_products"].is_array());
    assert_eq!(body["updated_products"].as_array().unwrap().len(), 0);
}

#[tokio::test]
async fn test_unauthorized_access() {
    let server = create_test_server().await;
    
    // 不提供 Authorization header
    let response = server
        .get("/api/products")
        .await;
    
    // 應該返回 401 未授權
    assert_eq!(response.status_code(), 401);
}

#[tokio::test]
async fn test_invalid_token() {
    let server = create_test_server().await;
    let (header_name, header_value) = (
        HeaderName::from_static("authorization"),
        HeaderValue::from_static("Bearer invalid_token")
    );
    
    let response = server
        .get("/api/products")
        .add_header(header_name, header_value)
        .await;
    
    // 應該返回 401 未授權
    assert_eq!(response.status_code(), 401);
}