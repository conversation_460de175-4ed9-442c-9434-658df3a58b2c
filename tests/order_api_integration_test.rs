use axum_test::TestServer;
use pharmacy_system::api::auth::AppState;
use pharmacy_system::api::create_api_router;
use pharmacy_system::config::Config;
use pharmacy_system::database::Database;
use pharmacy_system::repositories::{
    order::{OrderRepository, SqliteOrderRepository},
    cart::{CartRepository, SqliteCartRepository},
    product::{ProductRepository, SqliteProductRepository},
    user::{UserRepository, SqliteUserRepository},
};
use pharmacy_system::services::{
    order::{OrderService, OrderServiceImpl},
    product::{ProductService, ProductServiceImpl},
    file_processing::{FileProcessingService, FileProcessingServiceImpl},
    auth::{AuthService, AuthServiceImpl},
    cart::{CartService, CartServiceImpl},
    backup::{BackupService, BackupServiceImpl},
    notification::{NotificationService, NotificationServiceImpl},
};
use serde_json::json;
use std::sync::Arc;
use axum::http::{HeaderName, HeaderValue};

async fn create_test_server() -> TestServer {
    // 載入測試配置
    dotenvy::dotenv().ok();
    
    let config = Config::from_env().expect("Failed to load config");
    let database = Database::new(&config.database_url).await.expect("Failed to connect to database");
    
    let db_pool = database.pool().clone();

    // 建立 repositories
    let _user_repository: Arc<dyn UserRepository> = Arc::new(SqliteUserRepository::new(db_pool.clone()));
    let order_repository: Arc<dyn OrderRepository> = Arc::new(SqliteOrderRepository::new(db_pool.clone()));
    let cart_repository: Arc<dyn CartRepository> = Arc::new(SqliteCartRepository::new(db_pool.clone()));
    let product_repository: Arc<dyn ProductRepository> = Arc::new(SqliteProductRepository::new(db_pool.clone()));

    // 建立 services
    let file_processing_service: Arc<dyn FileProcessingService> = Arc::new(FileProcessingServiceImpl::new());
    let auth_service: Arc<dyn AuthService> = Arc::new(AuthServiceImpl::new(
        db_pool.clone(),
        config.clone(),
    ));
    let product_service: Arc<dyn ProductService> = Arc::new(ProductServiceImpl::new(
        product_repository.clone(),
        file_processing_service,
    ));
    let cart_service: Arc<dyn CartService> = Arc::new(CartServiceImpl::new(
        cart_repository.clone(),
        product_service.clone(),
    ));
    let order_service: Arc<dyn OrderService> = Arc::new(OrderServiceImpl::new(
        order_repository.clone(),
        cart_repository.clone(),
        product_service.clone(),
    ));
    let backup_service: Arc<dyn BackupService> = Arc::new(BackupServiceImpl::new(
        db_pool.clone(),
        config.gcp.storage_bucket.clone(),
        config.gcp.credentials_path.clone(),
    ).await.expect("Failed to create backup service"));
    let notification_service: Arc<dyn NotificationService> = Arc::new(NotificationServiceImpl::new(
        config.clone(),
    ));

    // 建立應用狀態
    let app_state = AppState {
        db: db_pool,
        config: config.clone(),
        auth_service,
        product_service,
        cart_service,
        order_service,
        backup_service,
        notification_service,
    };
    
    // 建立測試應用程式
    let app = create_api_router(app_state);
    
    TestServer::new(app).unwrap()
}

fn create_auth_header(token: &str) -> (HeaderName, HeaderValue) {
    (
        HeaderName::from_static("authorization"),
        HeaderValue::from_str(&format!("Bearer {}", token)).unwrap()
    )
}

async fn setup_test_data(server: &TestServer, token: &str) {
    let (header_name, header_value) = create_auth_header(token);
    
    // 建立測試產品資料
    let test_products = vec![
        json!({
            "nhi_code": "A001234567",
            "name": "阿司匹林 100mg",
            "manufacturer": "台灣製藥",
            "unit": "盒",
            "unit_price": 50.0,
            "stock_quantity": 100,
            "description": "止痛藥",
            "is_active": true
        }),
        json!({
            "nhi_code": "B002345678", 
            "name": "普拿疼 500mg",
            "manufacturer": "國際藥廠",
            "unit": "盒",
            "unit_price": 75.0,
            "stock_quantity": 50,
            "description": "解熱鎮痛劑",
            "is_active": true
        })
    ];
    
    // 直接插入資料庫（因為沒有產品建立 API）
    // 這裡我們需要直接操作資料庫
}

async fn get_auth_token(server: &TestServer) -> String {
    // 先註冊一個測試使用者
    let user_data = json!({
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "password123",
        "pharmacy_name": "測試藥局",
        "phone": "0912345678"
    });
    
    let _register_response = server
        .post("/api/auth/register")
        .json(&user_data)
        .await;
    
    // 登入取得 token
    let login_data = json!({
        "username": "testuser",
        "password": "password123"
    });
    
    let login_response = server
        .post("/api/auth/login")
        .json(&login_data)
        .await;
    
    let login_body: serde_json::Value = login_response.json();
    login_body["data"]["token"].as_str().unwrap().to_string()
}

async fn setup_cart_with_items(server: &TestServer, token: &str) {
    let (header_name, header_value) = create_auth_header(token);
    
    // 添加商品到購物車
    let add_to_cart_data = json!({
        "product_id": 1,
        "quantity": 2
    });
    
    let _response = server
        .post("/api/cart")
        .add_header(header_name.clone(), header_value.clone())
        .json(&add_to_cart_data)
        .await;
}

#[tokio::test]
async fn test_create_order_from_cart_success() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    // 先設置購物車
    setup_cart_with_items(&server, &token).await;
    
    // 從購物車建立訂單
    let response = server
        .post("/api/orders/cart")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    assert!(body["data"]["order"].is_object());
    assert!(body["data"]["order"]["order"]["order_number"].is_string());
}

#[tokio::test]
async fn test_create_order_from_cart_empty_cart() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    // 不添加任何商品到購物車，直接建立訂單
    let response = server
        .post("/api/orders/cart")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 400); // 空購物車是驗證錯誤，返回 400
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
    assert!(body["error"].as_str().unwrap().contains("Cart is empty"));
}

#[tokio::test]
async fn test_create_order_from_request_success() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let order_data = json!({
        "items": [
            {
                "product_id": 1,
                "quantity": 2
            }
        ]
    });
    
    let response = server
        .post("/api/orders")
        .add_header(header_name, header_value)
        .json(&order_data)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    assert!(body["data"]["order"].is_object());
    assert!(body["data"]["order"]["order"]["order_number"].is_string());
}

#[tokio::test]
async fn test_create_order_from_request_invalid_product() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let order_data = json!({
        "items": [
            {
                "product_id": 99999, // 不存在的產品
                "quantity": 2
            }
        ]
    });
    
    let response = server
        .post("/api/orders")
        .add_header(header_name, header_value)
        .json(&order_data)
        .await;
    
    assert_eq!(response.status_code(), 404);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
}

#[tokio::test]
async fn test_create_order_from_request_insufficient_stock() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let order_data = json!({
        "items": [
            {
                "product_id": 1,
                "quantity": 10000 // 超過庫存的數量
            }
        ]
    });
    
    let response = server
        .post("/api/orders")
        .add_header(header_name, header_value)
        .json(&order_data)
        .await;
    
    assert_eq!(response.status_code(), 400);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
    assert!(body["error"].as_str().unwrap().contains("Insufficient stock"));
}

#[tokio::test]
async fn test_list_orders_success() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/orders")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    assert!(body["data"]["orders"].is_array());
    assert!(body["data"]["pagination"].is_object());
}

#[tokio::test]
async fn test_list_orders_with_status_filter() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/orders")
        .add_header(header_name, header_value)
        .add_query_param("status", "pending")
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    assert!(body["data"]["orders"].is_array());
}

#[tokio::test]
async fn test_list_orders_with_pagination() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/orders")
        .add_header(header_name, header_value)
        .add_query_param("page", "1")
        .add_query_param("limit", "10")
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    assert!(body["data"]["orders"].is_array());
    assert_eq!(body["data"]["pagination"]["page"], 1);
    assert_eq!(body["data"]["pagination"]["limit"], 10);
}

#[tokio::test]
async fn test_get_order_details_not_found() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/orders/99999")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], false);
    assert!(body["message"].as_str().unwrap().contains("Order not found"));
}

#[tokio::test]
async fn test_get_order_by_number_not_found() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/orders/number/NONEXISTENT")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], false);
    assert!(body["message"].as_str().unwrap().contains("Order not found"));
}

#[tokio::test]
async fn test_get_order_count() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/orders/count")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    assert!(body["data"]["count"].is_number());
}

#[tokio::test]
async fn test_update_order_status_invalid_order() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let status_data = json!({
        "status": "confirmed"
    });
    
    let response = server
        .put("/api/orders/99999/status")
        .add_header(header_name, header_value)
        .json(&status_data)
        .await;
    
    assert_eq!(response.status_code(), 404);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
}

#[tokio::test]
async fn test_update_order_status_invalid_status() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let status_data = json!({
        "status": "invalid_status"
    });
    
    let response = server
        .put("/api/orders/1/status")
        .add_header(header_name, header_value)
        .json(&status_data)
        .await;
    
    assert_eq!(response.status_code(), 400);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
    assert!(body["error"].as_str().unwrap().contains("Invalid status"));
}

#[tokio::test]
async fn test_cancel_order_invalid_order() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .put("/api/orders/99999/cancel")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 404);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
}

#[tokio::test]
async fn test_unauthorized_access() {
    let server = create_test_server().await;
    
    // 不提供 Authorization header
    let response = server
        .get("/api/orders")
        .await;
    
    // 應該返回 401 未授權
    assert_eq!(response.status_code(), 401);
}

#[tokio::test]
async fn test_invalid_token() {
    let server = create_test_server().await;
    let (header_name, header_value) = (
        HeaderName::from_static("authorization"),
        HeaderValue::from_static("Bearer invalid_token")
    );
    
    let response = server
        .get("/api/orders")
        .add_header(header_name, header_value)
        .await;
    
    // 應該返回 401 未授權
    assert_eq!(response.status_code(), 401);
}

// 購物車 API 測試
#[tokio::test]
async fn test_get_cart_empty() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/cart")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    // 空購物車應該返回空的項目陣列
    assert!(body["data"]["cart"]["items"].is_array());
    assert_eq!(body["data"]["cart"]["items"].as_array().unwrap().len(), 0);
    assert_eq!(body["data"]["cart"]["total_items"], 0);
}

#[tokio::test]
async fn test_add_to_cart_success() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let add_to_cart_data = json!({
        "product_id": 1,
        "quantity": 2
    });
    
    let response = server
        .post("/api/cart")
        .add_header(header_name, header_value)
        .json(&add_to_cart_data)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    assert!(body["data"]["cart"].is_object());
    assert!(body["message"].as_str().unwrap().contains("added to cart"));
}

#[tokio::test]
async fn test_add_to_cart_invalid_product() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let add_to_cart_data = json!({
        "product_id": 99999, // 不存在的產品
        "quantity": 2
    });
    
    let response = server
        .post("/api/cart")
        .add_header(header_name, header_value)
        .json(&add_to_cart_data)
        .await;
    
    assert_eq!(response.status_code(), 404);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
}

#[tokio::test]
async fn test_add_to_cart_insufficient_stock() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let add_to_cart_data = json!({
        "product_id": 1,
        "quantity": 10000 // 超過庫存的數量
    });
    
    let response = server
        .post("/api/cart")
        .add_header(header_name, header_value)
        .json(&add_to_cart_data)
        .await;
    
    assert_eq!(response.status_code(), 400);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
    assert!(body["error"].as_str().unwrap().contains("Insufficient stock"));
}

#[tokio::test]
async fn test_get_cart_summary() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/cart/summary")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    assert!(body["data"]["summary"].is_object());
    assert!(body["data"]["summary"]["total_items"].is_number());
    assert!(body["data"]["summary"]["total_amount"].is_string()); // Decimal 序列化為字串
}

#[tokio::test]
async fn test_validate_cart_stock() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .get("/api/cart/validate")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert_eq!(body["success"], true);
    assert!(body["data"]["validation"].is_object());
    assert!(body["data"]["validation"]["is_valid"].is_boolean());
    assert!(body["data"]["validation"]["invalid_items"].is_array());
}

#[tokio::test]
async fn test_clear_cart() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .delete("/api/cart/clear")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["success"].is_boolean());
}

#[tokio::test]
async fn test_update_cart_item_not_found() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let update_data = json!({
        "quantity": 3
    });
    
    let response = server
        .put("/api/cart/items/99999")
        .add_header(header_name, header_value)
        .json(&update_data)
        .await;
    
    assert_eq!(response.status_code(), 404);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
}

#[tokio::test]
async fn test_remove_cart_item_not_found() {
    let server = create_test_server().await;
    let token = get_auth_token(&server).await;
    let (header_name, header_value) = create_auth_header(&token);
    
    let response = server
        .delete("/api/cart/items/99999")
        .add_header(header_name, header_value)
        .await;
    
    assert_eq!(response.status_code(), 404);
    
    let body: serde_json::Value = response.json();
    assert!(body["error"].is_string());
}