use axum_test::TestServer;
use pharmacy_system::api::auth::AppState;
use pharmacy_system::config::Config;
use pharmacy_system::database::Database;
use serde_json::json;

async fn create_test_server() -> TestServer<AppState> {
    // 載入測試配置
    dotenvy::dotenv().ok();
    
    let config = Config::from_env().expect("Failed to load config");
    let database = Database::new(&config.database_url).await.expect("Failed to connect to database");
    
    let app_state = AppState {
        db: database.pool,
        config,
    };
    
    // 建立測試應用程式
    let app = pharmacy_system::create_app(app_state);
    
    TestServer::new(app).unwrap()
}

#[tokio::test]
async fn test_health_check() {
    let server = create_test_server().await;
    
    let response = server.get("/health").await;
    assert_eq!(response.status_code(), 200);
    assert_eq!(response.text(), "OK");
}

#[tokio::test]
async fn test_auth_register() {
    let server = create_test_server().await;
    
    let user_data = json!({
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "password123",
        "pharmacy_name": "測試藥局",
        "phone": "**********"
    });
    
    let response = server
        .post("/api/auth/register")
        .json(&user_data)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["token"].is_string());
}

#[tokio::test]
async fn test_auth_login() {
    let server = create_test_server().await;
    
    let login_data = json!({
        "username": "testuser",
        "password": "password123"
    });
    
    let response = server
        .post("/api/auth/login")
        .json(&login_data)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["token"].is_string());
}

#[tokio::test]
async fn test_create_product() {
    let server = create_test_server().await;
    
    // 先登入取得 token
    let login_data = json!({
        "username": "testuser",
        "password": "password123"
    });
    
    let login_response = server
        .post("/api/auth/login")
        .json(&login_data)
        .await;
    
    let login_body: serde_json::Value = login_response.json();
    let token = login_body["token"].as_str().unwrap();
    
    // 建立產品
    let product_data = json!({
        "nhi_code": "TEST001",
        "name": "測試藥品",
        "manufacturer": "測試藥廠",
        "unit": "盒",
        "unit_price": 100.50,
        "stock_quantity": 50,
        "description": "測試藥品描述"
    });
    
    let response = server
        .post("/api/products")
        .header("Authorization", format!("Bearer {}", token))
        .json(&product_data)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["product"]["id"].is_number());
}

#[tokio::test]
async fn test_create_order() {
    let server = create_test_server().await;
    
    // 先登入取得 token
    let login_data = json!({
        "username": "testuser",
        "password": "password123"
    });
    
    let login_response = server
        .post("/api/auth/login")
        .json(&login_data)
        .await;
    
    let login_body: serde_json::Value = login_response.json();
    let token = login_body["token"].as_str().unwrap();
    
    // 建立訂單
    let order_data = json!({
        "items": [
            {
                "product_id": 1,
                "quantity": 2
            }
        ],
        "notes": "測試訂單"
    });
    
    let response = server
        .post("/api/orders")
        .header("Authorization", format!("Bearer {}", token))
        .json(&order_data)
        .await;
    
    assert_eq!(response.status_code(), 200);
    
    let body: serde_json::Value = response.json();
    assert!(body["order"]["id"].is_number());
}

#[tokio::test]
async fn test_backup_creation() {
    let server = create_test_server().await;
    
    // 先登入取得 token
    let login_data = json!({
        "username": "testuser",
        "password": "password123"
    });
    
    let login_response = server
        .post("/api/auth/login")
        .json(&login_data)
        .await;
    
    let login_body: serde_json::Value = login_response.json();
    let token = login_body["token"].as_str().unwrap();
    
    // 建立備份
    let response = server
        .post("/api/backup/create")
        .header("Authorization", format!("Bearer {}", token))
        .await;
    
    // 備份可能需要一些時間，所以我們檢查狀態碼
    assert!(response.status_code() == 200 || response.status_code() == 500);
}

#[tokio::test]
async fn test_notification_send() {
    let server = create_test_server().await;
    
    // 先登入取得 token
    let login_data = json!({
        "username": "testuser",
        "password": "password123"
    });
    
    let login_response = server
        .post("/api/auth/login")
        .json(&login_data)
        .await;
    
    let login_body: serde_json::Value = login_response.json();
    let token = login_body["token"].as_str().unwrap();
    
    // 測試通知
    let response = server
        .post("/api/notifications/test")
        .header("Authorization", format!("Bearer {}", token))
        .await;
    
    // 通知可能因為配置問題失敗，所以我們檢查狀態碼
    assert!(response.status_code() == 200 || response.status_code() == 500);
} 