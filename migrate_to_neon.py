#!/usr/bin/env python3
"""
SQLite to Neon DB (PostgreSQL) Migration Script
將SQLite數據庫遷移到Neon PostgreSQL數據庫
"""

import sqlite3
import psycopg2
import json
import os
from datetime import datetime
from decimal import Decimal
import sys

# 從環境變量讀取數據庫連接信息
def get_database_urls():
    # 讀取.env文件
    env_vars = {}
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
    except FileNotFoundError:
        print("錯誤：找不到.env文件")
        sys.exit(1)
    
    database_url = env_vars.get('DATABASE_URL')
    if not database_url:
        print("錯誤：在.env文件中找不到DATABASE_URL")
        sys.exit(1)
    
    return database_url

def connect_sqlite(db_path='pharmacy.db'):
    """連接SQLite數據庫"""
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 讓結果可以像字典一樣訪問
        return conn
    except Exception as e:
        print(f"連接SQLite數據庫失敗: {e}")
        return None

def connect_postgresql(database_url):
    """連接PostgreSQL數據庫"""
    try:
        conn = psycopg2.connect(database_url)
        return conn
    except Exception as e:
        print(f"連接PostgreSQL數據庫失敗: {e}")
        return None

def backup_sqlite_data(sqlite_conn):
    """備份SQLite數據到JSON文件"""
    print("正在備份SQLite數據...")
    
    backup_data = {}
    tables = ['users', 'products', 'orders', 'order_items', 'carts', 'cart_items', 
              'notifications', 'notification_preferences', 'backup_logs']
    
    cursor = sqlite_conn.cursor()
    
    for table in tables:
        try:
            cursor.execute(f"SELECT * FROM {table}")
            rows = cursor.fetchall()
            backup_data[table] = []
            
            for row in rows:
                row_dict = dict(row)
                # 處理Decimal類型
                for key, value in row_dict.items():
                    if isinstance(value, Decimal):
                        row_dict[key] = str(value)
                backup_data[table].append(row_dict)
            
            print(f"  {table}: {len(backup_data[table])} 條記錄")
        except Exception as e:
            print(f"  警告：無法備份表格 {table}: {e}")
            backup_data[table] = []
    
    # 保存備份文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"sqlite_backup_{timestamp}.json"
    
    with open(backup_filename, 'w', encoding='utf-8') as f:
        json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"SQLite數據已備份到: {backup_filename}")
    return backup_data

def run_migrations(pg_conn):
    """運行PostgreSQL遷移"""
    print("正在運行PostgreSQL遷移...")
    
    cursor = pg_conn.cursor()
    
    # 檢查是否已經有遷移表
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = '_sqlx_migrations'
        );
    """)
    
    has_migrations = cursor.fetchone()[0]
    
    if not has_migrations:
        print("正在創建數據庫結構...")
        
        # 讀取並執行遷移文件
        migration_files = [
            'migrations/20240101000001_initial_schema.sql',
            'migrations/20240102000001_add_cart_tables.sql',
            'migrations/20240103000001_add_user_roles.sql'
        ]
        
        for migration_file in migration_files:
            if os.path.exists(migration_file):
                print(f"  執行遷移: {migration_file}")
                with open(migration_file, 'r', encoding='utf-8') as f:
                    migration_sql = f.read()
                    cursor.execute(migration_sql)
            else:
                print(f"  警告：找不到遷移文件 {migration_file}")
        
        pg_conn.commit()
        print("數據庫結構創建完成")
    else:
        print("數據庫結構已存在，跳過遷移")

def clear_postgresql_data(pg_conn):
    """清空PostgreSQL數據（保留結構）"""
    print("正在清空PostgreSQL數據...")
    
    cursor = pg_conn.cursor()
    
    # 按照外鍵依賴順序刪除數據
    tables_to_clear = [
        'cart_items',
        'carts', 
        'order_items',
        'orders',
        'role_permissions',
        'notification_preferences',
        'notifications',
        'users',
        'products',
        'permissions',
        'roles',
        'backup_logs'
    ]
    
    for table in tables_to_clear:
        try:
            cursor.execute(f"DELETE FROM {table}")
            print(f"  清空表格: {table}")
        except Exception as e:
            print(f"  警告：無法清空表格 {table}: {e}")
    
    pg_conn.commit()
    print("PostgreSQL數據清空完成")

def migrate_data(sqlite_data, pg_conn):
    """遷移數據到PostgreSQL"""
    print("正在遷移數據到PostgreSQL...")
    
    cursor = pg_conn.cursor()
    
    # 遷移順序（考慮外鍵依賴）
    migration_order = [
        'roles',
        'permissions', 
        'role_permissions',
        'users',
        'products',
        'orders',
        'order_items',
        'carts',
        'cart_items',
        'notifications',
        'notification_preferences',
        'backup_logs'
    ]
    
    for table in migration_order:
        if table not in sqlite_data or not sqlite_data[table]:
            print(f"  跳過空表格: {table}")
            continue
            
        print(f"  遷移表格: {table} ({len(sqlite_data[table])} 條記錄)")
        
        try:
            if table == 'users':
                migrate_users(sqlite_data[table], cursor)
            elif table == 'products':
                migrate_products(sqlite_data[table], cursor)
            elif table == 'orders':
                migrate_orders(sqlite_data[table], cursor)
            elif table == 'order_items':
                migrate_order_items(sqlite_data[table], cursor)
            elif table == 'carts':
                migrate_carts(sqlite_data[table], cursor)
            elif table == 'cart_items':
                migrate_cart_items(sqlite_data[table], cursor)
            elif table == 'notifications':
                migrate_notifications(sqlite_data[table], cursor)
            elif table == 'notification_preferences':
                migrate_notification_preferences(sqlite_data[table], cursor)
            elif table == 'backup_logs':
                migrate_backup_logs(sqlite_data[table], cursor)
            else:
                print(f"    警告：未定義遷移方法 {table}")
                
        except Exception as e:
            print(f"    錯誤：遷移表格 {table} 失敗: {e}")
            continue
    
    pg_conn.commit()
    print("數據遷移完成")

def migrate_users(users_data, cursor):
    """遷移用戶數據"""
    success_count = 0
    for user in users_data:
        try:
            # 處理布爾值轉換
            notification_email = bool(user.get('notification_email', 1))
            notification_line = bool(user.get('notification_line', 0))
            
            cursor.execute("""
                INSERT INTO users (id, username, email, password_hash, pharmacy_name, 
                                 phone, line_user_id, notification_email, notification_line,
                                 role_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    username = EXCLUDED.username,
                    email = EXCLUDED.email,
                    password_hash = EXCLUDED.password_hash,
                    pharmacy_name = EXCLUDED.pharmacy_name,
                    phone = EXCLUDED.phone,
                    line_user_id = EXCLUDED.line_user_id,
                    notification_email = EXCLUDED.notification_email,
                    notification_line = EXCLUDED.notification_line,
                    role_id = EXCLUDED.role_id,
                    updated_at = EXCLUDED.updated_at
            """, (
                user['id'], user['username'], user['email'], user['password_hash'],
                user['pharmacy_name'], user.get('phone'), user.get('line_user_id'),
                notification_email, notification_line,
                user.get('role_id', 3), user.get('created_at'), user.get('updated_at')
            ))
            success_count += 1
        except Exception as e:
            print(f"    警告：用戶 {user.get('username', user.get('id'))} 遷移失敗: {e}")
    
    print(f"    成功遷移 {success_count}/{len(users_data)} 個用戶")

def migrate_products(products_data, cursor):
    """遷移產品數據"""
    success_count = 0
    for product in products_data:
        try:
            # 處理布爾值轉換
            is_active = bool(product.get('is_active', 1))
            
            cursor.execute("""
                INSERT INTO products (id, nhi_code, name, english_name, ingredients,
                                    dosage_form, manufacturer, unit, unit_price,
                                    stock_quantity, description, is_active,
                                    created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    nhi_code = EXCLUDED.nhi_code,
                    name = EXCLUDED.name,
                    english_name = EXCLUDED.english_name,
                    ingredients = EXCLUDED.ingredients,
                    dosage_form = EXCLUDED.dosage_form,
                    manufacturer = EXCLUDED.manufacturer,
                    unit = EXCLUDED.unit,
                    unit_price = EXCLUDED.unit_price,
                    stock_quantity = EXCLUDED.stock_quantity,
                    description = EXCLUDED.description,
                    is_active = EXCLUDED.is_active,
                    updated_at = EXCLUDED.updated_at
            """, (
                product['id'], product['nhi_code'], product['name'],
                product.get('english_name'), product.get('ingredients'),
                product.get('dosage_form'), product['manufacturer'],
                product['unit'], product['unit_price'], product['stock_quantity'],
                product.get('description'), is_active,
                product.get('created_at'), product.get('updated_at')
            ))
            success_count += 1
        except Exception as e:
            print(f"    警告：產品 {product.get('name', product.get('id'))} 遷移失敗: {e}")
    
    print(f"    成功遷移 {success_count}/{len(products_data)} 個產品")

def migrate_orders(orders_data, cursor):
    """遷移訂單數據"""
    success_count = 0
    for order in orders_data:
        try:
            cursor.execute("""
                INSERT INTO orders (id, order_number, user_id, status, total_amount,
                                  notes, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    order_number = EXCLUDED.order_number,
                    user_id = EXCLUDED.user_id,
                    status = EXCLUDED.status,
                    total_amount = EXCLUDED.total_amount,
                    notes = EXCLUDED.notes,
                    updated_at = EXCLUDED.updated_at
            """, (
                order['id'], order['order_number'], order['user_id'],
                order['status'], order['total_amount'], order.get('notes'),
                order.get('created_at'), order.get('updated_at')
            ))
            success_count += 1
        except Exception as e:
            print(f"    警告：訂單 {order.get('order_number', order.get('id'))} 遷移失敗: {e}")
    
    print(f"    成功遷移 {success_count}/{len(orders_data)} 個訂單")

def migrate_order_items(order_items_data, cursor):
    """遷移訂單項目數據"""
    success_count = 0
    for item in order_items_data:
        try:
            cursor.execute("""
                INSERT INTO order_items (id, order_id, product_id, quantity,
                                       unit_price, total_price, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    order_id = EXCLUDED.order_id,
                    product_id = EXCLUDED.product_id,
                    quantity = EXCLUDED.quantity,
                    unit_price = EXCLUDED.unit_price,
                    total_price = EXCLUDED.total_price
            """, (
                item['id'], item['order_id'], item['product_id'],
                item['quantity'], item['unit_price'], item['total_price'],
                item.get('created_at')
            ))
            success_count += 1
        except Exception as e:
            print(f"    警告：訂單項目 {item.get('id')} 遷移失敗: {e}")
    
    print(f"    成功遷移 {success_count}/{len(order_items_data)} 個訂單項目")

def migrate_carts(carts_data, cursor):
    """遷移購物車數據"""
    success_count = 0
    for cart in carts_data:
        try:
            cursor.execute("""
                INSERT INTO carts (id, user_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    user_id = EXCLUDED.user_id,
                    updated_at = EXCLUDED.updated_at
            """, (
                cart['id'], cart['user_id'],
                cart.get('created_at'), cart.get('updated_at')
            ))
            success_count += 1
        except Exception as e:
            print(f"    警告：購物車 {cart.get('id')} 遷移失敗: {e}")
    
    print(f"    成功遷移 {success_count}/{len(carts_data)} 個購物車")

def migrate_cart_items(cart_items_data, cursor):
    """遷移購物車項目數據"""
    success_count = 0
    for item in cart_items_data:
        try:
            cursor.execute("""
                INSERT INTO cart_items (id, cart_id, product_id, quantity,
                                      unit_price, subtotal, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    cart_id = EXCLUDED.cart_id,
                    product_id = EXCLUDED.product_id,
                    quantity = EXCLUDED.quantity,
                    unit_price = EXCLUDED.unit_price,
                    subtotal = EXCLUDED.subtotal,
                    updated_at = EXCLUDED.updated_at
            """, (
                item['id'], item['cart_id'], item['product_id'],
                item['quantity'], item['unit_price'], item['subtotal'],
                item.get('created_at'), item.get('updated_at')
            ))
            success_count += 1
        except Exception as e:
            print(f"    警告：購物車項目 {item.get('id')} 遷移失敗: {e}")
    
    print(f"    成功遷移 {success_count}/{len(cart_items_data)} 個購物車項目")

def migrate_notifications(notifications_data, cursor):
    """遷移通知數據"""
    success_count = 0
    for notification in notifications_data:
        try:
            cursor.execute("""
                INSERT INTO notifications (id, user_id, type, title, message,
                                         status, sent_at, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    user_id = EXCLUDED.user_id,
                    type = EXCLUDED.type,
                    title = EXCLUDED.title,
                    message = EXCLUDED.message,
                    status = EXCLUDED.status,
                    sent_at = EXCLUDED.sent_at
            """, (
                notification['id'], notification['user_id'], notification['type'],
                notification['title'], notification['message'], notification['status'],
                notification.get('sent_at'), notification.get('created_at')
            ))
            success_count += 1
        except Exception as e:
            print(f"    警告：通知 {notification.get('id')} 遷移失敗: {e}")
    
    print(f"    成功遷移 {success_count}/{len(notifications_data)} 個通知")

def migrate_notification_preferences(prefs_data, cursor):
    """遷移通知偏好數據"""
    success_count = 0
    for pref in prefs_data:
        try:
            # 處理布爾值轉換
            email_enabled = bool(pref.get('email_enabled', 1))
            line_enabled = bool(pref.get('line_enabled', 0))
            
            cursor.execute("""
                INSERT INTO notification_preferences (id, user_id, email_enabled,
                                                    line_enabled, line_user_id,
                                                    created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    user_id = EXCLUDED.user_id,
                    email_enabled = EXCLUDED.email_enabled,
                    line_enabled = EXCLUDED.line_enabled,
                    line_user_id = EXCLUDED.line_user_id,
                    updated_at = EXCLUDED.updated_at
            """, (
                pref['id'], pref['user_id'], email_enabled,
                line_enabled, pref.get('line_user_id'),
                pref.get('created_at'), pref.get('updated_at')
            ))
            success_count += 1
        except Exception as e:
            print(f"    警告：通知偏好 {pref.get('id')} 遷移失敗: {e}")
    
    print(f"    成功遷移 {success_count}/{len(prefs_data)} 個通知偏好")

def migrate_backup_logs(backup_logs_data, cursor):
    """遷移備份日誌數據"""
    success_count = 0
    for log in backup_logs_data:
        try:
            # 處理布爾值轉換
            uploaded_to_gcp = bool(log.get('uploaded_to_gcp', 0))
            
            cursor.execute("""
                INSERT INTO backup_logs (id, backup_file, file_size, status,
                                       uploaded_to_gcp, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    backup_file = EXCLUDED.backup_file,
                    file_size = EXCLUDED.file_size,
                    status = EXCLUDED.status,
                    uploaded_to_gcp = EXCLUDED.uploaded_to_gcp
            """, (
                log['id'], log['backup_file'], log['file_size'],
                log['status'], uploaded_to_gcp, log.get('created_at')
            ))
            success_count += 1
        except Exception as e:
            print(f"    警告：備份日誌 {log.get('id')} 遷移失敗: {e}")
    
    print(f"    成功遷移 {success_count}/{len(backup_logs_data)} 個備份日誌")

def fix_sequences(pg_conn):
    """修復PostgreSQL序列"""
    print("正在修復PostgreSQL序列...")
    
    cursor = pg_conn.cursor()
    
    tables_with_sequences = [
        'users', 'products', 'orders', 'order_items', 'carts', 'cart_items',
        'notifications', 'notification_preferences', 'backup_logs', 'roles',
        'permissions', 'role_permissions'
    ]
    
    for table in tables_with_sequences:
        try:
            cursor.execute(f"""
                SELECT setval(pg_get_serial_sequence('{table}', 'id'), 
                             COALESCE((SELECT MAX(id) FROM {table}), 1), false);
            """)
            print(f"  修復序列: {table}")
        except Exception as e:
            print(f"  警告：無法修復序列 {table}: {e}")
    
    pg_conn.commit()
    print("序列修復完成")

def verify_migration(sqlite_conn, pg_conn):
    """驗證遷移結果"""
    print("正在驗證遷移結果...")
    
    sqlite_cursor = sqlite_conn.cursor()
    pg_cursor = pg_conn.cursor()
    
    tables_to_verify = ['users', 'products', 'orders', 'order_items', 'carts', 'cart_items']
    
    for table in tables_to_verify:
        try:
            # SQLite計數
            sqlite_cursor.execute(f"SELECT COUNT(*) FROM {table}")
            sqlite_count = sqlite_cursor.fetchone()[0]
            
            # PostgreSQL計數
            pg_cursor.execute(f"SELECT COUNT(*) FROM {table}")
            pg_count = pg_cursor.fetchone()[0]
            
            status = "✓" if sqlite_count == pg_count else "✗"
            print(f"  {table}: SQLite={sqlite_count}, PostgreSQL={pg_count} {status}")
            
        except Exception as e:
            print(f"  錯誤：無法驗證表格 {table}: {e}")

def main():
    print("=== SQLite to Neon DB 遷移工具 ===")
    print()
    
    # 獲取數據庫連接信息
    database_url = get_database_urls()
    
    # 連接數據庫
    print("正在連接數據庫...")
    sqlite_conn = connect_sqlite()
    if not sqlite_conn:
        return
    
    pg_conn = connect_postgresql(database_url)
    if not pg_conn:
        sqlite_conn.close()
        return
    
    try:
        # 1. 備份SQLite數據
        sqlite_data = backup_sqlite_data(sqlite_conn)
        
        # 2. 運行PostgreSQL遷移
        run_migrations(pg_conn)
        
        # 3. 清空PostgreSQL數據
        clear_postgresql_data(pg_conn)
        
        # 4. 遷移數據
        migrate_data(sqlite_data, pg_conn)
        
        # 5. 修復序列
        fix_sequences(pg_conn)
        
        # 6. 驗證遷移
        verify_migration(sqlite_conn, pg_conn)
        
        print()
        print("=== 遷移完成 ===")
        print("你的數據已成功從SQLite遷移到Neon DB！")
        print("請測試你的應用程序以確保一切正常運行。")
        
    except Exception as e:
        print(f"遷移過程中發生錯誤: {e}")
        pg_conn.rollback()
    finally:
        sqlite_conn.close()
        pg_conn.close()

if __name__ == "__main__":
    main()