#!/usr/bin/env python3
import pandas as pd
import sys

def process_1_2_excel():
    # 讀取Excel檔案
    print("正在讀取 1_2.xlsx...")
    df = pd.read_excel('1_2.xlsx')
    original_count = len(df)
    print(f"原始資料總數: {original_count}")
    
    # C欄位 (索引2)
    c_col = df.columns[2]
    print(f"C欄位名稱: {c_col}")
    
    # 統計C欄位=0的記錄數
    zero_count = len(df[df.iloc[:, 2] == 0])
    print(f"C欄位=0的記錄數: {zero_count}")
    
    # 刪除C欄位=0的記錄
    filtered_df = df[df.iloc[:, 2] != 0].copy()
    final_count = len(filtered_df)
    
    print(f"處理後資料總數: {final_count}")
    print(f"刪除的記錄數: {zero_count}")
    
    # 儲存結果
    output_file = '1_2_filtered.xlsx'
    print(f"正在儲存到 {output_file}...")
    filtered_df.to_excel(output_file, index=False)
    
    print("處理完成！")
    print(f"原始檔案: 1_2.xlsx ({original_count} 筆記錄)")
    print(f"處理後檔案: {output_file} ({final_count} 筆記錄)")
    
    # 顯示處理後C欄位的統計
    print("\n處理後C欄位統計:")
    print(filtered_df.iloc[:, 2].value_counts().head(10))

if __name__ == "__main__":
    try:
        process_1_2_excel()
    except Exception as e:
        print(f"錯誤: {e}")
        sys.exit(1)