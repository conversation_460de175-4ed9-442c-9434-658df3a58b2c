#!/bin/bash

echo "🧪 使用正確測試用戶測試結帳功能..."

# 設定環境變數
export JWT_SECRET="my-super-secret-jwt-key-for-testing-2024"
export DATABASE_URL="postgresql://arguskao:<EMAIL>:26257/happyorder?sslmode=require"

echo "✅ 環境變數已設定"

# 測試伺服器狀態
echo "🏥 檢查伺服器狀態..."
HEALTH_RESPONSE=$(curl -s http://localhost:8080/health)
if echo $HEALTH_RESPONSE | grep -q '"status":"healthy"'; then
    echo "✅ 伺服器正常運行"
else
    echo "❌ 伺服器狀態異常"
    echo $HEALTH_RESPONSE
    exit 1
fi

# 測試產品 API
echo "📦 檢查產品資料..."
PRODUCTS_RESPONSE=$(curl -s http://localhost:8080/api/products)
if echo $PRODUCTS_RESPONSE | grep -q '"success":true'; then
    echo "✅ 產品 API 正常"
else
    echo "❌ 產品 API 異常"
    echo $PRODUCTS_RESPONSE
    exit 1
fi

# 使用正確的測試用戶登入
echo "🔐 使用正確的測試用戶登入..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }')

echo "登入回應: $LOGIN_RESPONSE"

# 提取 token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
    echo "✅ 成功取得 token"
    
    # 測試購物車 API
    echo "🛒 測試購物車 API..."
    CART_RESPONSE=$(curl -s -X GET http://localhost:8080/api/cart \
      -H "Authorization: Bearer $TOKEN")
    
    echo "購物車回應: $CART_RESPONSE"
    
    # 添加商品到購物車
    echo "➕ 添加商品到購物車..."
    ADD_CART_RESPONSE=$(curl -s -X POST http://localhost:8080/api/cart \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d '{
        "product_id": 32,
        "quantity": 2
      }')
    
    echo "添加購物車回應: $ADD_CART_RESPONSE"
    
    # 測試結帳
    echo "💳 測試結帳..."
    CHECKOUT_RESPONSE=$(curl -s -X POST http://localhost:8080/api/orders/cart \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d '{
        "notes": "測試訂單"
      }')
    
    echo "結帳回應: $CHECKOUT_RESPONSE"
    
    # 分析結果
    echo ""
    echo "📊 測試結果分析："
    echo "- 伺服器狀態: ✅ 正常"
    echo "- 產品 API: ✅ 正常"
    echo "- 用戶登入: $(if echo $LOGIN_RESPONSE | grep -q '"success":true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
    echo "- 購物車 API: $(if echo $CART_RESPONSE | grep -q '"success":true'; then echo '✅ 正常'; else echo '❌ 異常'; fi)"
    echo "- 添加購物車: $(if echo $ADD_CART_RESPONSE | grep -q '"success":true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
    echo "- 結帳功能: $(if echo $CHECKOUT_RESPONSE | grep -q '"success":true'; then echo '✅ 成功'; else echo '❌ 失敗'; fi)"
    
    if echo $CHECKOUT_RESPONSE | grep -q '"success":true'; then
        echo ""
        echo "🎉 結帳功能測試成功！"
        echo "🌐 您現在可以訪問 http://localhost:8080 來使用完整的結帳流程"
        echo ""
        echo "📋 使用說明："
        echo "1. 訪問 http://localhost:8080"
        echo "2. 使用以下帳號登入："
        echo "   - 用戶名: testuser"
        echo "   - 密碼: password123"
        echo "3. 瀏覽產品並添加到購物車"
        echo "4. 點擊結帳按鈕完成訂單"
    else
        echo ""
        echo "🔍 結帳失敗，錯誤信息："
        echo $CHECKOUT_RESPONSE
        echo ""
        echo "💡 可能的解決方案："
        echo "1. 檢查資料庫表結構是否完整"
        echo "2. 確認用戶權限設定"
        echo "3. 檢查購物車和訂單相關的表格"
        echo "4. 查看伺服器日誌中的詳細錯誤信息"
    fi
    
else
    echo "❌ 無法取得有效的 token"
    echo ""
    echo "🔍 登入失敗，可能的原因："
    echo "1. 用戶密碼不正確"
    echo "2. 資料庫中的用戶資料有問題"
    echo "3. JWT 配置問題"
    echo ""
    echo "💡 建議："
    echo "1. 檢查資料庫中的用戶資料"
    echo "2. 確認 testuser 用戶是否存在"
    echo "3. 檢查 JWT_SECRET 設定"
    echo "4. 查看伺服器日誌中的詳細錯誤信息"
fi

echo ""
echo "🎯 測試完成！" 