#!/usr/bin/env python3
import psycopg2
import os
from urllib.parse import urlparse

# 從環境變量或配置文件獲取數據庫 URL
DATABASE_URL = "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require"

def run_migration():
    print("開始執行 NHI 價格表遷移...")
    
    # 讀取遷移腳本
    with open('migrations/20250808000001_create_nhi_prices_table.sql', 'r', encoding='utf-8') as f:
        migration_sql = f.read()
    
    try:
        # 連接數據庫
        conn = psycopg2.connect(DATABASE_URL)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # 分段執行 SQL
        statements = [s.strip() for s in migration_sql.split(';') if s.strip()]
        
        for i, statement in enumerate(statements):
            if statement:
                print(f"執行語句 {i+1}: {statement[:50]}...")
                try:
                    cursor.execute(statement)
                    print(f"✅ 語句 {i+1} 執行成功")
                except Exception as e:
                    print(f"❌ 語句 {i+1} 執行失敗: {e}")
                    # 某些語句失敗是正常的（比如表已存在）
                    continue
        
        # 檢查結果
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_name = 'nhi_prices'")
        if cursor.fetchone():
            print("✅ nhi_prices 表創建成功")
        else:
            print("❌ nhi_prices 表未找到")
        
        # 檢查 products 表是否有 nhi_price 欄位
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'products' AND column_name = 'nhi_price'
        """)
        if cursor.fetchone():
            print("✅ products 表已添加 nhi_price 欄位")
        else:
            print("❌ products 表缺少 nhi_price 欄位")
        
        # 檢查數據遷移結果
        cursor.execute("SELECT COUNT(*) FROM nhi_prices")
        count = cursor.fetchone()[0]
        print(f"✅ nhi_prices 表中有 {count} 筆記錄")
        
        cursor.close()
        conn.close()
        print("✅ 遷移執行完成")
        
    except Exception as e:
        print(f"❌ 遷移執行失敗: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = run_migration()
    if success:
        print("\n🎉 NHI 價格表遷移成功完成！")
    else:
        print("\n❌ 遷移失敗，請檢查錯誤信息")