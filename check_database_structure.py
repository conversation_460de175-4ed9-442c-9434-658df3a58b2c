#!/usr/bin/env python3
"""
檢查資料庫結構的腳本
"""
import os
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

def main():
    # 載入環境變數
    load_dotenv()
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ 找不到 DATABASE_URL 環境變數")
        return
    
    try:
        # 連接資料庫
        print("🔗 正在連接資料庫...")
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # 查看所有表格
        print("\n📋 資料庫中的所有表格:")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()
        
        for table in tables:
            print(f"  - {table['table_name']}")
        
        # 檢查 products 表的結構
        print("\n🏷️  Products 表的欄位結構:")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'products' 
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        if columns:
            print("欄位名稱".ljust(20) + "資料類型".ljust(20) + "可為空".ljust(10) + "預設值")
            print("-" * 70)
            for col in columns:
                nullable = "是" if col['is_nullable'] == 'YES' else "否"
                default = col['column_default'] or "無"
                print(f"{col['column_name']:<20}{col['data_type']:<20}{nullable:<10}{default}")
        else:
            print("❌ 找不到 products 表")
        
        # 檢查是否有 nhi_price 欄位
        print(f"\n🔍 檢查 products 表是否有 nhi_price 欄位:")
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'products' AND column_name = 'nhi_price';
        """)
        nhi_price_column = cursor.fetchone()
        
        if nhi_price_column:
            print("✅ products 表確實有 nhi_price 欄位")
        else:
            print("❌ products 表沒有 nhi_price 欄位")
        
        # 檢查 nhi_prices 表是否存在
        print(f"\n🔍 檢查是否有 nhi_prices 表:")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = 'nhi_prices';
        """)
        nhi_prices_table = cursor.fetchone()
        
        if nhi_prices_table:
            print("✅ 找到 nhi_prices 表")
            
            # 查看 nhi_prices 表結構
            print("\n🏷️  nhi_prices 表的欄位結構:")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'nhi_prices' 
                ORDER BY ordinal_position;
            """)
            nhi_columns = cursor.fetchall()
            
            print("欄位名稱".ljust(20) + "資料類型".ljust(20) + "可為空".ljust(10) + "預設值")
            print("-" * 70)
            for col in nhi_columns:
                nullable = "是" if col['is_nullable'] == 'YES' else "否"
                default = col['column_default'] or "無"
                print(f"{col['column_name']:<20}{col['data_type']:<20}{nullable:<10}{default}")
        else:
            print("❌ 找不到 nhi_prices 表")
        
        # 檢查外鍵約束
        print(f"\n🔗 檢查 products 表的外鍵約束:")
        cursor.execute("""
            SELECT 
                tc.constraint_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND tc.table_name = 'products';
        """)
        foreign_keys = cursor.fetchall()
        
        if foreign_keys:
            for fk in foreign_keys:
                print(f"  - {fk['column_name']} -> {fk['foreign_table_name']}.{fk['foreign_column_name']}")
        else:
            print("  沒有找到外鍵約束")
        
        # 查看一些範例資料
        print(f"\n📊 Products 表的前5筆資料:")
        cursor.execute("SELECT * FROM products LIMIT 5;")
        sample_products = cursor.fetchall()
        
        if sample_products:
            # 顯示欄位名稱
            if sample_products:
                columns = list(sample_products[0].keys())
                print("  " + " | ".join(col[:15] for col in columns))
                print("  " + "-" * (len(columns) * 17))
                
                for product in sample_products:
                    row = []
                    for col in columns:
                        value = str(product[col]) if product[col] is not None else "NULL"
                        row.append(value[:15])
                    print("  " + " | ".join(row))
        else:
            print("  表格中沒有資料")
        
        cursor.close()
        conn.close()
        print("\n✅ 資料庫檢查完成")
        
    except Exception as e:
        print(f"❌ 連接資料庫時發生錯誤: {e}")

if __name__ == "__main__":
    main()