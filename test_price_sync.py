#!/usr/bin/env python3
import psycopg2
import requests
import json

DATABASE_URL = "postgresql://seo1515_owner:<EMAIL>/seo1515?sslmode=require"
API_BASE = "http://localhost:8080"

def test_price_synchronization():
    print("🔍 測試健保價格同步功能...")
    
    try:
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # 1. 找一個測試用的健保代碼
        print("\n📋 尋找測試數據...")
        cursor.execute("SELECT nhi_code, nhi_price, selling_price FROM nhi_prices LIMIT 1")
        test_record = cursor.fetchone()
        
        if not test_record:
            print("   ❌ 沒有找到測試數據")
            return False
            
        test_nhi_code, original_nhi_price, original_selling_price = test_record
        print(f"   測試健保代碼: {test_nhi_code}")
        print(f"   原始健保價: {original_nhi_price}")
        print(f"   原始賣價: {original_selling_price}")
        
        # 2. 檢查對應的產品
        cursor.execute("SELECT id, name, selling_price FROM products WHERE nhi_code = %s", (test_nhi_code,))
        product_records = cursor.fetchall()
        
        if not product_records:
            print("   ⚠️  沒有找到使用此健保代碼的產品")
            return False
            
        print(f"   找到 {len(product_records)} 個使用此健保代碼的產品")
        for product_id, name, unit_price in product_records:
            print(f"     - {name}: unit_price = {unit_price}")
        
        # 3. 透過 API 查詢產品價格（查看是否已經顯示正確價格）
        print("\n📡 透過 API 查詢產品價格...")
        try:
            response = requests.get(f"{API_BASE}/api/products", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and isinstance(data['data'], list):
                    # 找到我們的測試產品
                    test_product = None
                    for product in data['data']:
                        if product.get('nhi_code') == test_nhi_code:
                            test_product = product
                            break
                    
                    if test_product:
                        api_price = test_product.get('unit_price')
                        print(f"   API 返回的價格: {api_price}")
                        print(f"   健保價格表中的賣價: {original_selling_price}")
                        
                        if str(api_price) == str(original_selling_price):
                            print("   ✅ API 價格與健保賣價一致")
                        else:
                            print("   ❌ API 價格與健保賣價不一致")
                    else:
                        print("   ⚠️  在 API 回應中沒有找到測試產品")
                else:
                    print("   ❌ API 回應格式不正確")
            else:
                print(f"   ❌ API 請求失敗: {response.status_code}")
                
        except Exception as e:
            print(f"   ⚠️  API 請求錯誤: {e}")
        
        # 4. 測試價格更新功能
        print("\n💰 測試價格更新功能...")
        new_selling_price = float(original_selling_price) * 1.1  # 漲價10%
        
        print(f"   將賣價從 {original_selling_price} 更新為 {new_selling_price:.2f}")
        
        # 更新健保賣價
        cursor.execute("""
            UPDATE nhi_prices 
            SET selling_price = %s, updated_at = CURRENT_TIMESTAMP
            WHERE nhi_code = %s
        """, (new_selling_price, test_nhi_code))
        
        print(f"   ✅ 已更新健保價格表中的賣價")
        
        # 5. 再次透過 API 查詢，看看是否反映了新價格
        print("\n🔄 驗證價格更新是否生效...")
        try:
            response = requests.get(f"{API_BASE}/api/products", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and isinstance(data['data'], list):
                    test_product = None
                    for product in data['data']:
                        if product.get('nhi_code') == test_nhi_code:
                            test_product = product
                            break
                    
                    if test_product:
                        api_price_after_update = test_product.get('unit_price')
                        print(f"   更新後 API 返回的價格: {api_price_after_update}")
                        print(f"   期望的新價格: {new_selling_price:.2f}")
                        
                        if abs(float(api_price_after_update) - new_selling_price) < 0.01:
                            print("   🎉 價格同步功能正常工作！")
                            success = True
                        else:
                            print("   ❌ 價格沒有正確同步")
                            success = False
                    else:
                        print("   ❌ 沒有找到測試產品")
                        success = False
                else:
                    print("   ❌ API 回應格式錯誤")
                    success = False
            else:
                print(f"   ❌ API 請求失敗: {response.status_code}")
                success = False
        except Exception as e:
            print(f"   ❌ API 請求錯誤: {e}")
            success = False
        
        # 6. 恢復原始價格
        print("\n🔄 恢復原始價格...")
        cursor.execute("""
            UPDATE nhi_prices 
            SET selling_price = %s, updated_at = CURRENT_TIMESTAMP
            WHERE nhi_code = %s
        """, (original_selling_price, test_nhi_code))
        print("   ✅ 已恢復原始價格")
        
        cursor.close()
        conn.close()
        
        return success
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    success = test_price_synchronization()
    print(f"\n{'🎊 測試成功!' if success else '❌ 測試失敗'}")
    if success:
        print("\n📈 健保價格同步功能驗證:")
        print("   ✅ 產品價格顯示來自 nhi_prices.selling_price")
        print("   ✅ 更新健保價格表會立即反映在 API 中")
        print("   ✅ 實現了集中化價格管理")