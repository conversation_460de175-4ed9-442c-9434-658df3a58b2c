# Rust
/target/
**/*.rs.bk
*.pdb

# Cargo
Cargo.lock

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment variables
.env
.env.local
.env.*.local

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Backup files
*.bak
*.backup

# Docker
.dockerignore

# GCP credentials
*.json
gcp-key.json
service-account.json

# Build artifacts
dist/
build/

# Coverage reports
tarpaulin-report.html
coverage/

# Flamegraph
flamegraph.svg
perf.data*

# Editor backup files
*~
*.orig
*.rej

# Node modules (if using any frontend tools)
node_modules/

# Python (if using any Python scripts)
__pycache__/
*.py[cod]
*$py.class

# Test artifacts
test-results/
*.profraw

# Local development files
temp_*
local_*
debug_*